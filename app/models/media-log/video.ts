/* eslint-disable new-cap */
import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

@Entity('medialog_video')
@Index(['kdt_id', 'user_id', 'host_id', 'timestamps_str'])
class VideoDetail {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  sdk_type: string;

  @Column({ nullable: true })
  app_key: string;

  @Column({ nullable: true })
  timestamps: number;

  @Column({ nullable: true })
  timestamps_str: string;

  @Column({ nullable: true })
  action: string;

  @Column({ nullable: true })
  action_type: string;

  @Column({ nullable: true })
  action_from: string;

  @Column({ nullable: true })
  action_data: string;

  @Column({ nullable: true })
  uuid: string;

  @Column({ nullable: true })
  os: string;

  @Column({ nullable: true })
  os_version: string;

  @Column({ nullable: true })
  kdt_id: string;

  @Column({ nullable: true })
  mobile: number;

  @Column({ nullable: true })
  user_id: string;

  @Column({ nullable: true })
  host_id: string;

  @Column({ nullable: true })
  location: string;

  @Column({ nullable: true })
  media_type: string;

  @Column({ nullable: true })
  ua: string;
}

export = VideoDetail;
