/* eslint-disable new-cap */
import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

@Entity('media_log_insight')
@Index(['cur_time', 'target', 'dimension', 'media_type', 'os', 'app_key'], { unique: true })
class InsightDetail {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  cur_time: string;

  @Column({ nullable: true })
  matrix_target: number;

  @Column({ nullable: true })
  matrix_all: number;

  @Column({ nullable: true })
  target: string;

  @Column({ nullable: true })
  dimension: string;

  @Column({ nullable: true })
  media_type: string;

  @Column({ nullable: true })
  os: string;

  @Column({ nullable: true })
  app_key: string;
}

export = InsightDetail;
