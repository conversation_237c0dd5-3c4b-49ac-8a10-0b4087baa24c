/* eslint-disable new-cap */
import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('medialog_alert_history')
class AlertRule {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({ nullable: true })
  rule_details: string;

  @Column({ nullable: true })
  rule: string;

  @Column({ nullable: true })
  rule_id: number;

  @Column({ nullable: true })
  rule_level: number; // 0:info 1:warn 2:error

  @Column({ nullable: true })
  app_key: string;

  @Column({ nullable: true })
  media_type: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt?: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt?: Date;
}

export = AlertRule;
