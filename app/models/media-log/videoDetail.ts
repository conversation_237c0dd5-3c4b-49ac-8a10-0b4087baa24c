/* eslint-disable new-cap */
import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

@Entity('medialog_video_detail_realtime')
@Index(['cur_time', 'target', 'dimension', 'os', 'app_key'], { unique: true })
class VideoDetail {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  cur_time: string;

  @Column({ nullable: true })
  matrix: number;

  @Column({ nullable: true })
  target: string;

  @Column({ nullable: true })
  dimension: string;

  @Column({ nullable: true })
  os: string;

  @Column({ nullable: true })
  app_key: string;
}

export = VideoDetail;
