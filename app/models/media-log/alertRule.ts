/* eslint-disable new-cap */
import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity('medialog_alert')
class AlertRule {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({ nullable: true })
  rule_name: string;

  @Column({ nullable: true })
  app_key: string;

  @Column({ nullable: true })
  rule_desc: number;

  @Column({ nullable: true })
  rule_level: number;

  @Column({ nullable: true })
  is_run: number;

  @Column({ nullable: true })
  media_type: string;

  @Column({ nullable: true })
  rules_type: number;

  @Column({ nullable: true })
  rules: string;

  @Column({ nullable: true })
  notice: string;
}

export = AlertRule;
