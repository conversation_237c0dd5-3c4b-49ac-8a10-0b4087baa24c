/* eslint-disable new-cap */
import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('share_article_category')
class ShareArticleCategory {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  category: string;

  @Column({ name: 'wechatwork_robot' })
  wechatworkRobot: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}

export = ShareArticleCategory;
