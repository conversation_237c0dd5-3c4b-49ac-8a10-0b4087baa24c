import path from 'path';
import assert from 'assert';
import glob from 'glob';
import _ from 'lodash';
import schedule, { JobCallback } from 'node-schedule';

interface IStaticSchedule {
  second?: number;
  minute?: number;
  hour?: number;
  date?: number;
  month?: number;
  year?: number;
  dayOfWeek?: number;
  [index: string]: any;
}

export abstract class Subscription {
  protected app: any;
  protected ctx: any;

  abstract schedule: IStaticSchedule;

  constructor(ctx: any) {
    this.ctx = ctx;
    this.app = ctx.app;
  }

  // @override
  abstract subscribe: JobCallback;

  protected setup() {
    const rule = new schedule.RecurrenceRule() as { [index: string]: any };
    Object.keys(this.schedule).reduce((prev, cur) => {
      prev[cur] = this.schedule[cur];
      return prev;
    }, rule);
    schedule.scheduleJob(rule, this.subscribe);
  }
}

export default function setupSchedule(ctx) {
  glob(path.resolve(process.env.ROOT_PATH || process.cwd(), 'app/schedules/**/*.+(ts|js)'), (err, entries) => {
    if (err) console.log(err.message);
    entries.forEach(entry => {
      const Subscription = require(entry).default;
      assert(_.isObject(Subscription), `${entry} must be an Object.`);
      new Subscription(ctx).setup();
    });
  });
}
