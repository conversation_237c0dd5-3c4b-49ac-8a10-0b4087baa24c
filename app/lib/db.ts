import UniformResource from '@youzan/uniform-resource';
import path from 'path';
import { createConnection } from 'typeorm';

const uniformResource = new UniformResource({
  runtimePath: path.join(__dirname, '../..', './run/uniform_resource'),
});

const mysqlConfig = uniformResource.getMysql('ebiz-fe-platform', 'ebizFe');
const { dbname, ip, password, port, username } = mysqlConfig;

createConnection({
  type: 'mysql',
  host: ip,
  port,
  username,
  password,
  database: dbname,
  entities: [__dirname + '/../models/**/*.{ts,js}'],
})
  .then(() => {
    // eslint-disable-next-line no-console
    console.log('Connection has been established successfully.');
  })
  .catch(err => {
    // eslint-disable-next-line no-console
    console.error('Unable to connect to the database:', err);
  });
