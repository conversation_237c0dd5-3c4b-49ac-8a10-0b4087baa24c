import path from 'path';
import glob from 'fast-glob';
import completeAssign from 'complete-assign';

function globItem(baseDir, patterns, callback) {
  let newPatterns;
  if (typeof patterns === 'string') {
    newPatterns = [`${baseDir}${patterns}`];
  } else if (Array.isArray(patterns)) {
    newPatterns = patterns.map(pattern => {
      return `${baseDir}${pattern}`;
    });
  }
  const arr = glob.sync(newPatterns, { dot: true });
  callback(arr.filter(i => !i.includes('.d.ts')));
}

function globDirs(patterns, callback) {
  const dirs = require(path.resolve(process.env.ROOT_PATH || process.cwd(), './run/dirs.json'));

  dirs.forEach(item => {
    globItem(item.baseDir, patterns, callback);
  });
}

function loadExtend(patterns, proto) {
  globDirs(patterns, entries => {
    entries.forEach(entry => {
      completeAssign(proto, require(entry));
    });
  });
}

function createMockContext(options = {}) {
  const mockContext = { app: options.app, headers: {} };
  const defaultContextPattern = '/app/extends/context.+(js|ts)';

  loadExtend(defaultContextPattern, mockContext);

  return mockContext;
}

export default createMockContext;
