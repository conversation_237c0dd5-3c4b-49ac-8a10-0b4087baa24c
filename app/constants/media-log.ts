import { transMap } from '@utils/index';

export const OPERATOR: Record<string, string> = {
  gte: '>=',
  lte: '<=',
};

export const TARGET = {
  ERROR: 'error',
  LOGIN_TIME: 'login-time',
  LOGIN: 'login', // 洞察班课登录频道质量
  QUALITY: 'quality', // 洞察音视频质量
  WATCH: 'watch', // 洞察音视频观看量
};

export const MEDIA_TYPE = {
  AUDIO: 'audio',
  VIDEO: 'video',
  CLASS: 'class',
};

// 实时趋势的 demension
export const DEMENSION_ERROR = {
  ALL: 'all',
  USER: 'user',
  HOST: 'host',
  KDTID: 'kdtid',
  USER_HOST: 'user_host',
};

export const DEMENSION_LOGIN_TIME = {
  ONE_S: '1',
  TWO_S: '2',
  THREE_S: '3',
  FOUR_S: '4',
  FIVE_S: '5',
  FIVE_MORE_S: '5+',
};

// 数据洞察的 demension
export const DEMENSION_LOGIN = {
  LOGIN_IN_FIVE_SECONDS: 'login-in-five-seconds',
  LOGIN_ERROR: 'login-error',
};

// 音视频质量
export const DEMENSION_QUALITY = {
  ERROR: 'error',
};

// 音视频质量
export const DEMENSION_WATCH = {
  WATCH_COUNT: 'watch-count',
};

export const DEMENSION_TEXT_MAP = {
  [TARGET.ERROR]: {
    [DEMENSION_ERROR.ALL]: 'all',
    [DEMENSION_ERROR.USER]: '用户',
    [DEMENSION_ERROR.HOST]: '资源',
    [DEMENSION_ERROR.KDTID]: '店铺',
    [DEMENSION_ERROR.USER_HOST]: '用户资源',
  },
  [TARGET.LOGIN_TIME]: {
    [DEMENSION_LOGIN_TIME.ONE_S]: '0s~1s',
    [DEMENSION_LOGIN_TIME.TWO_S]: '1s~2s',
    [DEMENSION_LOGIN_TIME.THREE_S]: '2s~3s',
    [DEMENSION_LOGIN_TIME.FOUR_S]: '3s~4s',
    [DEMENSION_LOGIN_TIME.FIVE_S]: '4s~5s',
    [DEMENSION_LOGIN_TIME.FIVE_MORE_S]: '5s+',
  },
  [TARGET.LOGIN]: {
    [DEMENSION_LOGIN.LOGIN_ERROR]: '登录房间成功率',
    [DEMENSION_LOGIN.LOGIN_IN_FIVE_SECONDS]: '5s 内登录房间成功率',
  },
  [TARGET.QUALITY]: {
    [DEMENSION_QUALITY.ERROR]: '异常率',
  },
  [TARGET.WATCH]: {
    [DEMENSION_WATCH.WATCH_COUNT]: '观看次数',
  },
};

// 报警
export const RULE_NAMECOUNT_SQL_SELECT = {
  [TARGET.ERROR]: 'count(*)',
  [TARGET.LOGIN_TIME]: 'avg(action_data)',
};

// 报警规则等级
export const RULE_LEVEL = {
  INFO: 1,
  WARN: 2,
  ERROR: 3,
};

export const RULE_LEVEL_TEXT = {
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error',
};

export const RULE_LEVEL_MAP = transMap(RULE_LEVEL, RULE_LEVEL_TEXT);

// 匹配规则
export const RULES_TYPE = {
  ALL: 1,
  PART: 2,
};

export const RULES_TYPE_TEXT = {
  ALL: '命中所有规则',
  PART: '命中任一规则',
};

export const RULES_TYPE_MAP = transMap(RULES_TYPE, RULES_TYPE_TEXT);

// 粒度，报警的粒度
export const GRANULARITY = {
  ALL: 'all',
  USER: 'user_id',
  HOST: 'host_id',
  KDTID: 'kdt_id',
  USER_HOST: 'user_id&host_id',
};

export const GRANULARITY_TEXT = {
  ALL: '全网',
  USER: '用户',
  HOST: '资源',
  KDTID: '店铺',
  USER_HOST: '用户资源',
};

export const GRANULARITY_TEXT_MAP = transMap(GRANULARITY, GRANULARITY_TEXT);

// 具体指标
export const TARGETV2: Record<string, Record<string, any>> = {
  [MEDIA_TYPE.AUDIO]: {
    error: 'error',
  },
  [MEDIA_TYPE.VIDEO]: {
    error: 'error',
  },
  [MEDIA_TYPE.CLASS]: {
    error: 'error',
    loginTime: 'login-time',
  },
};

export const TARGETV2_TEXT = {
  [MEDIA_TYPE.AUDIO]: {
    error: '报错总数',
  },
  [MEDIA_TYPE.VIDEO]: {
    error: '报错总数',
  },
  [MEDIA_TYPE.CLASS]: {
    error: '登录房间失败总数',
    loginTime: '用户平均登录房间耗时',
  },
};

export const TARGETV2_TEXT_MAP = Object.values(MEDIA_TYPE).reduce<Record<string, any>>((prev, curr) => {
  prev[curr] = transMap(TARGETV2[curr], TARGETV2_TEXT[curr]);
  return prev;
}, {});

// 指标单位
export const TARGET_UNIT = {
  [TARGET.LOGIN_TIME]: '秒',
  [TARGET.ERROR]: '次',
};

// 规则匹配
export const RULE_COMPARE = {
  /* 小于等于 */
  lte: 'lte',
  /* 大于等于 */
  gte: 'gte',
};

export const RULE_COMPARE_TEXT = {
  /* 小于等于 */
  lte: '小于等于',
  /* 大于等于 */
  gte: '大于等于',
};

export const RULE_COMPARE_MAP = transMap(RULE_COMPARE, RULE_COMPARE_TEXT);

// 实时趋势图维度
export const DEMENSION = {
  USER: 'user',
  HOST: 'host',
  KDTID: 'kdtid',
  USER_HOST: 'user_host',
};

export const DEMENSION_TEXT = {
  USER: '包含用户数',
  HOST: '包含资源数',
  KDTID: '包含店铺数',
  USER_HOST: '包含用户资源数',
};

export const DEMENSION_MAP = transMap(DEMENSION, DEMENSION_TEXT);
