/* eslint-disable camelcase */
interface WechatWorkKeys {
  [propName: string]: string;
}

// 群机器人
export const groupRobots: WechatWorkKeys = {
  frontjinfeng: 'f73537cf-de2f-433c-82d6-1fc539913441',
  ebiz: '98ea5b7f-aad1-4be9-aa47-3321f3dc8370',
  test: 'f96298ff-928d-4fff-baf4-1328e9af427a',
  jinfengTest: 'c559240d-fce1-424e-a568-6489315174a5',
  // skynetAlert: '5ae5b47c-468c-4e62-a835-18f358c06e6b',
  skynetAlert: '54c1f476-0b1c-434f-ba7c-7d548c719d78',
  dadaTest: '54c1f476-0b1c-434f-ba7c-7d548c719d78',
};

// 公司id
export const corpid = 'wwce7053747d79c3b4';

// 应用通知
export const apps = {
  personMessage: {
    corpsecret: 'ViCCIRggeQWMzevOv0uuOs1ZkzJ13ZTfRI-LlxeTWL8',
    // eslint-disable-next-line @typescript-eslint/camelcase
    agent_id: '1000006',
  },
};
