import { Reader } from '@youzan/nsq-client';
import { CHANNEL, DEFAULT_HOST, DETAULT_MONITOR_TIME, ENV_HOST, TOPIC } from './constant';
import { getMessage } from '@utils/skynet-log-token';
import BaseService from '@services/base/BaseService';
import { retryApolloRequest } from '@services/api/skynet-log/utils';
import sendMessage from './send';
import { IApollConfig, IMsg } from './type';

class NsqMonitorLog extends BaseService {
  matchApollConfig: IApollConfig = {};
  monitorApplication: Record<string, Record<string, number>> = {};
  constructor(ctx: any) {
    super(ctx);
    this.start();
  }
  async getApollConfig() {
    return retryApolloRequest(3, () => {
      return this.ctx.apolloClient.getConfig({
        appId: 'ebiz-fe-platform',
        namespace: 'skynet-log',
        key: 'monitor-log-v2',
      });
    });
  }
  updateMonitorTime(id: number) {
    const monitorApplicationGroup = this.monitorApplication;
    if (!monitorApplicationGroup[id]) {
      monitorApplicationGroup[id] = {};
    }
    monitorApplicationGroup[id].time = Date.now();
  }
  validateMonitor(id: number, level: string) {
    // 未打开通知 || 配置的通知告警类型不符合
    const { enable, notifyType, notifyTime } = this.matchApollConfig;
    if (!enable || (notifyType && notifyType !== level)) return false;
    const monitorApplication = this.monitorApplication[id];
    // 第一次接到相关告警
    if (!monitorApplication) return true;
    const monitorTime = notifyTime || DETAULT_MONITOR_TIME;
    const preTime = monitorApplication.time;
    const diffTime = Date.now() - preTime;
    return diffTime >= monitorTime;
  }
  async start() {
    const env = process.env.NODE_ENV;
    const nsqlookupSeedAddr = env ? ENV_HOST[env] || DEFAULT_HOST : DEFAULT_HOST;
    const ropts = {
      // nsqlookup 种子节点地址
      nsqlookupSeedAddr,

      // 默认缓存刷新频率
      cacheRefreshFrequency: 5e3,
      // 缓存刷新指数退避最小值
      cacheRefreshBackOffMinInterval: 30e3,
      // 缓存刷新指数退避最大值
      cacheRefreshBackOffMaxInterval: 60e3,
    };
    const reader = new Reader(TOPIC, CHANNEL, null, ropts);
    await reader.connect();
    for await (const msg of reader) {
      try {
        const monitorConfig: IMsg = msg.json();
        const apollConfig = await this.getApollConfig();
        this.matchApollConfig = apollConfig[monitorConfig.owningApp] || {};
        const { monitorId, level } = monitorConfig;
        if (this.validateMonitor(monitorId, level)) {
          this.updateMonitorTime(monitorId);
          const skynetLogInfo = await getMessage(monitorId, apollConfig.logTime);
          sendMessage({ apollConfig: this.matchApollConfig, skynetLogInfo, monitorConfig });
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(error);
      }
      msg.finish();
    }
  }
}
export default NsqMonitorLog;
