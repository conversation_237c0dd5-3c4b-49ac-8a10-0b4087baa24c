export const TOPIC = 'topic_skynetv2_monitor_event';
export const CHANNEL = 'salesman_skynet_monitor_channel';
export const DEFAULT_HOST = 'sqs.s.qima-inc.com:4161';

/** nsq监听域名 */
export const ENV_HOST: {
  [key: string]: string;
} = {
  qa: 'sqs-qa.s.qima-inc.com:4161',
  pre: 'nsq-pre.s.qima-inc.com:4161',
  prod: DEFAULT_HOST,
};

/** 默认告警上报时间 */
export const DETAULT_MONITOR_TIME = 300000;
/** 运行环境 */
export const ENV = (process.env.NODE_ENV || 'prod') as 'qa' | 'prod';
/** marconi平台配置 */
export const MarconiConfig = {
  qa: {
    domain: 'http://marconi.qa.s.qima-inc.com',
    appId: '329e5',
    secret: '1bc774735bb446b9bdb4a7301ad63584',
  },
  prod: {
    domain: 'http://marconi-api.s.qima-inc.com',
    appId: '2d7b19',
    secret: '5437dffefec04d108efe128e10ce1a33',
  },
};
