import { baseAjax } from '@utils/ajax';
import { ISkynetMsg } from '@utils/skynet-log-token';
import { DETAULT_MONITOR_TIME, ENV, MarconiConfig } from './constant';
import { IApollConfig, IMsg } from './type';
import crypto from 'crypto';

interface IProps {
  apollConfig: IApollConfig;
  skynetLogInfo: ISkynetMsg;
  monitorConfig: IMsg;
}
const getMessageTemplate = ({ apollConfig, skynetLogInfo, monitorConfig }: IProps) => {
  const monitorTime = apollConfig.notifyTime || DETAULT_MONITOR_TIME;
  const minuteTime = monitorTime / 1000 / 60;
  return {
    config: {
      wide_screen_mode: true,
    },
    elements: [
      {
        tag: 'div',
        text: {
          content: '** 告警频率：**' + minuteTime + '分钟（可配置）',
          tag: 'lark_md',
        },
      },
      {
        tag: 'div',
        text: {
          content: '** 事件描述：**' + monitorConfig.detail,
          tag: 'lark_md',
        },
      },
      {
        tag: 'div',
        text: {
          content: `**日志详情：**[点击查看](${skynetLogInfo.logUrl})`,
          tag: 'lark_md',
        },
      },
      {
        tag: 'div',
        text: {
          content: `** 错误信息（近${apollConfig.logTime || 5}分钟）：**\n` + skynetLogInfo.message,
          tag: 'lark_md',
        },
      },
    ],
    header: {
      template: 'red',
      title: {
        content: `${monitorConfig.owningApp}应用告警：${monitorConfig.monitorName}`,
        tag: 'plain_text',
      },
    },
  };
};
const validateMessage = (msg: string) => !!msg;
const MARCONI_CONFIG = MarconiConfig[ENV];
const getHeaders = () => {
  const scrept = MARCONI_CONFIG.secret;
  const timestamp = Math.round(Date.now() / 1000);
  const signature = crypto
    .createHash('md5')
    .update(timestamp + scrept)
    .digest('hex');
  return {
    'custom-x-marconi-app-id': MARCONI_CONFIG.appId,
    'custom-x-marconi-app-secret': signature,
    'custom-x-marconi-timestamp': timestamp,
    'custom-x-marconi-company': 'YOUZAN',
  };
}
const sendMessage = ({ apollConfig, skynetLogInfo, monitorConfig }: IProps) => {
  if (!validateMessage(skynetLogInfo.message)) return;

  baseAjax({
    url: MARCONI_CONFIG.domain + '/proxy/lark/api' + apollConfig.webhooks,
    method: 'POST',
    headers: getHeaders(),
    data: { card: getMessageTemplate({ apollConfig, skynetLogInfo, monitorConfig }), msg_type: 'interactive' },
  })
    // eslint-disable-next-line no-console
    .then(res => console.log(res))
    // eslint-disable-next-line no-console
    .catch(err => console.log(err));
};
export default sendMessage;
