import { IYouzanFrameworkContext } from '@youzan/youzan-framework/definitions';

export = () => async (ctx: IYouzanFrameworkContext, next: any) => {
  ctx.res.setHeader('Access-Control-Allow-Credentials', 'true');
  // ctx.res.setHeader('Access-Control-Allow-Origin', '*');
  // ctx.res.setHeader('Access-Control-Allow-Methods', 'POST, GET, OPTIONS, PUT, DELETE');
  // ctx.res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  await next();
};
