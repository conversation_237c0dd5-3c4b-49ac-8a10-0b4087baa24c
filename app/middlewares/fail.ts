import { IYouzanFrameworkContext } from '@youzan/youzan-framework/definitions';

async function handleError(ctx: IYouzanFrameworkContext, error: any) {
  if (ctx.acceptJSON) {
    const { code = 9999, msg = error.message } = error.errorContent || {};
    ctx.body = { code, msg };
  } else {
    ctx.body = error;
  }
  return;
}

export = () => async (ctx: IYouzanFrameworkContext, next: any) => {
  try {
    await next();
  } catch (error) {
    await handleError(ctx, error);
  }
};
