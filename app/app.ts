import YouzanFramework from '@youzan/youzan-framework';
import createMockContext from './lib/create-mock-context';
// import skynetKafkaConsumer from './kafkas/skynet-log/consumer';
import setupSchedule from "@lib/Subscription";
import NsqMonitorLog from './nsqjs';
import 'reflect-metadata';

import locationCheck from './scripts/location-auto-check';
import jiraPushRun from './scripts/jira-push';
import npmPublish from './scripts/npm-upgrade';
import s300check from './scripts/S300check';

import '@lib/db';

const app = new YouzanFramework({
  ROOT_PATH: process.env.ROOT_PATH,
});

app.on('start', app => {
  const ctx = createMockContext({ app });
  setupSchedule(ctx);
  // skynetKafkaConsumer(ctx);
  new NsqMonitorLog(ctx);
  jiraPushRun(ctx);
  locationCheck(ctx);
  npmPublish(ctx);
  s300check();
});
