/**
 * 扩展 Koa Context 对象
 */
import { Sequelize } from 'sequelize';

const RDS = Symbol.for('Context#Rds');

module.exports = {
  get rds() {
    const mysqlConfig = this.uniformResource.getMysql('ebiz-fe-platform', 'ebizFe');
    const { dbname, ip, password, port, username } = mysqlConfig;
    if (!this[RDS]) {
      this[RDS] = new Sequelize(dbname, username, password, {
        host: ip,
        port,
        dialect: 'mysql',
        dialectOptions: {
          charset: 'utf8mb4',
        },
        timezone: '+08:00',
      });
    }

    return this[RDS];
  },
};
