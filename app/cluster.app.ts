import cluster from 'cluster';
// import { cpus } from 'os';

// const numCPUs = process.env._NUM_CPUS || cpus().length;

if (cluster.isMaster) {
  // Fork workers.
  // for (let i = 0; i < numCPUs; i++) {
  //   cluster.fork();
  // }
  cluster.fork();
  cluster.on('listening', (worker, address) => {
    // eslint-disable-next-line
    console.log('listening: worker ' + worker.process.pid + ', Address: ' + address.address + ":" + address.port);
  });

  cluster.on('online', worker => {
    // eslint-disable-next-line
    console.log('Worker ' + worker.process.pid + ' is online');
  });

  cluster.on('exit', (worker, code, signal) => {
    // eslint-disable-next-line
    console.log('Worker ' + worker.process.pid + ' died with code: ' + code + ', and signal: ' + signal);
    // eslint-disable-next-line
    console.log('Starting a new worker');
    cluster.fork();
  });
} else {
  require('./app.js');
}
