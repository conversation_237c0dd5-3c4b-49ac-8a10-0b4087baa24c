const fetch = require('node-fetch');

function getJiraData(data) {
  return fetch('https://jira.qima-inc.com/rest/issueNav/1/issueTable', {
    headers: {
      __amdmodulename: 'jira/issue/utils/xsrf-token-header',
      accept: '*/*',
      'accept-language': 'zh-CN,zh;q=0.9',
      'cache-control': 'no-cache',
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
      pragma: 'no-cache',
      'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"macOS"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-origin',
      'x-atlassian-token': 'no-check',
      'x-requested-with': 'XMLHttpRequest',
      cookie:
        'yz_log_uuid=110d1adf-c7ad-775c-eb43-d0e0ff12725d; yz_log_ftime=1677229226699; gr_user_id=3f71a43b-d8e2-4c0d-8473-27e19d928709; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22187a2c7b201194-0700279bf30566-1d525634-1296000-187a2c7b202210c%22%2C%22%24device_id%22%3A%22187a2c7b201194-0700279bf30566-1d525634-1296000-187a2c7b202210c%22%2C%22props%22%3A%7B%22%24latest_referrer%22%3A%22%22%2C%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer_host%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfYW5vbnltb3VzX2lkIjoiMTg3YTJjN2IyMDExOTQtMDcwMDI3OWJmMzA1NjYtMWQ1MjU2MzQtMTI5NjAwMC0xODdhMmM3YjIwMjIxMGMiLCIkaWRlbnRpdHlfY29va2llX2lkIjoiMTg4ZjY1OGZiMTVkN2QtMDgxNTA0NmNiODkyYTU4LTFiNTI1NjM0LTEyOTYwMDAtMTg4ZjY1OGZiMTYxMzBiIn0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%7D; DO_CHECK_YOU_VERSION=1; KDTSESSIONID=YZ1187431894816280576YZwrvj2QWO; cas_username=xujiazheng; access_user=16184_1; jira.editor.user.mode=wysiwyg; XIAOLV_SESSION_ID_prod=5Pu8MV8l0M4Wgf1JGxNfIwLCMTEpYY6Y-wkdY1Vq; oa.koa.sid=IdNyfRkTuKyKyAsZxHI2tjxfAu1J1kx8; oa.koa.sid.sig=jFMI5Tz4zmNXiR1pRELq4bsecOI; JSESSIONID=14B6981119B7E779B2A86724637EEE7E; atlassian.xsrf.token=BLB1-PN5V-28RZ-GHSI_f88c7cc61971443f8caf995b78dc7b6957ad1990_lin; loc_dfp=977d8570047be2f79099f9a6569d983f; dfp=1a3487c82daeb4726ec0661c87fb6a4d; cas=3d825881297b2d945f4067d9d1794e2b2599edba466ed95f1a534ae167db507856; TSID=aa0915ad53864c3fa0930605023d460d; iamp.sid=63KzPZfnkIGpru4jNymMT6VR7utDleMqNQkFA8PztotvHBdUSpE4R7LJ/FEHSUfFREnPZ6EMxWE5CnSwscwoJDaTDNFD70uUxKOryHTWiaDJtPSLgxThc6ELveW+sPoEg5AxitZ6vSf4gzpvCPpCffB8V14gmP/3COkDbGU3s1gdUts7uSS0OPzGwPASGtVxKFP96q8QG9+GogHn8r4RKg==; yz_log_seqb=1704431223943; yz_log_seqn=21',
      Referer: 'https://jira.qima-inc.com/issues/?jql=status%20%3D%20%22IN%20PROGRESS%22%20AND%20text%20~%20%22%E6%89%B9%E9%87%8F%E6%89%93%E5%8D%95%22',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
    },
    body: `startIndex=0&jql=status+in+(OPEN%2C+%22IN+PROGRESS%22%2C+REOPENED)+AND+text+~+%22${encodeURIComponent(data.keywords || '')}%22+AND+created+%3E%3D+-${data.day || 2}d&layoutKey=split-view`,
    method: 'POST',
  })
    .then(res => res.json())
    .then(res => {
      return res.issueTable.table;
    });
}

module.exports = {
  getJiraData,
};
