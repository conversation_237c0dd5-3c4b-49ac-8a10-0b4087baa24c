/**
 * 定制检测相关 jira推送到飞书， 每天 9 点到晚上 9 点每整点计算一遍。
 */
import schedule from 'node-schedule';

import { notify } from './webhook';
const { getJiraData } = require('./api');

function getJiraPushConfig() {
  const apolloClient = global.getRuntime().apolloClient;

  const result = apolloClient.getConfig({
    appId: 'ebiz-fe-platform',
    namespace: 'application',
    key: 'jiraPushConfig',
  });
  return result;
}

async function task(config) {
  const { webhook, ...query } = config;
  try {
    const jiraList = await getJiraData(query);
    const warnTips = (jiraList || [])
      .filter(item => item.key.includes('ONLINE'))
      .filter(item => item.summary.includes(query.keywords))
      .map(item => {
        return [
          {
            tag: 'a',
            text: item.summary,
            href: `https://jira.qima-inc.com/browse/${item.key}`,
          },
        ];
      });
    if (!warnTips.length) {
      warnTips.push([{
        tag: "text",
        text: '无相关jira',
      }])
    }
    const result = {
      msg_type: 'post',
      content: {
        post: {
          zh_cn: {
            title: `jira 提醒: {${query.keywords}}`,
            content: warnTips,
          },
        },
      },
    };
    notify(webhook, result);
  } catch (err) {
    notify(webhook, {
      msg_type: 'text',
      content: {
        text: `jira 提醒任务执行失败: ${(err && err.message) || ''}`,
      },
    });
  }
}

function main(ctx) {
  const rule = new schedule.RecurrenceRule();
  rule.hour = [9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21];
  rule.minute = [0, 30];
  if (process.env.NODE_ENV.toLocaleLowerCase() === 'qa') {
    schedule.scheduleJob(rule, () => {
      const jiraPushConfigList = getJiraPushConfig();
      jiraPushConfigList.forEach(task);
    });
  }
  // 注册路由，手动触发推送
  ctx.app.use(async (ctx, next) => {
    switch (ctx.path) {
      case '/jira/push': {
        const jiraPushConfigList = getJiraPushConfig();
        jiraPushConfigList.forEach(task);
        ctx.json(0, '', 'ok');
        break;
      }
      default:
        next();
    }
  });
}

export default main;
