// 统一接出服务请求

import url from 'url';
import { baseAjax } from '@utils/ajax';

const AMAP_HOST = 'restapi.amap.com';
const QQMAP_HOST = 'apis.map.qq.com';

const AMAP_YZC_KEYS = {
  qa: 'ebiz-fe-platform_589_1n0bcqpI',
  pre: 'ebiz-fe-platform_116_kSqx21IP',
  prod: 'ebiz-fe-platform_116_kSqx21IP',
};
const QQMAP_YZC_KEYS = {
  qa: 'ebiz-fe-platform_2196_neCiUhXm',
  pre: 'ebiz-fe-platform_2197_W4QRntrw',
  prod: 'ebiz-fe-platform_2197_W4QRntrw',
};

const YZC_ENV_HOST = {
  qa: 'proxy-static-qa.s.qima-inc.com',
  pre: 'proxy-static.s.qima-inc.com',
  prod: 'proxy-static.s.qima-inc.com',
};

const YZC_CONFIG = {
  [AMAP_HOST]: AMAP_YZC_KEYS,
  [QQMAP_HOST]: QQMAP_YZC_KEYS,
};

export default function request(options) {
  const env = process.env.NODE_ENV || 'prod';
  const urlParse = url.parse(options.url);
  const originHost = urlParse.hostname;
  const yzcConfig = YZC_CONFIG[originHost];
  // 匹配域名，更改请求参数
  if (yzcConfig) {
    urlParse.host = YZC_ENV_HOST[env];
    options.url = urlParse.format();
    options.headers = {
      ...options.headers,
      host: originHost,
      'yzc-token': yzcConfig[env],
    };
  }
  return baseAjax(options);
}
