import schedule from 'node-schedule';
import taskRun from './task';
import { notify } from './webhook';

function main(ctx) {
  const rule = new schedule.RecurrenceRule();
  rule.hour = [10, 14, 17];
  rule.minute = 5;

  if (process.env.NODE_ENV.toLocaleLowerCase() === 'prod') {
    schedule.scheduleJob(rule, () => {
      taskRun().then(res => {
        notify(res);
      });
    });
  }

  // 注册路由，手动触发推送
  ctx.app.use(async (ctx, next) => {
    switch (ctx.path) {
      case '/location/check': {
        taskRun().then(res => {
          notify(res);
          ctx.json(0, '', 'ok');
        });
        break;
      }
      default:
        next();
    }
  });
}

export default main;
