import { getLocationByTengxun, getLocationByGaode, getSkynetLog, getWeappVersion } from './data';
import { checkLocation, getSkynetLink } from './utils';

const poiNotFoundMsg = '未获取到poi';

// 获取需要检查的日志
function getLog() {
  return Promise.all([
    getWeappVersion(1)
      .then(res => `${res} poiType`)
      .then(getSkynetLog),
    getWeappVersion(0)
      .then(res => `${res} poiType`)
      .then(getSkynetLog),
    getSkynetLog('weixin && poiType'),
  ]).then(([res1, res2, res3]) => {
    return [...res3.slice(0, 5), ...res1.slice(0, 3), ...res2.slice(0, 3)];
  });
}

// 检查日志的经纬度
async function check({ poiId, lon, lat, poiType }) {
  let location = null;
  if (+poiType === 1) {
    location = await getLocationByTengxun(poiId);
  }
  if (+poiType === 3) {
    location = await getLocationByGaode(poiId);
  }
  if (!location) {
    return poiNotFoundMsg;
  }
  const errMsg = checkLocation(location, lon, lat);
  if (errMsg) {
    // eslint-disable-next-line no-console
    console.log(location, lon, lat);
  }
  return errMsg ? `校验失败,${errMsg};` : '';
}

// 根据日志分析返回结果
async function getLogsCheckResult(logs) {
  const result = [];
  let isAllValid = true;
  for (let i = 0; i < logs.length; i++) {
    const poiItem = logs[i];
    const errMsg = await check(poiItem.args);
    const platform = +poiItem.args.poiType === 1 ? 'weapp' : 'h5';
    if (errMsg) {
      const errData = [
        {
          tag: 'text',
          text: `【样本${i + 1} (${platform})】${errMsg}！`,
        },
        {
          tag: 'a',
          text: `查看天网`,
          href: getSkynetLink(poiItem.tags._traceId),
        },
      ]
      // 没有找到poi不做艾特提醒
      if (errMsg !== poiNotFoundMsg) {
        isAllValid = false;
      }
      result.push(errData);
    } else {
      result.push([
        {
          tag: 'text',
          text: `【样本${i + 1} (${platform})】检查成功`,
        },
        {
          tag: 'a',
          text: `查看天网`,
          href: getSkynetLink(poiItem.tags._traceId),
        },
      ]);
    }
  }

  if (!isAllValid) {
    result.push([
      {
        tag: 'a',
        text: `点击查看手动校验经纬度的方法`,
        href: 'https://qima.feishu.cn/wiki/W05HwF7HSiqfGWkkBcIciMghnhf',
      },
    ]);
  }
  return {
    result,
    isAllValid,
  };
}

async function run() {
  try {
    const logs = await getLog();
    if (!logs.length) {
      // 本次没有捞到需要检查数据
      return {
        msg_type: 'text',
        content: {
          text: '本次没有拿到需要检查数据',
        },
      };
    }
    const checkResult = await getLogsCheckResult(logs);
    return {
      msg_type: 'post',
      mentioned_list: checkResult.isAllValid ? [] : ['xujiazheng'],
      content: {
        post: {
          zh_cn: {
            title: '经纬度巡检结果通知',
            content: checkResult.result,
          },
        },
      },
    };
  } catch (err) {
    return {
      msg_type: 'text',
      content: {
        text: `脚本出错：${err.message || '未知错误'}`,
      },
    };
  }
}

export default run;
