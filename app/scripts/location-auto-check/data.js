import baseAjax from './map-service';
import { getTimeRangeByNow } from './utils';

// 通过日志分析请求参数
function getArgs(msg) {
  let argsStr = '';
  msg.replace(/args\=(.+)\s\[kdtId/g, (str, p1) => {
    argsStr = p1;
  });
  return JSON.parse(argsStr)[0];
}
// 获取小程序版本，type： 1和0分别代表体验版，稳定版
export function getWeappVersion(type) {
  return baseAjax({
    url: 'https://mmp.prod.qima-inc.com/api/versions/list',
    method: 'POST',
    data: { versionType: type, shopType: 0, businessType: 1, accountType: 2 },
    contentType: 'application/json;charset=UTF-8',
    headers: {
      accept: 'application/json, text/plain, */*',
      'accept-language': 'zh-CN,zh;q=0.9',
      'cache-control': 'no-cache',
      'content-type': 'application/json',
      pragma: 'no-cache',
      'sec-ch-ua': '"Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"macOS"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-origin',
      cookie:
        'yz_log_uuid=110d1adf-c7ad-775c-eb43-d0e0ff12725d; yz_log_ftime=*************; cas_username=xujiazheng; access_user=16184_1; KDTSESSIONID=YZ1086022414826213376YZwAEYkKNC; TSID=ba4e2f57dbcf49b887a4faaa51c668c6; io=DoFx-KOAGvvgqBPLAOyx; cas=e274fef3222631bc135b628ae63d2810ff087de508f7d4f480558c0e7c270a5647; iamp.sid=63KzPZfnkIGpru4jNymMT6VR7utDleMqNQkFA8PztotvHBdUSpE4R7LJ/FEHSUfFREnPZ6EMxWE5CnSwscwoJDaTDNFD70uUxKOryHTWiaDJtPSLgxThc6ELveW+sPoEg5AxitZ6vSf4gzpvCPpCffB8V14gmP/3COkDbGU3s1gdUts7uSS0OPzGwPASGtVxKFP96q8QG9+GogHn8r4RKg==; weapp-manager.sid=UBnMcporDoqs3LCvs5NKxNiDrs+VhvJxwvmTzVithnKsAOgPjUqQLGEKRa3LeDton75r4XS5vQs+XJX5ZJ+fNdLCR7a4gPRno2r7wSGdBeBDCeGC6uZiequr32WJsWlUeGVja3f35guu/lRp/ac+IUXjBrMzc7idvKLdmSwrv6XAS+yd9SD0zDuaCvtPOxhFRcnWRl5u3DaXt/i0/Xuq0w==; weapp-manager.sid.sig=LtaHEp9mj_GZlfCwC9Kx9PYWctQ; XIAOLV_SESSION_ID_prod=MWbUg9j5CRtNL_lr4wLYxeoN_xpfWdKYHypzDdXj; loc_dfp=85651c3dd903e29a917e1553cde2fc6a; dfp=a8c4cda9dce56d282b08036696cf2651; yz_log_seqb=1681123936981; yz_log_seqn=92',
      Referer: 'https://mmp.prod.qima-inc.com/version/list',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
    },
  }).then(res => {
    const isTodayPublish = new Date(res[0].createdAt).getDate() === new Date().getDate();
    return res[+isTodayPublish].version;
  });
}

// 通过关键字查找天网日志
export function getSkynetLog(keywords) {
  const [startTime, endTime] = getTimeRangeByNow();
  return baseAjax({
    url: 'https://ops.qima-inc.com/v3/skynet/log/search/search',
    method: 'POST',
    data: {
      app: 'wsc-h5-trade',
      timestampBeginMs: startTime,
      timestampEndMs: endTime,
      levelArray: [],
      tagConditions: [['methodName', 'eq', 'add']],
      queryString: keywords,
      direction: 'ASC',
      after: null,
      limit: 8,
    },
    contentType: 'application/json;charset=UTF-8',
    headers: {
      accept: 'application/json, text/plain, */*',
      'accept-language': 'zh-CN,zh;q=0.9',
      authorization:
        'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWFsbmFtZSI6IuW-kOWYieatoyIsInVzZXJfaWQiOjE2MTg0LCJpc19hZG1pbiI6ZmFsc2UsImV4cCI6MTY4MTQ4OTk3MCwiZW1haWwiOiJ4dWppYXpoZW5nQHlvdXphbi5jb20iLCJ1c2VybmFtZSI6Inh1amlhemhlbmciLCJnZW5kZXIiOmZhbHNlLCJhbGlhc25hbWUiOiLlvpDlmInmraMiLCJrZXkiOiJ4dWppYXpoZW5nIiwibW9iaWxlIjoiMTgzOTc5NjgzMjYiLCJ0aW1lc3RhbXAiOjE2ODA0ODk5NzEsImlkIjoxNjE4NH0.4ZjFvwdzix-lBJP6y0N_sGtr9fOIs0u2p0ghCbY2WdQ',
      'cache-control': 'no-cache',
      'content-type': 'application/json;charset=UTF-8',
      pragma: 'no-cache',
      'sec-ch-ua': '"Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"macOS"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-origin',
      'x-yz-bu': 'main',
      'x-yz-env': 'prod',
      cookie:
        'yz_log_uuid=110d1adf-c7ad-775c-eb43-d0e0ff12725d; yz_log_ftime=*************; cas_username=xujiazheng; access_user=16184_1; authority=user; is_admin=false; username=xujiazheng; buId=1; RontgenEnv=prod; UnitWhiteList=java-demo,ump-manage,uic,ump-trade,sam,shop-prod,trade-rp,scrm-level,retail-scrm,shopcenter,scrm-behavior,retail-stock,retail-stock-scm,trade-invoice,ic,pay-gateway,scrm-api,retail-ump-calculation,shop-config,retail-pay,trade-plugin,retail-trade-cart,trade-dc,retail-shop,retail-ump,retail-trademanager,ump-asset,shop-center,scrm,yop,ump-deal,mall-trade-seller,marketing,retail-ofc-dispatcher,delivery,scrm-cmc,carmen-oauth,uic-user,yz-cardvoucher-biz,scrm-cmc-core,trade,scrm-credit,scrm-coc,trade-safeguard,trade-refund,retail-ofc,shop-configretail-scrm,price-center,cert,trade-detail,sc,retail-trade-core,trade-core,pay-gateway,pay-ucashier,ump-voucher-core,pay-assetcenter,pay-customer,bifrost-token-proxy,pay-gateway,pay-ucashier,pay-customer,pay-customercore,pay-merchant,pay-login,pay-assetcenter,pay-trading-core,pay-payment-core,pay-fund-channel,pay-acctrans,pay-microacctrans,pay-user,pay-usercore,pay-cashier,pay-cardvoucher,pay-cardvoucherop,pay-payment-recharge,bifrost-youzan-oauth,bifrost-youzan-gateway,paas-test-provider,paas-test-consumer,paas-test-node,yz7test-tool,bifrost-proxy,item-core; KDTSESSIONID=YZ1086022414826213376YZwAEYkKNC; loc_dfp=85651c3dd903e29a917e1553cde2fc6a; dfp=0bcc246fe27bf57e82b398c079bc2539; cas=49e2a0026de9cc9ba42bdeed76d697af50c4c3a83c9f80569a1832c21de29641541; TSID=ba4e2f57dbcf49b887a4faaa51c668c6; iamp.sid=63KzPZfnkIGpru4jNymMT6VR7utDleMqNQkFA8PztotvHBdUSpE4R7LJ/FEHSUfFREnPZ6EMxWE5CnSwscwoJDaTDNFD70uUxKOryHTWiaDJtPSLgxThc6ELveW+sPoEg5AxitZ6vSf4gzpvCPpCffB8V14gmP/3COkDbGU3s1gdUts7uSS0OPzGwPASGtVxKFP96q8QG9+GogHn8r4RKg==; XIAOLV_SESSION_ID_prod=LclT5NyTrqZDVZ7qSlCkegsgStaXDfgeOAEYDE0O; OPS_JWT_TOKEN=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6MTYxODQsInVzZXJfaWQiOjE2MTg0LCJpc19hZG1pbiI6ZmFsc2UsImV4cCI6MTY4MTUwMjM2NCwiZW1haWwiOiJ4dWppYXpoZW5nQHlvdXphbi5jb20iLCJnZW5kZXIiOmZhbHNlLCJyZWFsbmFtZSI6IuW-kOWYieatoyIsImFsaWFzbmFtZSI6IuW-kOWYieatoyIsInVzZXJuYW1lIjoieHVqaWF6aGVuZyIsImtleSI6Inh1amlhemhlbmciLCJ0aW1lc3RhbXAiOjE2ODA1MDIzNjUsIm1vYmlsZSI6IjE4Mzk3OTY4MzI2In0.p4tNu8iLR4i_b6T2eYW3TB-L0RhUJKwYoaZYinSx-YA; yz_log_seqb=1680502366208; yz_log_seqn=24',
      Referer: 'https://ops.qima-inc.com/v3/skynet/',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
    },
  }).then(res => {
    const logEvents = res.logEvents.map(vo => {
      return {
        ...vo,
        args: getArgs(vo.message),
      };
    });
    return logEvents.filter(vo => vo.args.type === 2);
  });
}
// 获取高德定位，返回经纬度
export function getLocationByGaode(id) {
  return baseAjax({
    url: 'http://restapi.amap.com/v3/place/detail',
    withCredentials: true,
    data: {
      id,
      key: '87ae1554fd8b15535917a666b2b172c3',
    },
  })
    .then(res => {
      const poi = res.pois && res.pois[0];
      if (!poi) {
        return null;
      }
      const [lng, lat] = poi.location.split(',');
      return {
        lng,
        lat,
      };
    })
    .catch(err => {
      throw new Error('高德api请求报错' + err.message);
    });
}

// 获取腾讯定位，返回经纬度
export function getLocationByTengxun(id) {
  return baseAjax({
    url: 'http://apis.map.qq.com/ws/place/v1/detail',
    withCredentials: true,
    data: {
      id,
      key: 'NYGBZ-ISSK4-IKGUN-FZASJ-XFEN3-RGF7R',
    },
  })
    .then(res => {
      const poi = res.data && res.data[0];
      if (!poi) {
        return null;
      }
      return poi.location;
    })
    .catch(err => {
      throw new Error('腾讯api请求报错' + err.message);
    });
}
