// 坐标转换
export function gcjToBaidu(lng, lat) {
  const delta = (Math.PI * 3000.0) / 180.0;
  const x = lng;
  const y = lat;
  const z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * delta);
  const theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * delta);
  lng = z * Math.cos(theta) + 0.0065;
  lat = z * Math.sin(theta) + 0.006;
  return { lng, lat };
}

// 距离计算
export function calcDistance(lat1, lon1, lat2, lon2, unit) {
  if (lat1 == lat2 && lon1 == lon2) {
    return 0;
  } else {
    const radlat1 = (Math.PI * lat1) / 180;
    const radlat2 = (Math.PI * lat2) / 180;
    const theta = lon1 - lon2;
    const radtheta = (Math.PI * theta) / 180;
    let dist = Math.sin(radlat1) * Math.sin(radlat2) + Math.cos(radlat1) * Math.cos(radlat2) * Math.cos(radtheta);
    if (dist > 1) {
      dist = 1;
    }
    dist = Math.acos(dist);
    dist = (dist * 180) / Math.PI;
    dist = dist * 60 * 1.1515;
    if (unit == 'K') {
      dist = dist * 1.609344;
    }
    if (unit == 'N') {
      dist = dist * 0.8684;
    }
    return dist;
  }
}

// 校验地址，获取的经纬度与系统经纬度进行匹配
export function checkLocation(gcjLocation, lng, lat) {
  const baiduLocation = gcjToBaidu(gcjLocation.lng, gcjLocation.lat);
  // 计算两点距离
  const distance = calcDistance(baiduLocation.lat, baiduLocation.lng, lat, lng, 'M');

  if (distance < 1) {
    return false;
  }
  return `系统经纬度：[${lng}, ${lat}]；对比经纬度：[${baiduLocation.lng},${baiduLocation.lat}]；请检查`;
}

// 获取当前的时间区间，1天
export function getTimeRangeByNow() {
  const now = Date.now();
  const before1Days = now - 24 * 3600 * 1000;
  return [before1Days, now];
}

// 拼接天网日志链接
export function getSkynetLink(traceId) {
  const [before1Days, now] = getTimeRangeByNow();
  return `https://ops.qima-inc.com/v3/skynet/#/main/prod/log/search/all-log?appName=wsc-h5-trade&order=ASC&tags=_traceId%3D${traceId}&timeRange=${before1Days}&timeRange=${now}`;
}
