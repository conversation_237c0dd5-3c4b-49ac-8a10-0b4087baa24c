const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 测试 yarn 路径查找逻辑
function testYarnPathFinding() {
  console.log('🧪 Testing yarn path finding logic...\n');
  
  // 模拟不同的项目结构
  const testScenarios = [
    {
      name: 'Current project structure',
      gitDir: '/Users/<USER>/Documents/code/fe-platform',
      clientPath: '/Users/<USER>/Documents/code/fe-platform/client'
    },
    {
      name: 'GitHub project structure', 
      gitDir: '/Users/<USER>/Documents/code/fe-platform/github/test',
      clientPath: '/Users/<USER>/Documents/code/fe-platform/github/test/client'
    }
  ];
  
  testScenarios.forEach((scenario, index) => {
    console.log(`${index + 1}. Testing scenario: ${scenario.name}`);
    console.log(`   Git dir: ${scenario.gitDir}`);
    console.log(`   Client path: ${scenario.clientPath}`);
    
    const possibleYarnPaths = [
      // 1. 当前项目的 node_modules/.bin/yarn
      path.join(scenario.clientPath, 'node_modules', '.bin', 'yarn'),
      // 2. 父级项目的 node_modules/.bin/yarn
      path.join(scenario.gitDir, 'node_modules', '.bin', 'yarn'),
      // 3. 全局 yarn
      'yarn'
    ];
    
    let yarnCommand = 'yarn';
    
    for (const yarnPath of possibleYarnPaths) {
      console.log(`   Checking: ${yarnPath}`);
      
      if (yarnPath !== 'yarn') {
        if (fs.existsSync(yarnPath)) {
          yarnCommand = yarnPath;
          console.log(`   ✅ Found: ${yarnCommand}`);
          break;
        } else {
          console.log(`   ❌ Not found`);
        }
      } else {
        // 检查全局 yarn
        try {
          execSync('which yarn', { stdio: 'ignore' });
          console.log(`   ✅ Global yarn available`);
        } catch (error) {
          console.log(`   ❌ Global yarn not available`);
        }
      }
    }
    
    console.log(`   📋 Final choice: ${yarnCommand}`);
    console.log('');
  });
}

// 测试实际的 yarn 命令执行
function testYarnExecution() {
  console.log('🧪 Testing yarn command execution...\n');
  
  // 查找当前项目的 yarn
  const currentDir = process.cwd();
  const possibleYarnPaths = [
    path.join(currentDir, 'node_modules', '.bin', 'yarn'),
    path.join(currentDir, 'client', 'node_modules', '.bin', 'yarn'),
    'yarn'
  ];
  
  console.log('Current working directory:', currentDir);
  console.log('Searching for yarn...\n');
  
  for (const yarnPath of possibleYarnPaths) {
    console.log(`Testing: ${yarnPath}`);
    
    if (yarnPath !== 'yarn' && !fs.existsSync(yarnPath)) {
      console.log('  ❌ File does not exist');
      continue;
    }
    
    try {
      // 测试 yarn --version
      const version = execSync(`${yarnPath} --version`, { 
        encoding: 'utf8',
        timeout: 10000 
      });
      console.log(`  ✅ Version: ${version.trim()}`);
      
      // 测试 yarn config
      try {
        const config = execSync(`${yarnPath} config get registry`, { 
          encoding: 'utf8',
          timeout: 10000 
        });
        console.log(`  ✅ Current registry: ${config.trim()}`);
      } catch (configError) {
        console.log(`  ⚠️  Config test failed: ${configError.message}`);
      }
      
    } catch (error) {
      console.log(`  ❌ Execution failed: ${error.message}`);
    }
    
    console.log('');
  }
}

// 检查项目结构
function checkProjectStructure() {
  console.log('🧪 Checking project structure...\n');
  
  const currentDir = process.cwd();
  const importantPaths = [
    'package.json',
    'node_modules',
    'node_modules/.bin',
    'node_modules/.bin/yarn',
    'client',
    'client/package.json',
    'client/node_modules',
    'client/node_modules/.bin',
    'client/node_modules/.bin/yarn',
    'github',
    'github/test',
    'github/test/client'
  ];
  
  console.log('Project structure analysis:');
  importantPaths.forEach(relativePath => {
    const fullPath = path.join(currentDir, relativePath);
    const exists = fs.existsSync(fullPath);
    const isDir = exists && fs.statSync(fullPath).isDirectory();
    const type = exists ? (isDir ? 'DIR' : 'FILE') : 'MISSING';
    
    console.log(`  ${exists ? '✅' : '❌'} ${type.padEnd(7)} ${relativePath}`);
  });
}

function runTests() {
  console.log('🔍 Testing yarn command path resolution\n');
  console.log('=' .repeat(60));
  
  checkProjectStructure();
  console.log('\n' + '='.repeat(60));
  
  testYarnPathFinding();
  console.log('='.repeat(60));
  
  testYarnExecution();
  console.log('='.repeat(60));
  
  console.log('\n💡 Recommendations:');
  console.log('1. Use local yarn from node_modules/.bin when available');
  console.log('2. Fall back to global yarn if local not found');
  console.log('3. Always test yarn command before using it');
  console.log('4. Consider using npx yarn as an alternative');
}

if (require.main === module) {
  runTests();
}

module.exports = { testYarnPathFinding, testYarnExecution, checkProjectStructure };
