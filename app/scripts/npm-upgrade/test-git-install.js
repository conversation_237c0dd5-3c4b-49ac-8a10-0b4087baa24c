const { execSync } = require('child_process');

// 测试 git 安装功能
function testGitInstall() {
  try {
    console.log('🧪 Testing git installation check...\n');
    
    // 检查 git 是否已安装
    try {
      const gitVersion = execSync('git --version', { encoding: 'utf8' });
      console.log('✅ Git is already installed:', gitVersion.trim());
      return true;
    } catch (error) {
      console.log('📦 Git not found, would need to install');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

// 模拟安装过程（不实际执行 yum install）
function simulateGitInstall() {
  console.log('\n🧪 Simulating git installation process...\n');
  
  console.log('🔧 Checking git installation...');
  
  // 检查 git 是否已安装
  try {
    execSync('git --version', { stdio: 'ignore' });
    console.log('✅ Git is already installed');
    return;
  } catch (error) {
    console.log('📦 Git not found, would install with:');
    console.log('   Command: yum install -y git');
    console.log('   (Simulation - not actually running)');
  }
}

// 检查系统信息
function checkSystemInfo() {
  console.log('\n🧪 Checking system information...\n');
  
  try {
    // 检查操作系统
    const os = process.platform;
    console.log('Operating System:', os);
    
    // 检查是否有 yum 命令
    try {
      execSync('which yum', { stdio: 'ignore' });
      console.log('✅ yum package manager is available');
    } catch (error) {
      console.log('❌ yum package manager not found');
      console.log('   Note: This system may not support yum (e.g., macOS, Ubuntu)');
      console.log('   Alternative package managers:');
      console.log('   - macOS: brew install git');
      console.log('   - Ubuntu/Debian: apt-get install -y git');
    }
    
    // 检查是否有 sudo 权限（间接检查）
    try {
      execSync('id', { stdio: 'ignore' });
      console.log('✅ User ID command available');
    } catch (error) {
      console.log('❌ Cannot check user permissions');
    }
    
  } catch (error) {
    console.error('❌ System check failed:', error.message);
  }
}

function runTests() {
  console.log('🧪 Testing git installation functionality...\n');
  
  const gitInstalled = testGitInstall();
  simulateGitInstall();
  checkSystemInfo();
  
  console.log('\n📋 Summary:');
  console.log(`Git installed: ${gitInstalled ? 'Yes' : 'No'}`);
  console.log('Installation function: Ready');
  console.log('Note: Actual yum installation requires root privileges and CentOS/RHEL system');
}

if (require.main === module) {
  runTests();
}

module.exports = { testGitInstall, simulateGitInstall, checkSystemInfo };
