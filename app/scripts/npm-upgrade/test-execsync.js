const { execSync } = require('child_process');

// 测试 execSync 执行 yum 命令
function testExecSyncYum() {
  console.log('🧪 Testing execSync with yum commands...\n');
  
  // 1. 测试基本的 yum 命令
  console.log('1. Testing basic yum availability:');
  try {
    const yumPath = execSync('which yum', { encoding: 'utf8' });
    console.log('   ✅ yum found at:', yumPath.trim());
  } catch (error) {
    console.log('   ❌ yum not found in PATH');
    console.log('   Error:', error.message);
    return;
  }
  
  // 2. 测试环境变量
  console.log('\n2. Environment variables:');
  console.log('   PATH:', process.env.PATH);
  console.log('   USER:', process.env.USER || 'undefined');
  console.log('   HOME:', process.env.HOME || 'undefined');
  console.log('   PWD:', process.env.PWD || 'undefined');
  
  // 3. 测试用户权限
  console.log('\n3. User permissions:');
  try {
    const userId = execSync('id', { encoding: 'utf8' });
    console.log('   Current user:', userId.trim());
  } catch (error) {
    console.log('   ❌ Cannot get user info:', error.message);
  }
  
  // 4. 测试不同的 execSync 选项
  console.log('\n4. Testing different execSync options:');
  
  const testCommands = [
    'yum --version',
    'yum repolist',
  ];
  
  const execOptions = [
    {
      name: 'Default options',
      options: { encoding: 'utf8' }
    },
    {
      name: 'With bash shell',
      options: { 
        encoding: 'utf8',
        shell: '/bin/bash'
      }
    },
    {
      name: 'With extended PATH',
      options: { 
        encoding: 'utf8',
        shell: '/bin/bash',
        env: {
          ...process.env,
          PATH: process.env.PATH + ':/usr/bin:/bin:/usr/sbin:/sbin'
        }
      }
    },
    {
      name: 'With full environment',
      options: { 
        encoding: 'utf8',
        shell: '/bin/bash',
        env: {
          ...process.env,
          PATH: process.env.PATH + ':/usr/bin:/bin:/usr/sbin:/sbin',
          TERM: 'xterm'
        },
        timeout: 30000
      }
    }
  ];
  
  for (const command of testCommands) {
    console.log(`\n   Testing command: ${command}`);
    
    for (const { name, options } of execOptions) {
      try {
        console.log(`     ${name}:`);
        const result = execSync(command, options);
        console.log(`       ✅ Success: ${result.trim().substring(0, 100)}...`);
      } catch (error) {
        console.log(`       ❌ Failed: ${error.message}`);
        if (error.status) {
          console.log(`       Exit code: ${error.status}`);
        }
        if (error.stderr) {
          console.log(`       Stderr: ${error.stderr.toString().trim()}`);
        }
      }
    }
  }
}

// 测试 git 安装命令
function testGitInstallCommand() {
  console.log('\n🧪 Testing git installation command...\n');
  
  // 首先检查 git 是否已安装
  try {
    const gitVersion = execSync('git --version', { encoding: 'utf8' });
    console.log('✅ Git already installed:', gitVersion.trim());
    console.log('⏭️  Skipping installation test');
    return;
  } catch (error) {
    console.log('📦 Git not found, testing installation...');
  }
  
  const installCommand = 'yum install -y git';
  
  console.log(`Testing command: ${installCommand}`);
  console.log('⚠️  This will attempt to actually install git!');
  
  try {
    // 使用改进的执行选项
    const execOptions = {
      stdio: 'inherit',
      shell: '/bin/bash',
      env: {
        ...process.env,
        PATH: process.env.PATH + ':/usr/bin:/bin:/usr/sbin:/sbin',
      },
      timeout: 300000, // 5分钟超时
    };
    
    console.log('Executing with enhanced options...');
    execSync(installCommand, execOptions);
    console.log('✅ Git installation completed successfully');
    
    // 验证安装
    const gitVersion = execSync('git --version', { encoding: 'utf8' });
    console.log('✅ Git verification:', gitVersion.trim());
    
  } catch (error) {
    console.log('❌ Git installation failed');
    console.log('Error details:');
    console.log('  Message:', error.message);
    console.log('  Exit code:', error.status);
    if (error.stderr) {
      console.log('  Stderr:', error.stderr.toString());
    }
    if (error.stdout) {
      console.log('  Stdout:', error.stdout.toString());
    }
  }
}

// 比较 shell 和 Node.js 环境
function compareEnvironments() {
  console.log('\n🧪 Comparing shell vs Node.js environments...\n');
  
  console.log('1. PATH comparison:');
  try {
    const shellPath = execSync('echo $PATH', { encoding: 'utf8', shell: '/bin/bash' });
    console.log('   Shell PATH:', shellPath.trim());
    console.log('   Node.js PATH:', process.env.PATH);
    console.log('   Match:', shellPath.trim() === process.env.PATH ? '✅' : '❌');
  } catch (error) {
    console.log('   ❌ Cannot get shell PATH:', error.message);
  }
  
  console.log('\n2. User comparison:');
  try {
    const shellUser = execSync('whoami', { encoding: 'utf8' });
    console.log('   Shell user:', shellUser.trim());
    console.log('   Node.js USER env:', process.env.USER || 'undefined');
    console.log('   Process UID:', process.getuid ? process.getuid() : 'undefined');
  } catch (error) {
    console.log('   ❌ Cannot get user info:', error.message);
  }
  
  console.log('\n3. Working directory:');
  console.log('   Node.js CWD:', process.cwd());
  console.log('   Process PWD env:', process.env.PWD || 'undefined');
}

function runTests() {
  console.log('🔍 Diagnosing execSync vs direct shell execution\n');
  console.log('=' .repeat(60));
  
  compareEnvironments();
  testExecSyncYum();
  
  console.log('\n' + '='.repeat(60));
  console.log('💡 If you want to test git installation, uncomment the next line:');
  console.log(testGitInstallCommand());
  console.log('\n⚠️  Note: testGitInstallCommand() will actually try to install git!');
}


module.exports = { runTests, testExecSyncYum, testGitInstallCommand, compareEnvironments };
