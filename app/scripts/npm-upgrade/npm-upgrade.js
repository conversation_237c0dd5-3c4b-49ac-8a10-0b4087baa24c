
/**
 * data格式如下：
 * {
    "user_name": "xujiazheng",
    "real_name": "徐嘉正",
    "alias_name": "徐嘉正",
    "repo_id": "221",
    "repo_name": "wsc-tee-trade-common",
    "repo_url": "https://gitlab.qima-inc.com/weapp/wsc-tee-trade-common",
    "branch_name": "hotfix/test-hook",
    "release_type": "1",
    "dist_tag": "beta",
    "semver": "prerelease",
    "modules": [
        {
            "module_id": "643",
            "module_name": "@youzan/wsc-tee-trade-common",
            "version": "2.5.7-beta.20250613105251.0"
        }
    ]
}
 */

const { createOrSwitchBranch } = require('./gitbash');

const authUserList = ['xujiazheng'];

module.exports = function (data) {
  const {
    modules,
    branch_name,
    release_type,
    dist_tag,
    semver,
    user_name
  } = data;

  // 验证用户
  if (!authUserList.includes(user_name)) {
    return;
  }

  // 验证分支
  if (branch_name === 'master') {
    return;
  }

  const options = {
    appName: 'test',  // 项目会被克隆到 fe-platform/github/test/
    branchName: 'hotfix/test-hook-final',
    updatePackages: true,
    packageName: '@youzan/wsc-tee-trade-common',
    packageVersion: '2.5.7-beta.20250613105251.0',
    clientDir: 'client',
    commitAndPush: true
  };

  createOrSwitchBranch(options);
}