
/**
 * data格式如下：
 * {
    "user_name": "xujiazheng",
    "real_name": "徐嘉正",
    "alias_name": "徐嘉正",
    "repo_id": "221",
    "repo_name": "wsc-tee-trade-common",
    "repo_url": "https://gitlab.qima-inc.com/weapp/wsc-tee-trade-common",
    "branch_name": "hotfix/test-hook",
    "release_type": "1",
    "dist_tag": "beta",
    "semver": "prerelease",
    "modules": [
        {
            "module_id": "643",
            "module_name": "@youzan/wsc-tee-trade-common",
            "version": "2.5.7-beta.20250613105251.0"
        }
    ]
}
 */

const { createOrSwitchBranch } = require('./gitbash');
const { packageAppMaps } = require('./constants');

const authUserList = ['xujiazheng'];

module.exports = async function (data) {
  const {
    modules,
    branch_name,
    release_type,
    dist_tag,
    semver,
    user_name
  } = data;

  // 验证用户
  if (!authUserList.includes(user_name)) {
    console.log(`User ${user_name} is not authorized`);
    return { success: false, error: 'User not authorized' };
  }

  // 验证分支
  if (branch_name === 'master') {
    console.log('Skipping master branch');
    return { success: false, error: 'Master branch not allowed' };
  }

  // 验证 modules 是否存在
  if (!modules || !Array.isArray(modules) || modules.length === 0) {
    console.log('No modules found in data');
    return { success: false, error: 'No modules found' };
  }

  console.log(`Processing ${modules.length} modules...`);

  const results = [];

  try {
    // 遍历每个 module
    for (const module of modules) {
      const { module_name, version } = module;

      console.log(`\n📦 Processing module: ${module_name}@${version}`);

      // 检查 module_name 是否在 packageAppMaps 中
      if (!packageAppMaps[module_name]) {
        console.log(`⏭️  Module ${module_name} not found in packageAppMaps, skipping...`);
        continue;
      }

      const targetApps = packageAppMaps[module_name];
      console.log(`🎯 Found ${targetApps.length} target apps: ${targetApps.join(', ')}`);

      // 为每个目标应用执行 createOrSwitchBranch
      for (const appName of targetApps) {
        console.log(`\n🚀 Processing app: ${appName}`);

        const options = {
          appName: appName,
          branchName: branch_name,
          updatePackages: true,
          packageName: module_name,
          packageVersion: version,
          clientDir: 'client',
          commitAndPush: true,
          commitMessage: `feat: update ${module_name} to ${version}`
        };

        try {
          const result = await createOrSwitchBranch(options);
          console.log(`✅ Successfully processed ${appName} for ${module_name}`);

          results.push({
            module_name,
            version,
            appName,
            success: true,
            operations: result.operations
          });
        } catch (error) {
          console.error(`❌ Failed to process ${appName} for ${module_name}: ${error.message}`);

          results.push({
            module_name,
            version,
            appName,
            success: false,
            error: error.message
          });
        }
      }
    }

    // 检查是否所有操作都成功
    const failedResults = results.filter(r => !r.success);
    const successCount = results.filter(r => r.success).length;

    console.log(`\n📊 Summary: ${successCount}/${results.length} operations completed successfully`);

    if (failedResults.length > 0) {
      console.log('❌ Failed operations:');
      failedResults.forEach(result => {
        console.log(`   - ${result.appName} (${result.module_name}): ${result.error}`);
      });
    }

    return {
      success: failedResults.length === 0,
      totalModules: modules.length,
      processedOperations: results.length,
      successfulOperations: successCount,
      failedOperations: failedResults.length,
      results: results
    };

  } catch (error) {
    console.error(`❌ Unexpected error during processing: ${error.message}`);
    return {
      success: false,
      error: error.message,
      results: results
    };
  }
}