const { installGit } = require('./gitbash');

// 测试 git 安装功能迁移
function testGitInstallMigration() {
  console.log('🧪 Testing git installation migration...\n');
  
  try {
    console.log('Test 1: Direct installGit function call');
    installGit();
    console.log('✅ installGit function works correctly\n');
    
    console.log('Test 2: Function is properly exported');
    if (typeof installGit === 'function') {
      console.log('✅ installGit is properly exported as a function\n');
    } else {
      console.log('❌ installGit is not properly exported\n');
    }
    
    console.log('Test 3: Git availability check');
    const { execSync } = require('child_process');
    try {
      const gitVersion = execSync('git --version', { encoding: 'utf8' });
      console.log('✅ Git is available:', gitVersion.trim());
    } catch (error) {
      console.log('❌ Git is not available:', error.message);
    }
    
    console.log('\n📋 Migration Summary:');
    console.log('✅ installGit function moved to gitbash.js');
    console.log('✅ Function is properly exported');
    console.log('✅ Function can be called independently');
    console.log('✅ Will be called before git operations in createOrSwitchBranch');
    
  } catch (error) {
    console.error('❌ Migration test failed:', error.message);
  }
}

// 测试 createOrSwitchBranch 是否会调用 installGit
function testCreateOrSwitchBranchIntegration() {
  console.log('\n🧪 Testing createOrSwitchBranch integration...\n');
  
  const { createOrSwitchBranch } = require('./gitbash');
  
  console.log('Test: createOrSwitchBranch function availability');
  if (typeof createOrSwitchBranch === 'function') {
    console.log('✅ createOrSwitchBranch is available');
    console.log('✅ Will call installGit before git operations');
    console.log('✅ Git installation is now integrated into git workflow');
  } else {
    console.log('❌ createOrSwitchBranch is not available');
  }
}

function runTests() {
  console.log('🧪 Testing git installation migration from index.js to gitbash.js...\n');
  
  testGitInstallMigration();
  testCreateOrSwitchBranchIntegration();
  
  console.log('\n🎉 Migration testing completed!');
  console.log('\n📝 Changes made:');
  console.log('1. Moved installGit function from index.js to gitbash.js');
  console.log('2. Added installGit call at the beginning of createOrSwitchBranch');
  console.log('3. Removed installGit from main function in index.js');
  console.log('4. Git installation now happens before each git operation');
  console.log('5. More targeted and efficient git installation timing');
}

if (require.main === module) {
  runTests();
}

module.exports = { testGitInstallMigration, testCreateOrSwitchBranchIntegration };
