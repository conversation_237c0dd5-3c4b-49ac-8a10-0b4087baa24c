
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const appGitMaps = require('./appGitMaps');

// Helper function to get current branch name
function getCurrentBranch() {
  try {
    return execSync('git branch --show-current', { encoding: 'utf8' }).trim();
  } catch (error) {
    return 'unknown';
  }
}

// Helper function to check if working directory is clean
function isWorkingDirectoryClean() {
  try {
    const status = execSync('git status --porcelain', { encoding: 'utf8' });
    return !status.trim();
  } catch (error) {
    return false;
  }
}

// Helper function to get or clone project directory
function getOrCloneProjectDir(appName, baseGithubDir = '/Users/<USER>/Documents/code/github') {
  // 构建项目目录路径
  const projectDir = path.join(baseGithubDir, appName);

  // 检查项目目录是否存在
  if (fs.existsSync(projectDir)) {
    console.log(`✓ Project directory already exists: ${projectDir}`);
    return projectDir;
  }

  // 从 appGitMaps 获取 git 地址
  const gitUrl = appGitMaps[appName];
  if (!gitUrl) {
    throw new Error(`App '${appName}' not found in appGitMaps. Available apps: ${Object.keys(appGitMaps).join(', ')}`);
  }

  console.log(`Project directory not found, cloning from: ${gitUrl}`);

  // 确保基础目录存在
  if (!fs.existsSync(baseGithubDir)) {
    fs.mkdirSync(baseGithubDir, { recursive: true });
    console.log(`Created base directory: ${baseGithubDir}`);
  }

  // 克隆项目
  try {
    execSync(`git clone ${gitUrl} ${projectDir}`, { stdio: 'inherit' });
    console.log(`✅ Successfully cloned project to: ${projectDir}`);
    return projectDir;
  } catch (error) {
    throw new Error(`Failed to clone project: ${error.message}`);
  }
}

/**
 * 创建或切换到指定分支，并可选择更新npm包
 * @param {Object} options - 配置选项
 * @param {string} options.appName - 应用名称（将根据此名称从appGitMaps获取git地址并管理项目目录）
 * @param {string} [options.baseGithubDir='/Users/<USER>/Documents/code/github'] - github项目基础目录
 * @param {string} options.branchName - 目标分支名称
 * @param {string} [options.baseBranch='master'] - 基础分支（当目标分支不存在时从此分支创建）
 * @param {boolean} [options.cleanWorkingDir=true] - 是否清理工作目录
 * @param {boolean} [options.updatePackages=false] - 是否更新npm包
 * @param {string} [options.packageName] - 要更新的npm包名称
 * @param {string} [options.packageVersion] - 要更新的npm包版本
 * @param {string} [options.clientDir='client'] - client目录名称
 * @param {string} [options.npmRegistry='http://registry.npm.qima-inc.com/'] - npm源地址
 * @param {boolean} [options.commitAndPush=true] - 是否提交并推送更改
 * @param {string} [options.commitMessage] - 自定义提交信息
 * @returns {Promise<Object>} 返回操作结果
 */
async function createOrSwitchBranch(options) {
  const {
    appName,
    baseGithubDir = '/Users/<USER>/Documents/code/github',
    branchName,
    baseBranch = 'master',
    cleanWorkingDir = true,
    updatePackages = false,
    packageName,
    packageVersion,
    clientDir = 'client',
    npmRegistry = 'http://registry.npm.qima-inc.com/',
    commitAndPush = true,
    commitMessage,
  } = options;

  // 验证必需参数
  if (!appName || !branchName) {
    throw new Error('appName and branchName are required');
  }

  // 获取或克隆项目目录
  const gitDir = getOrCloneProjectDir(appName, baseGithubDir);

  const result = {
    success: false,
    currentBranch: null,
    targetBranch: branchName,
    operations: [],
  };

  try {
    // Change to the git directory
    const originalDir = process.cwd();
    process.chdir(gitDir);
    console.log(`Changed directory to: ${gitDir}`);
    result.operations.push(`Changed to directory: ${gitDir}`);

    // Check if we're in a git repository
    execSync('git rev-parse --is-inside-work-tree', { stdio: 'ignore' });

    // Show current branch status
    const currentBranch = getCurrentBranch();
    result.currentBranch = currentBranch;
    console.log(`Current branch: ${currentBranch}`);
    console.log(`Target branch: ${branchName}`);

    // Check if we need to switch branches
    if (currentBranch === branchName) {
      console.log(`✓ Already on branch '${branchName}'`);
      if (isWorkingDirectoryClean()) {
        // Check if remote branch exists before pulling
        try {
          execSync(`git rev-parse --verify origin/${branchName}`, { stdio: 'ignore' });
          console.log('Working directory is clean, pulling latest changes...');
          execSync(`git pull origin ${branchName}`, { stdio: 'inherit' });
          result.operations.push(`Pulled latest changes from origin/${branchName}`);
        } catch (e) {
          console.log(`Remote branch origin/${branchName} doesn't exist, skipping pull`);
          result.operations.push('Skipped pull - remote branch does not exist');
        }
      } else if (cleanWorkingDir) {
        console.log('Working directory has changes, cleaning up first...');
        // Clean working directory first, then pull
        const status = execSync('git status --porcelain', { encoding: 'utf8' });
        if (status.trim()) {
          console.log('Found uncommitted changes:');
          console.log(status);
          console.log('Cleaning up uncommitted changes...');

          // Reset any staged and unstaged changes
          execSync('git reset --hard HEAD', { stdio: 'inherit' });
          console.log('✓ Reset all changes to last commit');
          result.operations.push('Reset uncommitted changes');

          // Remove untracked files and directories
          execSync('git clean -fd', { stdio: 'inherit' });
          console.log('✓ Removed untracked files and directories');
          result.operations.push('Removed untracked files');
        }

        // Check if remote branch exists before pulling
        try {
          execSync(`git rev-parse --verify origin/${branchName}`, { stdio: 'ignore' });
          console.log('Pulling latest changes...');
          execSync(`git pull origin ${branchName}`, { stdio: 'inherit' });
          result.operations.push(`Pulled latest changes from origin/${branchName}`);
        } catch (e) {
          console.log(`Remote branch origin/${branchName} doesn't exist, skipping pull`);
          result.operations.push('Skipped pull - remote branch does not exist');
        }
      } else {
        console.log('Working directory has changes, skipping pull (cleanWorkingDir=false)');
        result.operations.push('Skipped pull due to uncommitted changes');
      }
    } else {
      // Check for uncommitted changes and clean them up
      if (cleanWorkingDir) {
        console.log('Checking for uncommitted changes...');
        try {
          const status = execSync('git status --porcelain', { encoding: 'utf8' });
          if (status.trim()) {
            console.log('Found uncommitted changes:');
            console.log(status);
            console.log('Cleaning up uncommitted changes...');

            // Show what will be reset
            try {
              const diff = execSync('git diff --name-only', { encoding: 'utf8' });
              if (diff.trim()) {
                console.log('Modified files that will be reset:', diff.trim().split('\n'));
              }
            } catch (e) {
              // Ignore diff errors
            }

            // Reset any staged and unstaged changes
            execSync('git reset --hard HEAD', { stdio: 'inherit' });
            console.log('✓ Reset all changes to last commit');
            result.operations.push('Reset uncommitted changes');

            // Remove untracked files and directories
            try {
              const untracked = execSync('git ls-files --others --exclude-standard', { encoding: 'utf8' });
              if (untracked.trim()) {
                console.log('Untracked files that will be removed:', untracked.trim().split('\n'));
              }
            } catch (e) {
              // Ignore if no untracked files
            }

            execSync('git clean -fd', { stdio: 'inherit' });
            console.log('✓ Removed untracked files and directories');
            result.operations.push('Removed untracked files');

            console.log('✓ Working directory is now clean');
          } else {
            console.log('✓ Working directory is already clean');
          }
        } catch (error) {
          console.log('Warning: Could not check git status, continuing...');
        }
      }

      // Fetch latest changes from remote
      console.log('Fetching latest changes...');
      execSync('git fetch', { stdio: 'inherit' });
      result.operations.push('Fetched latest changes');

      // Check if branch exists locally or remotely
      const branchExists = () => {
        try {
          execSync(`git rev-parse --verify ${branchName}`, { stdio: 'ignore' });
          return true;
        } catch (e) {
          try {
            execSync(`git rev-parse --verify origin/${branchName}`, { stdio: 'ignore' });
            return true;
          } catch (e) {
            return false;
          }
        }
      };

      if (branchExists()) {
        // Branch exists, checkout to it
        console.log(`Branch ${branchName} exists, checking out...`);
        execSync(`git checkout ${branchName}`, { stdio: 'inherit' });
        result.operations.push(`Checked out existing branch: ${branchName}`);

        // Pull latest changes for existing branch
        console.log('Pulling latest changes...');
        execSync(`git pull origin ${branchName}`, { stdio: 'inherit' });
        result.operations.push(`Pulled latest changes from origin/${branchName}`);
      } else {
        // Branch doesn't exist, create from base branch
        console.log(`Branch ${branchName} doesn't exist, creating from ${baseBranch}...`);
        execSync(`git checkout ${baseBranch}`, { stdio: 'inherit' });
        execSync(`git pull origin ${baseBranch}`, { stdio: 'inherit' });
        execSync(`git checkout -b ${branchName}`, { stdio: 'inherit' });
        result.operations.push(`Created new branch: ${branchName} from ${baseBranch}`);
      }
    }

    // Update npm packages if requested
    if (updatePackages) {
      console.log('\n🔄 Starting npm package update...');

      // Validate package parameters
      if (!packageName || !packageVersion) {
        throw new Error('packageName and packageVersion are required when updatePackages is true');
      }

      const clientPath = path.join(gitDir, clientDir);

      // Check if client directory exists
      if (!fs.existsSync(clientPath)) {
        throw new Error(`Client directory not found: ${clientPath}`);
      }

      console.log(`Entering client directory: ${clientPath}`);
      process.chdir(clientPath);
      result.operations.push(`Changed to client directory: ${clientPath}`);

      // Set npm registry
      console.log(`Setting npm registry to: ${npmRegistry}`);
      execSync(`yarn config set registry ${npmRegistry}`, { stdio: 'inherit' });
      result.operations.push(`Set npm registry to: ${npmRegistry}`);

      // Update the specific package
      const packageSpec = packageVersion ? `${packageName}@${packageVersion}` : packageName;
      console.log(`Updating package: ${packageSpec}`);
      execSync(`yarn add ${packageSpec}`, { stdio: 'inherit' });
      result.operations.push(`Updated package: ${packageSpec}`);

      console.log('✅ Package update completed successfully!');

      // Commit and push changes if requested
      if (commitAndPush) {
        console.log('\n📤 Committing and pushing changes...');

        // Go back to git root directory
        process.chdir(gitDir);

        // Generate commit message
        const defaultCommitMessage = `feat: update ${packageName} to ${packageVersion || 'latest'}`;
        const finalCommitMessage = commitMessage || defaultCommitMessage;

        // Add package.json and yarn.lock changes
        const clientRelativePath = clientDir;
        execSync(`git add ${clientRelativePath}/package.json`, { stdio: 'inherit' });
        execSync(`git add ${clientRelativePath}/yarn.lock`, { stdio: 'inherit' });
        console.log('Added package.json and yarn.lock to git');
        result.operations.push('Added package files to git');

        // Check if there are changes to commit
        try {
          const status = execSync('git status --porcelain', { encoding: 'utf8' });
          if (status.trim()) {
            // Commit changes
            execSync(`git commit -m "${finalCommitMessage}"`, { stdio: 'inherit' });
            console.log(`Committed changes: ${finalCommitMessage}`);
            result.operations.push(`Committed: ${finalCommitMessage}`);

            // Push to remote
            execSync(`git push -u origin ${branchName}`, { stdio: 'inherit' });
            console.log(`✅ Successfully pushed to origin/${branchName}`);
            result.operations.push(`Pushed to origin/${branchName}`);
          } else {
            console.log('No changes to commit (package files unchanged)');
            result.operations.push('No changes to commit');
          }
        } catch (error) {
          console.log('Warning: Could not check git status for commit');
          result.operations.push('Warning: Git status check failed');
        }
      }
    }

    result.success = true;
    return result;
  } catch (error) {
    result.error = error.message;
    throw error;
  }
}

// 导出函数供其他模块使用
module.exports = {
  createOrSwitchBranch,
  getCurrentBranch,
  isWorkingDirectoryClean,
  getOrCloneProjectDir,
};

// 如果直接运行此文件，执行示例
if (require.main === module) {
  // 用户原始需求测试（完整功能）
  const defaultOptions = {
    appName: 'test',
    baseGithubDir: '/Users/<USER>/Documents/code/github',
    branchName: 'hotfix/test-hook-final',
    updatePackages: true,
    packageName: '@youzan/wsc-tee-trade-common',
    packageVersion: '2.5.7-beta.20250613105251.0',
    clientDir: 'client',
    commitAndPush: true,
    // 使用默认提交信息
  };

  // 执行函数
  createOrSwitchBranch(defaultOptions)
    .then(result => {
      console.log('\n✅ Branch operation completed successfully!');
      console.log('📊 Summary:');
      console.log(`   Current branch: ${result.currentBranch}`);
      console.log(`   Target branch: ${result.targetBranch}`);
      console.log(`   Operations performed: ${result.operations.length}`);
      result.operations.forEach((op, index) => {
        console.log(`   ${index + 1}. ${op}`);
      });
    })
    .catch(error => {
      console.error('\n❌ Branch operation failed:');
      console.error(`   Error: ${error.message}`);
      process.exit(1);
    });
}
