const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const { appGitMaps } = require('./constants');

// 安装 git
function installGit() {
  try {
    console.log('🔧 Checking git installation...');

    // 检查 git 是否已安装
    try {
      const gitVersion = execSync('git --version', { encoding: 'utf8' });
      console.log('✅ Git is already installed:', gitVersion.trim());
      return;
    } catch (error) {
      console.log('📦 Git not found, attempting to install...');
    }

    // 检测系统类型并选择合适的包管理器
    const installCommands = [
      // CentOS/RHEL with sudo
      'sudo yum install -y git',
      // CentOS/RHEL as root
      'yum install -y git',
      // Ubuntu/Debian with sudo
      'sudo apt-get update && sudo apt-get install -y git',
      // Ubuntu/Debian as root
      'apt-get update && apt-get install -y git',
      // Alpine Linux
      'apk add git',
      // macOS (if homebrew is available)
      'brew install git',
    ];

    let installSuccess = false;
    let lastError = null;

    for (const command of installCommands) {
      try {
        console.log(`Trying: ${command}`);

        // 使用更完整的执行选项
        const execOptions = {
          stdio: 'inherit',
          shell: '/bin/bash',  // 明确指定使用 bash
          env: {
            ...process.env,    // 继承当前进程的环境变量
            PATH: process.env.PATH + ':/usr/bin:/bin:/usr/sbin:/sbin',  // 确保包含常用路径
          },
          timeout: 300000,     // 5分钟超时
        };

        execSync(command, execOptions);
        console.log('✅ Git installed successfully');
        installSuccess = true;
        break;
      } catch (error) {
        console.log(`❌ Command failed: ${command}`);
        console.log(`   Error code: ${error.status}`);
        console.log(`   Error message: ${error.message}`);
        if (error.stderr) {
          console.log(`   Stderr: ${error.stderr.toString()}`);
        }
        lastError = error;
        continue;
      }
    }

    if (!installSuccess) {
      console.error('❌ All installation attempts failed');
      console.error('Last error:', lastError && lastError.message);
      console.log('💡 Please install git manually or run with appropriate permissions');
      console.log('💡 Common solutions:');
      console.log('   - Run as root user');
      console.log('   - Use sudo if available');
      console.log('   - Install git manually: yum install -y git (CentOS/RHEL) or apt-get install -y git (Ubuntu/Debian)');
      throw new Error('Git installation failed with all attempted methods');
    }

    // 验证安装
    try {
      const gitVersion = execSync('git --version', { encoding: 'utf8' });
      console.log('✅ Git installation verified:', gitVersion.trim());
    } catch (error) {
      throw new Error('Git installation appeared to succeed but git command is still not available');
    }

  } catch (error) {
    console.error('❌ Failed to install git:', error.message);
    // 不抛出错误，允许程序继续运行
    console.log('⚠️  Continuing without git installation - git operations may fail');
  }
}

// Git 认证配置
const GIT_USERNAME = 'xujiazheng';
const GIT_PASSWORD = 'Xjz0520`';

// 将 SSH URL 转换为 HTTPS URL 并添加认证信息
function convertToHttpsUrl(gitUrl) {
  // URL 编码用户名和密码以处理特殊字符
  const encodedUsername = encodeURIComponent(GIT_USERNAME);
  const encodedPassword = encodeURIComponent(GIT_PASSWORD);

  // 如果已经是 HTTPS URL，直接添加认证信息
  if (gitUrl.startsWith('https://')) {
    const url = new URL(gitUrl);
    url.username = encodedUsername;
    url.password = encodedPassword;
    return url.toString();
  }

  // 将 SSH URL 转换为 HTTPS URL
  // 格式: ***********************:xujiazheng/test.git -> https://username:<EMAIL>/xujiazheng/test.git
  if (gitUrl.startsWith('git@')) {
    const match = gitUrl.match(/git@([^:]+):(.+)\.git$/);
    if (match) {
      const [, host, path] = match;
      return `https://${encodedUsername}:${encodedPassword}@${host}/${path}.git`;
    }
  }

  throw new Error(`Unsupported git URL format: ${gitUrl}`);
}

// Helper function to get current branch name
function getCurrentBranch() {
  try {
    return execSync('git branch --show-current', { encoding: 'utf8' }).trim();
  } catch (error) {
    return 'unknown';
  }
}

// Helper function to check if working directory is clean
function isWorkingDirectoryClean() {
  try {
    const status = execSync('git status --porcelain', { encoding: 'utf8' });
    return !status.trim();
  } catch (error) {
    return false;
  }
}

// Helper function to get or clone project directory
function getOrCloneProjectDir(appName, baseGithubDir = path.join(__dirname, '../../../github')) {
  // 构建项目目录路径
  const projectDir = path.join(baseGithubDir, appName);

  // 检查项目目录是否存在
  if (fs.existsSync(projectDir)) {
    console.log(`✓ Project directory already exists: ${projectDir}`);
    return projectDir;
  }

  // 从 appGitMaps 获取 git 地址
  const gitUrl = appGitMaps[appName];
  if (!gitUrl) {
    throw new Error(`App '${appName}' not found in appGitMaps. Available apps: ${Object.keys(appGitMaps).join(', ')}`);
  }

  // 转换为 HTTPS URL 并添加认证信息
  const httpsUrl = convertToHttpsUrl(gitUrl);
  console.log(`Project directory not found, cloning from: ${gitUrl}`);

  // 确保基础目录存在
  if (!fs.existsSync(baseGithubDir)) {
    fs.mkdirSync(baseGithubDir, { recursive: true });
    console.log(`Created base directory: ${baseGithubDir}`);
  }

  // 克隆项目
  try {
    execSync(`git clone ${httpsUrl} ${projectDir}`, { stdio: 'inherit' });
    console.log(`✅ Successfully cloned project to: ${projectDir}`);

    // 配置克隆后的仓库的 git 用户信息
    try {
      const originalDir = process.cwd();
      process.chdir(projectDir);
      execSync('git config user.name "xujiazheng"', { stdio: 'ignore' });
      execSync('git config user.email "<EMAIL>"', { stdio: 'ignore' });
      console.log('✓ Git user configuration set for cloned repository');
      process.chdir(originalDir);
    } catch (configError) {
      console.log('Warning: Could not configure git user for cloned repository');
    }

    return projectDir;
  } catch (error) {
    throw new Error(`Failed to clone project: ${error.message}`);
  }
}

/**
 * 创建或切换到指定分支，并可选择更新npm包
 * @param {Object} options - 配置选项
 * @param {string} options.appName - 应用名称（将根据此名称从appGitMaps获取git地址并管理项目目录）
 * @param {string} [options.baseGithubDir] - github项目基础目录（默认为当前项目的github文件夹）
 * @param {string} options.branchName - 目标分支名称
 * @param {string} [options.baseBranch='master'] - 基础分支（当目标分支不存在时从此分支创建）
 * @param {boolean} [options.cleanWorkingDir=true] - 是否清理工作目录
 * @param {boolean} [options.updatePackages=false] - 是否更新npm包
 * @param {string} [options.packageName] - 要更新的npm包名称
 * @param {string} [options.packageVersion] - 要更新的npm包版本
 * @param {string} [options.clientDir='client'] - client目录名称
 * @param {string} [options.npmRegistry='http://registry.npm.qima-inc.com/'] - npm源地址
 * @param {boolean} [options.commitAndPush=true] - 是否提交并推送更改
 * @param {string} [options.commitMessage] - 自定义提交信息
 * @returns {Promise<Object>} 返回操作结果
 */
async function createOrSwitchBranch(options) {
  const {
    appName,
    baseGithubDir = path.join(__dirname, '../../../github'),
    branchName,
    baseBranch = 'master',
    cleanWorkingDir = true,
    updatePackages = false,
    packageName,
    packageVersion,
    clientDir = 'client',
    npmRegistry = 'http://registry.npm.qima-inc.com/',
    commitAndPush = true,
    commitMessage,
  } = options;

  // 验证必需参数
  if (!appName || !branchName) {
    throw new Error('appName and branchName are required');
  }

  // 确保 git 已安装
  try {
    installGit();
  } catch (error) {
    console.error('Git installation failed, but continuing...', error.message);
  }

  // 获取或克隆项目目录
  const gitDir = getOrCloneProjectDir(appName, baseGithubDir);

  const result = {
    success: false,
    currentBranch: null,
    targetBranch: branchName,
    operations: [],
  };

  try {
    // Change to the git directory
    process.chdir(gitDir);
    console.log(`Changed directory to: ${gitDir}`);
    result.operations.push(`Changed to directory: ${gitDir}`);

    // Check if we're in a git repository
    execSync('git rev-parse --is-inside-work-tree', { stdio: 'ignore' });

    // Configure git user name and email
    try {
      execSync('git config user.name "xujiazheng"', { stdio: 'ignore' });
      execSync('git config user.email "<EMAIL>"', { stdio: 'ignore' });
      console.log('✓ Git user configuration set');
      result.operations.push('Configured git user name and email');
    } catch (configError) {
      console.log('Warning: Could not configure git user, continuing...');
      result.operations.push('Warning: Git user configuration failed');
    }

    // Show current branch status
    const currentBranch = getCurrentBranch();
    result.currentBranch = currentBranch;
    console.log(`Current branch: ${currentBranch}`);
    console.log(`Target branch: ${branchName}`);

    // Check if we need to switch branches
    if (currentBranch === branchName) {
      console.log(`✓ Already on branch '${branchName}'`);
      if (isWorkingDirectoryClean()) {
        // Check if remote branch exists before pulling
        try {
          execSync(`git rev-parse --verify origin/${branchName}`, { stdio: 'ignore' });
          console.log('Working directory is clean, pulling latest changes...');
          execSync(`git pull origin ${branchName}`, { stdio: 'inherit' });
          result.operations.push(`Pulled latest changes from origin/${branchName}`);
        } catch (e) {
          console.log(`Remote branch origin/${branchName} doesn't exist, skipping pull`);
          result.operations.push('Skipped pull - remote branch does not exist');
        }
      } else if (cleanWorkingDir) {
        console.log('Working directory has changes, cleaning up first...');
        // Clean working directory first, then pull
        const status = execSync('git status --porcelain', { encoding: 'utf8' });
        if (status.trim()) {
          console.log('Found uncommitted changes:');
          console.log(status);
          console.log('Cleaning up uncommitted changes...');

          // Reset any staged and unstaged changes
          execSync('git reset --hard HEAD', { stdio: 'inherit' });
          console.log('✓ Reset all changes to last commit');
          result.operations.push('Reset uncommitted changes');

          // Remove untracked files and directories
          execSync('git clean -fd', { stdio: 'inherit' });
          console.log('✓ Removed untracked files and directories');
          result.operations.push('Removed untracked files');
        }

        // Check if remote branch exists before pulling
        try {
          execSync(`git rev-parse --verify origin/${branchName}`, { stdio: 'ignore' });
          console.log('Pulling latest changes...');
          execSync(`git pull origin ${branchName}`, { stdio: 'inherit' });
          result.operations.push(`Pulled latest changes from origin/${branchName}`);
        } catch (e) {
          console.log(`Remote branch origin/${branchName} doesn't exist, skipping pull`);
          result.operations.push('Skipped pull - remote branch does not exist');
        }
      } else {
        console.log('Working directory has changes, skipping pull (cleanWorkingDir=false)');
        result.operations.push('Skipped pull due to uncommitted changes');
      }
    } else {
      // Check for uncommitted changes and clean them up
      if (cleanWorkingDir) {
        console.log('Checking for uncommitted changes...');
        try {
          const status = execSync('git status --porcelain', { encoding: 'utf8' });
          if (status.trim()) {
            console.log('Found uncommitted changes:');
            console.log(status);
            console.log('Cleaning up uncommitted changes...');

            // Show what will be reset
            try {
              const diff = execSync('git diff --name-only', { encoding: 'utf8' });
              if (diff.trim()) {
                console.log('Modified files that will be reset:', diff.trim().split('\n'));
              }
            } catch (e) {
              // Ignore diff errors
            }

            // Reset any staged and unstaged changes
            execSync('git reset --hard HEAD', { stdio: 'inherit' });
            console.log('✓ Reset all changes to last commit');
            result.operations.push('Reset uncommitted changes');

            // Remove untracked files and directories
            try {
              const untracked = execSync('git ls-files --others --exclude-standard', { encoding: 'utf8' });
              if (untracked.trim()) {
                console.log('Untracked files that will be removed:', untracked.trim().split('\n'));
              }
            } catch (e) {
              // Ignore if no untracked files
            }

            execSync('git clean -fd', { stdio: 'inherit' });
            console.log('✓ Removed untracked files and directories');
            result.operations.push('Removed untracked files');

            console.log('✓ Working directory is now clean');
          } else {
            console.log('✓ Working directory is already clean');
          }
        } catch (error) {
          console.log('Warning: Could not check git status, continuing...');
        }
      }

      // Fetch latest changes from remote
      console.log('Fetching latest changes...');
      execSync('git fetch', { stdio: 'inherit' });
      result.operations.push('Fetched latest changes');

      // Check if branch exists locally or remotely
      const branchExists = () => {
        try {
          execSync(`git rev-parse --verify ${branchName}`, { stdio: 'ignore' });
          return true;
        } catch (e) {
          try {
            execSync(`git rev-parse --verify origin/${branchName}`, { stdio: 'ignore' });
            return true;
          } catch (e) {
            return false;
          }
        }
      };

      if (branchExists()) {
        // Branch exists, checkout to it
        console.log(`Branch ${branchName} exists, checking out...`);
        execSync(`git checkout ${branchName}`, { stdio: 'inherit' });
        result.operations.push(`Checked out existing branch: ${branchName}`);

        // Pull latest changes for existing branch
        console.log('Pulling latest changes...');
        execSync(`git pull origin ${branchName}`, { stdio: 'inherit' });
        result.operations.push(`Pulled latest changes from origin/${branchName}`);
      } else {
        // Branch doesn't exist, create from base branch
        console.log(`Branch ${branchName} doesn't exist, creating from ${baseBranch}...`);
        execSync(`git checkout ${baseBranch}`, { stdio: 'inherit' });
        execSync(`git pull origin ${baseBranch}`, { stdio: 'inherit' });
        execSync(`git checkout -b ${branchName}`, { stdio: 'inherit' });
        result.operations.push(`Created new branch: ${branchName} from ${baseBranch}`);
      }
    }

    // Update npm packages if requested
    if (updatePackages) {
      console.log('\n🔄 Starting npm package update...');

      // Validate package parameters
      if (!packageName || !packageVersion) {
        throw new Error('packageName and packageVersion are required when updatePackages is true');
      }

      const clientPath = path.join(gitDir, clientDir);

      // Check if client directory exists
      if (!fs.existsSync(clientPath)) {
        throw new Error(`Client directory not found: ${clientPath}`);
      }

      console.log(`Entering client directory: ${clientPath}`);
      process.chdir(clientPath);
      result.operations.push(`Changed to client directory: ${clientPath}`);

      // Set npm registry
      console.log(`Setting npm registry to: ${npmRegistry}`);
      execSync(`yarn config set registry ${npmRegistry}`, { stdio: 'inherit' });
      result.operations.push(`Set npm registry to: ${npmRegistry}`);

      // Update the specific package
      const packageSpec = packageVersion ? `${packageName}@${packageVersion}` : packageName;
      console.log(`Updating package: ${packageSpec}`);
      execSync(`yarn add ${packageSpec}`, { stdio: 'inherit' });
      result.operations.push(`Updated package: ${packageSpec}`);

      console.log('✅ Package update completed successfully!');

      // Commit and push changes if requested
      if (commitAndPush) {
        console.log('\n📤 Committing and pushing changes...');

        // Go back to git root directory
        process.chdir(gitDir);

        // Generate commit message
        const defaultCommitMessage = `feat: update ${packageName} to ${packageVersion || 'latest'}`;
        const finalCommitMessage = commitMessage || defaultCommitMessage;

        // Add package.json and yarn.lock changes
        const clientRelativePath = clientDir;
        execSync(`git add ${clientRelativePath}/package.json`, { stdio: 'inherit' });
        execSync(`git add ${clientRelativePath}/yarn.lock`, { stdio: 'inherit' });
        console.log('Added package.json and yarn.lock to git');
        result.operations.push('Added package files to git');

        // Check if there are changes to commit
        try {
          const status = execSync('git status --porcelain', { encoding: 'utf8' });
          if (status.trim()) {
            // Commit changes
            execSync(`git commit -m "${finalCommitMessage}"`, { stdio: 'inherit' });
            console.log(`Committed changes: ${finalCommitMessage}`);
            result.operations.push(`Committed: ${finalCommitMessage}`);

            // Push to remote
            execSync(`git push -u origin ${branchName}`, { stdio: 'inherit' });
            console.log(`✅ Successfully pushed to origin/${branchName}`);
            result.operations.push(`Pushed to origin/${branchName}`);
          } else {
            console.log('No changes to commit (package files unchanged)');
            result.operations.push('No changes to commit');
          }
        } catch (error) {
          console.log('Warning: Could not check git status for commit');
          result.operations.push('Warning: Git status check failed');
        }
      }
    }

    result.success = true;
    return result;
  } catch (error) {
    result.error = error.message;
    throw error;
  }
}

// 导出函数供其他模块使用
module.exports = {
  createOrSwitchBranch,
  getCurrentBranch,
  isWorkingDirectoryClean,
  getOrCloneProjectDir,
  installGit,
};

// 如果直接运行此文件，执行示例
if (require.main === module) {
  // 用户原始需求测试（完整功能）
  const defaultOptions = {
    appName: 'test',
    // baseGithubDir 使用默认值（当前项目的github文件夹）
    branchName: 'hotfix/test-hook-final',
    updatePackages: true,
    packageName: '@youzan/wsc-tee-trade-common',
    packageVersion: '2.5.7-beta.20250613105251.0',
    clientDir: 'client',
    commitAndPush: true,
    // 使用默认提交信息
  };

  // 执行函数
  createOrSwitchBranch(defaultOptions)
    .then(result => {
      console.log('\n✅ Branch operation completed successfully!');
      console.log('📊 Summary:');
      console.log(`   Current branch: ${result.currentBranch}`);
      console.log(`   Target branch: ${result.targetBranch}`);
      console.log(`   Operations performed: ${result.operations.length}`);
      result.operations.forEach((op, index) => {
        console.log(`   ${index + 1}. ${op}`);
      });
    })
    .catch(error => {
      console.error('\n❌ Branch operation failed:');
      console.error(`   Error: ${error.message}`);
      process.exit(1);
    });
}
