/**
 * 定制检测相关 jira推送到飞书， 每天 9 点到晚上 9 点每整点计算一遍。
 */
import schedule from 'node-schedule';
import { notify } from './notify';
import npmUpgrade from './npm-upgrade';

const  webhook = 'https://open.feishu.cn/open-apis/bot/v2/hook/80e76905-ea54-439f-bbfb-701a66fb7f9f'

function main(ctx) {
  // 注册路由，手动触发推送
  ctx.app.use(async (ctx, next) => {
    switch (ctx.path) {
      case '/webhook/npmpublish': {
        const postData = 
        // ctx.getPostData() || 
        {
          "user_name": "xujiazheng",
          "real_name": "徐嘉正",
          "alias_name": "徐嘉正",
          "repo_id": "221",
          "repo_name": "wsc-tee-trade-common",
          "repo_url": "https://gitlab.qima-inc.com/weapp/wsc-tee-trade-common",
          "branch_name": "hotfix/test-hook",
          "release_type": "1",
          "dist_tag": "beta",
          "semver": "prerelease",
          "modules": [
              {
                  "module_id": "643",
                  "module_name": "@youzan/order-domain-pc-components",
                  "version": "1.1.2-beta.20250612122915.0"
              }
          ]
        };
        npmUpgrade(postData).then(res => {
          console.log('res', res);
        })
        console.log('postData', postData);
         notify(webhook, {
          msg_type: 'text',
          content: {
            text: `测试npmpublish，postData: ${JSON.stringify(postData)}`,
          },
        });
        ctx.json(0, '', 'ok');
        break;
      }
      default:
        next();
    }
  });
}

export default main;
