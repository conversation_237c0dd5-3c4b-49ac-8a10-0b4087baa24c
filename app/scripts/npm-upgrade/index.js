/**
 * 定制检测相关 jira推送到飞书， 每天 9 点到晚上 9 点每整点计算一遍。
 */
import schedule from 'node-schedule';
import { notify } from './notify';
import npmUpgrade from './npm-upgrade';

const webhook = 'https://open.feishu.cn/open-apis/bot/v2/hook/80e76905-ea54-439f-bbfb-701a66fb7f9f';

// 生成通知文本
function generateNotificationText(result, originalData) {
  const { user_name, branch_name } = originalData;

  // 如果没有 results 数组，说明是早期失败（用户验证、分支验证等）
  if (!result.success && (!result.results || result.results.length === 0)) {
    return `❌ NPM包更新失败\n用户: ${user_name}\n分支: ${branch_name}\n错误: ${result.error || '未知错误'}`;
  }

  const { successfulOperations, failedOperations, results } = result;

  // 成功的更新
  const successResults = results.filter(r => r.success);
  const failedResults = results.filter(r => !r.success);

  // 根据整体结果决定标题
  const title = result.success ? '✅ NPM包更新完成' : '⚠️ NPM包更新部分完成';

  let text = `${title}\n`;
  text += `用户: ${user_name}\n`;
  text += `分支: ${branch_name}\n`;
  text += `总计: ${successfulOperations}成功 / ${failedOperations}失败\n\n`;

  if (successResults.length > 0) {
    text += `📦 成功更新的包:\n`;
    successResults.forEach(result => {
      text += `  • ${result.module_name}@${result.version} → ${result.appName}\n`;
    });
  }

  if (failedResults.length > 0) {
    text += `\n❌ 失败的更新:\n`;
    failedResults.forEach(result => {
      text += `  • ${result.module_name}@${result.version} → ${result.appName}\n`;
      text += `    错误: ${result.error}\n`;
    });
  }

  return text;
}

function main(ctx) {
  // 注册路由，手动触发推送
  ctx.app.use(async (ctx, next) => {
    switch (ctx.path) {
      case '/webhook/npmpublish': {
        const postData = ctx.getPostData();

        try {
          console.log('Received npm publish data:', postData);
          ctx.json(0, '', '请稍后...');
          const result = await npmUpgrade(postData);
          console.log('NPM upgrade result:', result);

          // 生成通知文本
          const notificationText = generateNotificationText(result, postData);

          // 发送通知
          await notify(webhook, {
            msg_type: 'text',
            content: {
              text: notificationText,
            },
          });

          console.log('Notification sent successfully');

        } catch (error) {
          console.error('Error processing npm publish:', error);

          // 发送错误通知
          await notify(webhook, {
            msg_type: 'text',
            content: {
              text: `❌ NPM包更新处理失败\n错误: ${error.message}\n数据: ${JSON.stringify(postData)}`,
            },
          });

          ctx.json(1, error.message, null);
        }

        break;
      }
      default:
        next();
    }
  });
}

export default main;
