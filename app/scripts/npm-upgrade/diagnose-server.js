const { execSync } = require('child_process');

// 诊断服务器环境
function diagnoseServerEnvironment() {
  console.log('🔍 Diagnosing server environment for git installation...\n');
  
  // 1. 检查操作系统
  console.log('1. Operating System Information:');
  try {
    const osInfo = execSync('uname -a', { encoding: 'utf8' });
    console.log('   OS:', osInfo.trim());
    
    // 检查发行版信息
    try {
      const release = execSync('cat /etc/os-release', { encoding: 'utf8' });
      console.log('   Release info:');
      release.split('\n').slice(0, 3).forEach(line => {
        if (line.trim()) console.log('   ', line);
      });
    } catch (e) {
      console.log('   /etc/os-release not found');
    }
  } catch (error) {
    console.log('   ❌ Cannot determine OS:', error.message);
  }
  
  // 2. 检查用户权限
  console.log('\n2. User Permissions:');
  try {
    const userId = execSync('id', { encoding: 'utf8' });
    console.log('   User ID:', userId.trim());
    
    const isRoot = process.getuid && process.getuid() === 0;
    console.log('   Running as root:', isRoot ? 'Yes' : 'No');
  } catch (error) {
    console.log('   ❌ Cannot check user permissions:', error.message);
  }
  
  // 3. 检查包管理器
  console.log('\n3. Package Managers Available:');
  const packageManagers = [
    { name: 'yum', command: 'which yum' },
    { name: 'apt-get', command: 'which apt-get' },
    { name: 'apk', command: 'which apk' },
    { name: 'brew', command: 'which brew' },
    { name: 'dnf', command: 'which dnf' },
  ];
  
  packageManagers.forEach(pm => {
    try {
      const path = execSync(pm.command, { encoding: 'utf8' }).trim();
      console.log(`   ✅ ${pm.name}: ${path}`);
    } catch (error) {
      console.log(`   ❌ ${pm.name}: Not found`);
    }
  });
  
  // 4. 检查 sudo 权限
  console.log('\n4. Sudo Availability:');
  try {
    execSync('which sudo', { stdio: 'ignore' });
    console.log('   ✅ sudo command available');
    
    // 尝试检查 sudo 权限（不实际执行）
    try {
      execSync('sudo -n true', { stdio: 'ignore' });
      console.log('   ✅ sudo permissions available (no password required)');
    } catch (error) {
      console.log('   ⚠️  sudo available but may require password');
    }
  } catch (error) {
    console.log('   ❌ sudo not available');
  }
  
  // 5. 检查 git 状态
  console.log('\n5. Git Status:');
  try {
    const gitVersion = execSync('git --version', { encoding: 'utf8' });
    console.log('   ✅ Git installed:', gitVersion.trim());
  } catch (error) {
    console.log('   ❌ Git not installed');
  }
  
  // 6. 检查网络连接
  console.log('\n6. Network Connectivity:');
  try {
    execSync('ping -c 1 8.8.8.8', { stdio: 'ignore' });
    console.log('   ✅ Internet connectivity available');
  } catch (error) {
    console.log('   ❌ No internet connectivity or ping blocked');
  }
  
  // 7. 检查 yum 仓库（如果 yum 可用）
  console.log('\n7. YUM Repository Status:');
  try {
    execSync('which yum', { stdio: 'ignore' });
    try {
      const repoList = execSync('yum repolist', { encoding: 'utf8', timeout: 10000 });
      console.log('   ✅ YUM repositories accessible');
      const enabledRepos = repoList.split('\n').filter(line => line.includes('enabled')).length;
      console.log(`   📦 Enabled repositories: ${enabledRepos}`);
    } catch (error) {
      console.log('   ❌ YUM repository access failed:', error.message);
    }
  } catch (error) {
    console.log('   ⏭️  YUM not available, skipping repository check');
  }
}

// 提供安装建议
function provideInstallationAdvice() {
  console.log('\n💡 Installation Recommendations:\n');
  
  console.log('Based on common server configurations:');
  console.log('');
  console.log('🔧 For CentOS/RHEL servers:');
  console.log('   sudo yum install -y git');
  console.log('   # or as root: yum install -y git');
  console.log('');
  console.log('🔧 For Ubuntu/Debian servers:');
  console.log('   sudo apt-get update && sudo apt-get install -y git');
  console.log('   # or as root: apt-get update && apt-get install -y git');
  console.log('');
  console.log('🔧 For Alpine Linux:');
  console.log('   apk add git');
  console.log('');
  console.log('🔧 Manual verification:');
  console.log('   git --version');
  console.log('');
  console.log('⚠️  Common issues:');
  console.log('   - Permission denied: Run as root or use sudo');
  console.log('   - Network issues: Check firewall and proxy settings');
  console.log('   - Repository issues: Update package lists first');
  console.log('   - Wrong package manager: Use the correct one for your OS');
}

function runDiagnosis() {
  console.log('🏥 Server Environment Diagnosis for Git Installation\n');
  console.log('=' .repeat(60));
  
  diagnoseServerEnvironment();
  provideInstallationAdvice();
  
  console.log('\n' + '='.repeat(60));
  console.log('📋 Please share this output to help diagnose the git installation issue.');
}

if (require.main === module) {
  runDiagnosis();
}

module.exports = { diagnoseServerEnvironment, provideInstallationAdvice };
