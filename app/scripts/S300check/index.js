/* eslint-disable max-len */
/**
 * 定制检测相关 jira推送到飞书， 每天 9 点到晚上 9 点每整点计算一遍。
 */
import schedule from 'node-schedule';

import { notify } from '../jira-push/webhook';
const { getJiraDataByJql } = require('./api');

function getJiraPushConfig() {
  const apolloClient = global.getRuntime().apolloClient;

  const result = apolloClient.getConfig({
    appId: 'ebiz-fe-platform',
    namespace: 'application',
    key: 'check-sjira',
  });
  return result;
}

async function getS300({ webhook, jql: jqlRetail, jqlAll: jqlAllRetail }) {
  try {
    const jiraRetailList = await getJiraDataByJql(jqlRetail);
    const jiraAllRetailList = await getJiraDataByJql(jqlAllRetail);

    const jiraWscList = await getJiraDataByJql(encodeURIComponent('project = ONLINE AND status in (OPEN, "IN PROGRESS", 待商家回复) AND labels in (S300) AND created >= -1d'));
    const jiraAllWscList = await getJiraDataByJql(encodeURIComponent('project = ONLINE AND labels = S300 AND created >= -1d'));

    const jiraList = { table: jiraRetailList.table.concat(jiraWscList.table) };
    const jiraAllList = { total: jiraAllRetailList.total + jiraAllWscList.total };

    const warnTips = (jiraList.table || [])
      .filter(item => item.key.includes('ONLINE'))
      .map(item => {
        return [
          {
            tag: 'a',
            text: `[${item.status}]${item.summary}`,
            href: `https://jira.qima-inc.com/browse/${item.key}`,
          },
        ];
      });
    if (!warnTips.length) {
      return;
    }
    const rate = parseInt(((jiraAllList.total - warnTips.length) / jiraAllList.total) * 100);
    const total = jiraAllList.total;
    const processingTotal = warnTips.length;
    // eslint-disable-next-line max-len
    const processingUncontainAsk = (jiraList.table || []).filter(item => item.key.includes('ONLINE') && item.status !== '待商家回复').length;
    const rateUncontainAsk = parseInt(((total - processingUncontainAsk) / total) * 100);
    const result = {
      msg_type: 'post',
      content: {
        post: {
          zh_cn: {
            // eslint-disable-next-line max-len, prettier/prettier
          title: `S300 当前SLA: ${rate}% -> 总数：${total}；未解决：${processingTotal}；（剔除待商家回复SLA：${rateUncontainAsk}%）`,
            content: warnTips,
          },
        },
      },
    };
    notify(webhook, result);
  } catch (error) {
    notify(webhook, {
      msg_type: 'text',
      content: {
        text: (error && error.message) || '',
      },
    });
  }
}

function main() {
  const rule = new schedule.RecurrenceRule();
  rule.dayOfWeek = [1, 2, 3, 4, 5];
  rule.hour = [9, 14];
  // 9 点是为了看昨天一天是否还有剩余，有的话补救一下
  // 14 点是为了周三周五家庭日催一下快过期的
  rule.minute = [0];
  if (process.env.NODE_ENV.toLocaleLowerCase() === 'prod') {
    schedule.scheduleJob(rule, () => {
      getS300(getJiraPushConfig());
    });
  }
}

export default main;
