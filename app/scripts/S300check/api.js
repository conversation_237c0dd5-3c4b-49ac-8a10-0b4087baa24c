const fetch = require('node-fetch');

function getJiraDataByJql(jql) {
  return fetch('https://jira.qima-inc.com/rest/issueNav/1/issueTable', {
    headers: {
      'accept-language': 'zh-CN,zh;q=0.9',
      'cache-control': 'no-cache',
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
      pragma: 'no-cache',
      'x-atlassian-token': 'no-check',
      'x-requested-with': 'XMLHttpRequest',
      cookie:
        // eslint-disable-next-line max-len
        'yz_log_uuid=d182fb53-aa9b-2f00-1aad-d151588173df; yz_log_ftime=1695722149404; cas_username=lvdada; access_user=2102_1; jira.editor.user.mode=wysiwyg; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2218c90a017e811c8-08d00d0d474c6c-1f525637-1296000-18c90a017e9ee6%22%2C%22%24device_id%22%3A%2218c90a017e811c8-08d00d0d474c6c-1f525637-1296000-18c90a017e9ee6%22%2C%22props%22%3A%7B%22%24latest_referrer%22%3A%22https%3A%2F%2Fqima.feishu.cn%2F%22%2C%22%24latest_referrer_host%22%3A%22qima.feishu.cn%22%2C%22%24latest_traffic_source_type%22%3A%22%E5%BC%95%E8%8D%90%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC%22%7D%7D; loc_dfp=b8886e8e5654a31d2fdfd7cb2ebb3b8b; dfp=b2038f3c2ceed5d81aa6f605bf8cfd22; cas=015834029a721b063f30edd757f95813b073a1533204cc257f672578cd3fa21d273; KDTSESSIONID=YZ1190311836086362113YZVXn7xhqR; oa.koa.sid=KCmifEQuvDzjGJ7A86B0a0whwjh64mUk; oa.koa.sid.sig=kMoBpE_yRR3KpM3UQaFJDcODsgI; iamp.sid=1jTP+JE0J9dHZePAPdR63sA1xvbphqVC3tgpf7M22izxoM0dz9rRSlJYMzsVhgz16eHdEufMphB6sThOMOAK70flK1QDIIUQy7mWcdnrKSX88iQl/H5x8+/KhiwkgNzkLARUYAVcULeyTCDvrAthtHszdX6k3BdnqRg1T0weKjfPRgDSm4BmQJgUH/oNPaV+; JSESSIONID=CF24712FF6BD8FD3C05546508B54A365; seraph.rememberme.cookie=181826%3A450f21d8cc85834f3027df5e655c2dd50ef228c6; atlassian.xsrf.token=BLB1-PN5V-28RZ-GHSI_515ae78a442c0ee4bc7e3cbda657efd02394433a_lin; TSID=2ecac70f17a144fbbc040b3ffa92da37; yz_log_seqn=2',
    },
    body: `startIndex=0&jql=${jql}&layoutKey=split-view`,
    method: 'POST',
  })
    .then(res => res.json())
    .then(res => {
      return res.issueTable;
    });
}

module.exports = {
  getJiraDataByJql
};
