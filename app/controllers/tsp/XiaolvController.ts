import { IYouzanFrameworkContext } from '@youzan/youzan-framework/definitions';
import BaseController from '../base/BaseController';
import ManPowerService from '../../services/api/xiaolv/ManPowerService';

export = class XiaolvController extends BaseController {
  /**
   * 每周五提醒大家拖动任务
   * @param ctx ctx
   */
  async notifyUnReachedManPowerGoal(ctx: IYouzanFrameworkContext) {
    const ret = await new ManPowerService(ctx).notifyUnReachedManPowerGoal();
    ctx.json(0, 'ok', ret);
    return;
  }

  /**
   * 人力投入接口未开放，写死的cookie，所以需要定位保活，使用cookie生效
   * @param ctx ctx
   */
  async keepPersenalAlive(ctx: IYouzanFrameworkContext) {
    const ret = await new ManPowerService(ctx).keepAlive();
    ctx.json(0, 'ok', ret);
    return;
  }
};
