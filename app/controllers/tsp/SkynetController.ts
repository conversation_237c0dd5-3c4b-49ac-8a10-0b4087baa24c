import { IYouzanFrameworkContext } from '@youzan/youzan-framework/definitions';
import subDays from 'date-fns/sub_days';
import format from 'date-fns/format';
import startNodeLogs from '@youzan/skynet-log/src/core/start';
import BaseController from '../base/BaseController';
import DemandApiService from '../../services/api/xiaolv/DemandApi';

interface IError {
  name: string;
  cnt: number;
  feMethod: string;
  errorType: string;
  weight: number;
  isTimeout: boolean;
  isUnknown: boolean;
  isMagical: boolean;
  noActive: boolean;
  referer: string;
  checkTip: string;
  apiMethod: string;
  rootIds: Array<string>;
}

function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export = class SkynetController extends BaseController {
  async getSkynetErrorLogsAndCreateXiaolvIssue(ctx: IYouzanFrameworkContext) {
    const { appName, logIndex } = ctx.getPostData();
    const now = new Date();
    const startTime = subDays(now, 1);
    const { content } = await startNodeLogs({
      start: format(startTime, 'YYYY-MM-DD HH:mm:ss'),
      end: format(now, 'YYYY-MM-DD HH:mm:ss'),
      skynetProject: appName || 'wsc-h5-salesman',
      logIndexName: logIndex || 'wsc_salesman_log',
      cache: false,
    });
    const top5Error: Array<IError> = content.slice(0, 5);
    // 效率接口问题，并发请求会造成死锁。等他们改了再优化下。。
    const data: number[] = [];
    const len = top5Error.length;
    await Promise.all(
      top5Error.map((err, index) => {
        const param = {
          name: err.name,
          description: `
          httpUrl: ${err.feMethod}
          errorType: ${err.errorType}
          count: ${err.cnt}
          referer: ${err.referer}
          search: ${err.checkTip}
          error: ${err.apiMethod},
          rootIds: ${err.rootIds.join(',\n')}
        `,
          category: 1,
          ownerOaId: 1882,
          creatorOaId: 887,
          business: 78,
          otherBusiness: [78],
          tagIds: [215],
        };
        return (async function() {
          try {
            const result = await new DemandApiService(ctx).createDailyDemand(param);
            data.push(result);
            // 等500ms，不然效率的接口会死锁。。
            if (index < len - 1) {
              await sleep(500);
            }
          } catch (e) {
            console.log(e); // eslint-disable-line
          }
        })();
      })
    );
    ctx.json(0, 'ok', data);
  }
};
