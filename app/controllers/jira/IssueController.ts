import BaseController from '../base/BaseController';
import JiraIssueService from '../../services/api/jira/IssueService';

import { IYouzanFrameworkContext } from '@youzan/youzan-framework/definitions';
import { ICreateSentryIssueQuery, ICreateIssueData } from 'definitions/jira/Issue';

export = class IssueController extends BaseController {
  async postCreateSentryIssueJson(ctx: IYouzanFrameworkContext) {
    const postData: ICreateSentryIssueQuery = ctx.getPostData();
    const data = this.processCreateSentryIssueData(postData);
    const result = await new JiraIssueService(ctx).createIssue(data);
    ctx.json(0, 'ok', result);
  }

  async postBatchCreateSentryIssueJson(ctx: IYouzanFrameworkContext) {
    const postData: ICreateSentryIssueQuery[] = ctx.getPostData();
    const data = postData.map(this.processCreateSentryIssueData);
    const result = await new JiraIssueService(ctx).batchCreateIssue(data);
    ctx.json(0, 'ok', result);
  }

  private processCreateSentryIssueData(data: ICreateSentryIssueQuery): ICreateIssueData {
    const { summary, description, path } = data;
    // TODO：先写死，后续改成界面配置
    const assigneeMap: { [index: string]: string } = {
      '教务、督学': 'lvdada',
      '课程商品、下单': 'xiaorui',
      '营销、线索': 'wangshaojuan',
    };
    const assignee = assigneeMap[path] || 'lvdada';

    return {
      issuetypeId: '10600',
      projectKey: 'EBIZSENTRY',
      reporter: 'qianyongjun',
      priorityId: '3',
      assignee,
      summary,
      description,
    };
  }
};
