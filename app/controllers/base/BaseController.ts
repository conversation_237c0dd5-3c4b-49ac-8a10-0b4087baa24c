import { YouzanBaseController } from '@youzan/youzan-framework';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';

export = class BaseController extends YouzanBaseController {
  toCamelCase<T>(data: T, deep?: boolean): T {
    if (Array.isArray(data)) {
      return (data.map(o => {
        return mapKeysToCamelCase({ ...o }, deep);
      }) as unknown as T);
    } else if (Object.prototype.toString.call({ ...data }) === '[object Object]') {
      return mapKeysToCamelCase({ ...data }, deep);
    } else {
      return data;
    }
  }
};
