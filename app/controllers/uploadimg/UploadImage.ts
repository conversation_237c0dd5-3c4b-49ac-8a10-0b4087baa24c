import BaseController from '../base/BaseController';
import { IYouzanFrameworkContext } from '@youzan/youzan-framework/definitions';
import fs from 'fs';
import path from 'path';
import mime from 'mime-types';

const pathRoot = path.join(process.cwd(), 'tmp/wxworkLoginQRCodes');

export = class UploadImageController extends BaseController {
  public async list(ctx: IYouzanFrameworkContext) {
    const files = fs.readdirSync(pathRoot);

    const urls = files.map(f => {
      return `/upload/iamges/wxworkLoginQRCodes/${f}`;
    });

    ctx.json(0, 'ok', urls);
    return;
  }

  public async upload(ctx: IYouzanFrameworkContext) {
    const {
      files: { wxworkLoginQRCode },
    } = ctx.request.body || {};

    const savePath = path.join(pathRoot, wxworkLoginQRCode.name);

    try {
      fs.accessSync(pathRoot);
    } catch {
      fs.mkdirSync(pathRoot, { recursive: true });
    }

    fs.copyFileSync(wxworkLoginQRCode.path, savePath);

    ctx.json(0, 'ok', 1);
  }

  /**
   * images/xxx/xxx.jpg
   * @param ctx
   */
  public async images(ctx: IYouzanFrameworkContext) {
    const name = path.basename(ctx.params[0]);

    const filepath = path.join(pathRoot, name);

    try {
      fs.accessSync(filepath);
      const mimeType = mime.lookup(filepath) || '';
      ctx.res.setHeader('Content-Type', mimeType);
      ctx.body = fs.createReadStream(filepath);
    } catch {
      return;
    }
  }
};
