import { IYouzanFrameworkContext } from '@youzan/youzan-framework/definitions';
import ParamsError from '@youzan/youzan-framework/app/exceptions/ParamsError';
import { isPlainObject } from 'lodash';
import BaseController from '../base/BaseController';
import WechatWorkService from '../../services/api/wechatwork/WechatWorkService';
import { apps } from '../../constants/wechatwrok';

export = class IndexController extends BaseController {
  // 健康打卡
  async postHelthPunchJson(ctx: IYouzanFrameworkContext) {
    const news: object = {
      msgtype: 'news',
      news: {
        articles: [
          {
            title: '点击卡片可快速进入健康打卡页面',
            url: 'https://cloud.italent.cn/HealthCard/#/indexPage?metaObjName=HealthCard.HealthCardRecord&_k=eg0n7m',
            picurl: 'https://img.yzcdn.cn/upload_files/2020/02/25/FiN8kQ6EveRRqX9crKh0oY-IG63I.png',
          },
        ],
      },
    };
    const text: object = {
      msgtype: 'text',
      text: {
        content: '大家花一分钟时间快速完成一下健康打卡吧',
        mentioned_list: ['@all'],
      },
    };
    const serviceInstance = new WechatWorkService(ctx);
    let result = await serviceInstance.webhookMessageSend(news, 'frontjinfeng');
    result = await serviceInstance.webhookMessageSend(news, 'ebiz');
    result = await serviceInstance.webhookMessageSend(text, 'frontjinfeng');
    ctx.json(0, 'ok', result);
  }

  // 消息通知
  async personalMessageJson(ctx: IYouzanFrameworkContext) {
    const serviceInstance = new WechatWorkService(ctx);
    const tokenRes = await serviceInstance.getToken(apps.personMessage.corpsecret);
    const token = tokenRes.access_token;

    const message = ctx.getPostData() || {};

    if (!isPlainObject(message)) {
      throw new ParamsError(10001, '消息必须是一个对象');
    }
    if (!message.msgtype) {
      throw new ParamsError(10001, '请指定消息类型');
    }
    if (message.touser && message.touser === '@all') {
      throw new ParamsError(10001, '禁止全员发送消息');
    }

    message.agentid = apps.personMessage.agent_id;

    const msgRes = await serviceInstance.appMessageSend(token, message);
    ctx.json(0, 'ok', msgRes);
  }
};
