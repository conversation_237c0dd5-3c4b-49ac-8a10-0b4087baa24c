import BaseController from '../base/BaseController';
import { IYouzanFrameworkContext } from '@youzan/youzan-framework/definitions';
import ReadmeSearchService from '@services/readme-nav/ReadmeSearchService';
import { curlRequest, parseProjectAggregations, parseQuery } from '@utils/es-utils';
const ES_URL = 'http://es-proxy-common.qa.s.qima-inc.com/sync/search/ebiz_readme';

export = class ReadmeController extends BaseController {
  // 获取readme列表
  public async getReadmeList(ctx: IYouzanFrameworkContext) {

    const { type, business_domain, project, keyword, module, pagination = {} } = parseQuery(ctx.request.query[0]) || {};
    const { current = 1, pageSize = 20 } = pagination;
    const mustCheck: any[] = [];
    const shouldCheck: any[] = [];
    // 组件类型
    if (type) {
      mustCheck.push({
        type: 1,
        equalQuery: {
          field: 'type',
          value: type,
        },
      });
    }

    if (keyword) {
      ['title', 'content', 'tag', 'business_domain'].map(keywordItem => {
        shouldCheck.push({
          type: 4,
          matchQuery: {
            field: keywordItem,
            value: keyword,
          },
        });
      });
    }

    // 业务域名称
    if (business_domain) {
      mustCheck.push({
        type: 1,
        equalQuery: {
          field: 'business_domain',
          value: business_domain,
        },
      });
    }

    if (module) {
      mustCheck.push({
        type: 1,
        equalQuery: {
          field: 'module',
          value: module,
        },
      });
    }

    // 工程名称
    if (project) {
      mustCheck.push({
        type: 1,
        equalQuery: {
          field: 'project',
          value: project,
        },
      });
    }

    const result = await new ReadmeSearchService(ctx).search({
      sourceName: 'ebiz_readme',
      pageRequest: {
        pageNumber: current,
        pageSize,
        sort: {
          orders: [
            {
              direction: 'DESC',
              property: '_score',
            },
          ],
        },
      },
      fetchedFields: ['cover', 'tag', 'title', 'description', 'id', 'project', 'module'],
      query: {
        withQueries: mustCheck,
        orQueries: shouldCheck,
      },
    });
    ctx.json(0, 'ok', result);
  }

  // 获取工程列表 
  // TODO: 由于los接口目前还在改进，暂时不支持多个字段聚合，故此接口暂时无法调用los接口，直接访问es
  async getProjectList(ctx: IYouzanFrameworkContext) {
    const result: any = await curlRequest(
      ES_URL,
      JSON.stringify({
        aggs: {
          unique_project: {
            terms: {
              field: 'project',
            },
            aggs: {
              unique_type: {
                terms: {
                  field: 'type',
                },
              },
            },
          },
        },
        size: 0,
      })
    );

    const data = parseProjectAggregations(JSON.parse(result.data.aggregations));
    ctx.json(0, 'ok', data);
  }

  // 获取业务域列表
  // TODO: 由于los接口目前还在改进，暂时不支持多个字段聚合，故此接口暂时无法调用los接口，直接访问es
  async getBusinessDomain(ctx: IYouzanFrameworkContext) {
    const { project } = ctx.request.body || {};
    if (!project) {
      // eslint-disable-next-line no-throw-literal
      throw '必须指定工程名称';
    }
    const mustCheck = [];
    mustCheck.push({ term: { project } });
    const result: any = await curlRequest(
      ES_URL,
      JSON.stringify({
        size: 0,
        query: {
          bool: {
            must: mustCheck,
          },
        },
        aggs: {
          unique_project: {
            terms: {
              field: 'business_domain',
            },
            aggs: {
              unique_type: {
                terms: {
                  field: 'module',
                },
              },
            },
          },
        },
      })
    );

    const data = parseProjectAggregations(JSON.parse(result.data.aggregations));
    ctx.json(0, 'ok', data);
  }

  // 查看readme详情
  public async getReadmeDetail(ctx: IYouzanFrameworkContext) {
    const { id } = parseQuery(ctx.request.query[0]) || {};
    // parseQuery(ctx.query[0]);
    if (!id) {
      // eslint-disable-next-line no-throw-literal
      throw '未指定组件文档id';
    }
    const result = await new ReadmeSearchService(ctx).search({
      sourceName: 'ebiz_readme',
      pageRequest: {
        pageNumber: 1,
        pageSize: 10,
        sort: {
          orders: [
            {
              direction: 'DESC',
              property: '_score',
            },
          ],
        },
      },
      query: {
        withQueries: [
          {
            type: 1,
            equalQuery: {
              field: 'id',
              value: id,
            },
          },
        ],
      },
    });
    ctx.json(0, 'ok', result);
  }
};
