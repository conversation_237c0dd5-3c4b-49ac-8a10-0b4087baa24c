import BaseController from '../base/BaseController';
import ShareArticleCategoryService from '@services/share-article/ShareArticleCategory';
import ShareArticleCategoryModel from '@models/share-article/ShareArticleCategory';
import { IYouzanFrameworkContext } from '@youzan/youzan-framework/definitions';
import { PageInfo } from '@definitions/base';

export = class ArticleController extends BaseController {
  public async list(ctx: IYouzanFrameworkContext) {
    const query: PageInfo = ctx.request.query || {};
    if (!query.page) query.page = 1;
    if (!query.pageSize) query.pageSize = 20;

    const result = await new ShareArticleCategoryService(ctx).getAll(query);

    // 避免机器人地址被人获取到，隐藏部分关键信息
    result.dataset.forEach(item => {
      item.wechatworkRobot = item.wechatworkRobot.replace(/key=(\w*)-(.*)-(\w*)$/, '$1-****-$3');
    });

    ctx.json(0, 'ok', result);
    return;
  }

  public async create(ctx: IYouzanFrameworkContext) {
    const body: ShareArticleCategoryModel = ctx.request.body;

    this.validator.required(body.category, 'category不能为空').required(body.wechatworkRobot, 'wechatworkRobot不能为空');

    const result = await new ShareArticleCategoryService(ctx).create(body);
    ctx.json(0, 'ok', result);
  }

  public async update(ctx: IYouzanFrameworkContext) {
    const body: ShareArticleCategoryModel = ctx.request.body;

    this.validator.required(body.id, 'id不能为空');

    const result = await new ShareArticleCategoryService(ctx).update(body);
    ctx.json(0, 'ok', result);
  }

  public async delete(ctx: IYouzanFrameworkContext) {
    const { id } = ctx.request.body || {};
    this.validator.required(id, 'id不能为空').isNumeric(id, 'id必须为数字');

    const result = await new ShareArticleCategoryService(ctx).delete(id);
    ctx.json(0, 'ok', result);
  }
};
