import BaseController from '../base/BaseController';
import ShareArticleService from '@services/share-article/ShareArticle';
import ShareArticleCategoryService from '@services/share-article/ShareArticleCategory';
import ShareArticleModel from '@models/share-article/ShareArticle';
import { IYouzanFrameworkContext } from '@youzan/youzan-framework/definitions';
import { PageInfo } from '@definitions/base';
import WechatWorkService from '@services/api/wechatwork/WechatWorkService';
import ServiceError from '@youzan/youzan-framework/app/exceptions/ServiceError';

export = class ArticleController extends BaseController {
  public async list(ctx: IYouzanFrameworkContext) {
    const query: PageInfo = ctx.request.query || {};
    if (!query.page) query.page = 1;
    if (!query.pageSize) query.pageSize = 20;

    const result = await new ShareArticleService(ctx).getAll(query);
    ctx.json(0, 'ok', result);
    return;
  }

  public async create(ctx: IYouzanFrameworkContext) {
    const body: ShareArticleModel = ctx.request.body;

    this.validator
      .required(body.title, 'title不能为空')
      .required(body.url, 'url不能为空')
      .required(body.picture, 'picture不能为空')
      .required(body.category, 'category不能为空');

    const categroy = await new ShareArticleCategoryService(ctx).getAll({ category: body.category, page: 1, pageSize: 1 });
    if (!categroy.dataset.length) {
      throw new ServiceError(11111, '分类不存在');
    }

    const wxRobotUrl: string = categroy.dataset[0].wechatworkRobot;
    const promises = [];
    promises.push(
      new WechatWorkService(ctx).webhookMessageSend(
        {
          msgtype: 'news',
          news: {
            articles: [
              {
                title: body.title,
                url: body.url,
                picurl: body.picture,
              },
            ],
          },
        },
        wxRobotUrl
      )
    );

    promises.push(new ShareArticleService(ctx).create(body));
    const [wxRes, service] = await Promise.all(promises);
    ctx.json(0, 'ok', service);
  }

  public async update(ctx: IYouzanFrameworkContext) {
    const body: ShareArticleModel = ctx.request.body;

    this.validator.required(body.id, 'id不能为空');

    const result = await new ShareArticleService(ctx).update(body);
    ctx.json(0, 'ok', result);
  }

  public async delete(ctx: IYouzanFrameworkContext) {
    const { id } = ctx.request.body || {};
    this.validator.required(id, 'id不能为空').isNumeric(id, 'id必须为数字');

    const result = await new ShareArticleService(ctx).delete(id);
    ctx.json(0, 'ok', result);
  }
};
