export interface IRule {
  id: number;
  ruleName: string;
  appKey: string;
  ruleDesc?: string;
  ruleLevel: number; // 0:info 1:warn 2:error
  isRun: 0 | 1;
  mediaType: 'class' | 'audio' | 'video';
  // 触发规则
  rulesType: IRuleType; // 1 全包含 2 满足其一
  rules: {
    target: string; // error
    granularity: string; // all user_id user_id&host_id kdt_id host_id
    matchRule: [number, number, string];
    subMatchRule: [number, string, number, string][]; // [[2, 'user', 10, '>=']]
  }[];
  notice: {
    time: number[];
    qiwei: string;
    frequency: [number, number]; // 15分钟内最多 10 次
    owner: string[];
  };
}