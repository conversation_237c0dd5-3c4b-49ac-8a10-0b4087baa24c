import BaseController from '../base/BaseController';
import AlertService from '@services/media-log/alert';
import format from 'date-fns/format';
import { IYouzanFrameworkContext } from '@youzan/youzan-framework/definitions';
import WechatWorkService from '@services/api/wechatwork/WechatWorkService';
import AlertRuleService from '@services/media-log/alert-rule';
import AlertHistoryService from '@services/media-log/alert-history';
import { PageInfo } from '@definitions/base';
import { IRule } from './type';
import { transformAlertHistoryToWeb, transformToWeb } from './utils';
import AlertRule from '@models/media-log/alertRule';
import {
  DEMENSION_MAP,
  GRANULARITY,
  GRANULARITY_TEXT_MAP,
  RULES_TYPE_MAP,
  RULE_COMPARE_MAP,
  RULE_LEVEL,
  TARGETV2_TEXT_MAP,
  TARGET_UNIT
} from '@constants/media-log';

interface IListQuery {
  mediaType: number;
  target: string;
  osType: string;
  appKey: string;
  startTime: number;
  endTime: number;
}

type IListClassQuery = IListQuery & { sdkVersion: string };

const timeFrequencyMap: Record<number, { count: number; init: number }> = {};

enum IRuleType {
  AND = 1,
  OR = 2,
}

// 先把这个报错频率存在本地
// 可能会因为有多台机器 会有问题
const checkLimitFrequency = (rule: IRule) => {
  const id = rule.id;
  const frequency = rule.notice.frequency;
  const frequencyTime = frequency[0] * 60 * 1000;
  const frequencyCount = frequency[1];

  if (!timeFrequencyMap[id]) {
    timeFrequencyMap[id] = {
      count: 1,
      init: Date.now(),
    };
    return true;
  } else {
    const count = timeFrequencyMap[id].count;
    const initTime = timeFrequencyMap[id].init;

    if (Date.now() - initTime < frequencyTime && count >= frequencyCount) {
      return false;
    } else if (Date.now() - initTime < frequencyTime && count < frequencyCount) {
      timeFrequencyMap[id].count = timeFrequencyMap[id].count + 1;
      return true;
    } else if (Date.now() - initTime >= frequencyTime && count >= frequencyCount) {
      timeFrequencyMap[id].count = 1;
      timeFrequencyMap[id].init = Date.now();
      return false;
    } else if (Date.now() - initTime >= frequencyTime && count < frequencyCount) {
      timeFrequencyMap[id].count = 1;
      timeFrequencyMap[id].init = Date.now();
      return true;
    } else {
      return false;
    }
  }
};

const genAlertContent = (
  rule: IRule,
  ruleDetails: {
    inTarget: boolean;
    result: { rule: []; subRule: [] };
  }[]
) => {
  const title = '## 多媒体天眼报警，请相关同事注意。 \n';
  const time = `> 时间：<font color="comment">${format(Date.now(), 'YYYY-MM-DD HH:mm:ss')}</font>\n`;
  const alertLevel = `> 报警等级：<font color="${rule.ruleLevel === RULE_LEVEL.ERROR ? 'warning' : 'comment'}">warn</font>\n`;
  const checkMore = `> 查看详情: 即将上线\n`;
  const owner = `> 负责人：${rule.notice.owner.map(owner => `<@${owner}>`).join()} \n`;
  const ruleType = `> 媒体类型：<font color="comment">${rule.mediaType}</font> \n`;
  const ruleName = `> 命中规则名称：<font color="info">${rule.ruleName}</font>\n`;
  const ruleDesc = `> 命中规则描述：<font color="comment">${rule.ruleDesc}</font>\n`;
  const ruleDetail = `> 规则详情： 该报警共有${rule.rules.length}条子规则，${RULES_TYPE_MAP[rule.rulesType]}即触发，请关注！\n\n`;
  const rulesTitle = `子规则详情：\n`;

  const rules = rule.rules
    .map((oRule, index) => {
      const granularityText =
        oRule.granularity === GRANULARITY.ALL
          ? '全网'
          : `${ruleDetails[index].result.rule
              .map(oResultRule => oResultRule[oRule.granularity])
              .slice(0, 3)
              .join()} ${GRANULARITY_TEXT_MAP[oRule.granularity]}`;

      const isTarget = `【${ruleDetails[index].inTarget ? '命中' : '未命中'}】`;
      const indexText = `${index + 1}、`;
      const timeDuration = `在${oRule.matchRule[0]}分钟内，`;
      const granularity = `${granularityText}的`;
      const targetText = `${TARGETV2_TEXT_MAP[rule.mediaType][oRule.target]}`;
      const operator = `${RULE_COMPARE_MAP[oRule.matchRule[2]]}`;
      const matrix = `${oRule.matchRule[1]}`;
      const unit = `${TARGET_UNIT[oRule.target]}`;

      const subRules = oRule.subMatchRule
        .map(oSubRule => {
          return ` 且 ${DEMENSION_MAP[oSubRule[1]]}${RULE_COMPARE_MAP[oSubRule[3]]} ${oSubRule[2]} 个`;
        })
        .join('');

      return `- ${isTarget}${indexText}${timeDuration}${granularity}${targetText}${operator}${matrix}${unit} ${subRules} \n`;
    })
    .join('');

  return [title, time, alertLevel, checkMore, owner, ruleType, ruleName, ruleDesc, ruleDetail, rulesTitle, rules].join('');
};

export = class AlertController extends BaseController {
  private async postWechatMessage(
    rule: IRule,
    ruleDetails: {
      inTarget: boolean;
      result: any;
    }[],
    alertHistoryId: number
  ) {
    const message = {
      msgtype: 'markdown',
      markdown: {
        mentioned_list: rule.notice.owner,
        content: genAlertContent(rule, ruleDetails),
      },
    };

    this.ctx.localLog('log', {
      msg: 'alertMessage',
      data: { ruleDetails, rule, alertHistoryId, content: message.markdown.content },
    });

    const wechatworkService = new WechatWorkService(this.ctx);
    const result = await wechatworkService.webhookMessageSend(message, rule.notice.qiwei);
  }

  private async createAlertHistory(
    rule: IRule,
    ruleDetails: {
      inTarget: boolean;
      result: any;
    }[]
  ) {
    return await new AlertHistoryService(this.ctx).create({
      rule_details: JSON.stringify(ruleDetails),
      rule: JSON.stringify(rule),
      rule_id: rule.id,
      rule_level: rule.ruleLevel,
      app_key: rule.appKey,
      media_type: rule.mediaType,
    });
  }

  public async list(ctx: IYouzanFrameworkContext) {
    // 查到当前的报警规则列表
    // 异步批量调用 alert service
    // 拿到结果后根据规则计算 调用 公司 alert 服务（先选择 qiwei 机器人

    // ruleExample.forEach
    const engineMap = {
      audio: (config: IRule) => {
        return new AlertService(ctx).findAudio(config);
      },
      video: (config: IRule) => {
        return new AlertService(ctx).findVideo(config);
      },
      class: (config: IRule) => {
        return new AlertService(ctx).findClass(config);
      },
    };

    const ruleListDataset = await new AlertRuleService(ctx).getAll({
      page: {
        page: 1,
        pageSize: 1000,
      },
    });

    const ruleLists = ruleListDataset.dataset.map(o => transformToWeb(o)) as IRule[];

    // 先根据 isRun 和通知时间做前置的过滤
    const filterRules = ruleLists.filter(rule => {
      return rule.isRun &&
      (rule.notice.time.length > 1 ? Date.now() > rule.notice.time[0] && Date.now() < rule.notice.time[1] : true);
    });

    filterRules.forEach(async rule => {
      const results = await engineMap[rule.mediaType](rule);
      // alert
      this.ctx.localLog('log', {
        msg: 'alert result rule.id',
        data: JSON.stringify({ id: rule.id, results }),
      });

      // 这个任务的每个规则结果
      const ruleDetails = results.map((result, resultIndex) => {
        // 说明设置过 subRule 那么需要父子查询全命中
        if (rule.rules[resultIndex].subMatchRule.length) {
          return {
            inTarget: !!result.rule.length && result.subRule.every(o => o && o.length),
            result: {
              rule: result.rule,
              subRule: result.subRule,
            },
          };
        } else {
          return {
            inTarget: !!result.rule.length,
            result: {
              rule: result.rule,
              subRule: result.subRule,
            },
          };
        }
      });

      this.ctx.localLog('log', {
        msg: 'ruleDetails',
        data: JSON.stringify({ ruleDetails }),
      });

      let ruleCanAlertFromData = false;

      if (rule.rulesType === IRuleType.AND) {
        ruleCanAlertFromData = ruleDetails.every(o => o.inTarget);
      } else {
        ruleCanAlertFromData = ruleDetails.some(o => o.inTarget);
      }

      if (ruleCanAlertFromData) {
        this.ctx.localLog('log', {
          msg: 'ruleCanAlertFromData',
          data: { ruleCanAlertFromData },
        });

        if (checkLimitFrequency(rule)) {
          // alert
          this.ctx.localLog('log', {
            msg: 'checkLimitFrequency',
            data: true,
          });
          const createHistoryId = await this.createAlertHistory(rule, ruleDetails);
          await this.postWechatMessage(rule, ruleDetails, createHistoryId);
        }
      }
    });

    // 无需等待返回
    ctx.json(0, 'ok', true);
  }

  public async listAlertHistory(ctx: IYouzanFrameworkContext) {
    const query: {
      page: PageInfo;
      appKey: string;
      mediaType: string;
    } = ctx.getQueryParse();

    const result = await new AlertHistoryService(ctx).getAll(query);
    result.dataset = result.dataset.map(o => transformAlertHistoryToWeb(o));
    ctx.json(0, 'ok', result);
  }
};
