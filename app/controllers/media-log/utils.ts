import AlertRuleModel from '@models/media-log/alertRule';
import AlertHistoryModel from '@models/media-log/alertHistory';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import get from 'lodash/get';

export const transformToWeb = (query: AlertRuleModel) => {
  const { ...object } = query;

  const toCamelCase = mapKeysToCamelCase(object);

  toCamelCase.rules = JSON.parse(toCamelCase.rules);
  toCamelCase.notice = JSON.parse(toCamelCase.notice);
  toCamelCase.id = Number(toCamelCase.id);

  return toCamelCase;
};

export const transformAlertHistoryToWeb = (query: AlertHistoryModel) => {
  const { ...object } = query;

  const toCamelCase = mapKeysToCamelCase(object);

  toCamelCase.ruleDetails = JSON.parse(toCamelCase.ruleDetails);
  toCamelCase.rule = JSON.parse(toCamelCase.rule);
  toCamelCase.ruleId = Number(toCamelCase.ruleId);

  return toCamelCase;
};
