import BaseController from '../base/BaseController';
import VideoRealtimeService from '@services/media-log/video-realtime';
import AudioRealtimeService from '@services/media-log/audio-realtime';
import ClassRealtimeService from '@services/media-log/class-realtime';
import { IYouzanFrameworkContext } from '@youzan/youzan-framework/definitions';
import { PageInfo } from '@definitions/base';
import { MEDIA_TYPE } from '@constants/media-log';

interface IListQuery {
  mediaType: string;
  target: string;
  osType: string;
  appKey: string;
  startTime: number;
  endTime: number;
}

type IListClassQuery = IListQuery & { sdkVersion: string };

export = class RealtimeController extends BaseController {
  public async list(ctx: IYouzanFrameworkContext) {
    const query: IListQuery = ctx.request.query || {};

    if (query.mediaType === MEDIA_TYPE.AUDIO) {
      const result = await new AudioRealtimeService(ctx).getErrorTimeline(query);
      ctx.json(0, 'ok', this.toCamelCase(result));
      return;
    }

    if (query.mediaType === MEDIA_TYPE.VIDEO) {
      const result = await new VideoRealtimeService(ctx).getTimeline(query);
      ctx.json(0, 'ok', this.toCamelCase(result));
      return;
    }

    if (query.mediaType === MEDIA_TYPE.CLASS) {
      const result = await new ClassRealtimeService(ctx).getTimeline(query as IListClassQuery);
      ctx.json(0, 'ok', this.toCamelCase(result));
      return;
    }
  }

  public async errorTopNList(ctx: IYouzanFrameworkContext) {
    const query: IListQuery = ctx.request.query || {};

    if (query.mediaType === MEDIA_TYPE.AUDIO) {
      const result = await new AudioRealtimeService(ctx).getErrorTopNList(query);
      ctx.json(0, 'ok', this.toCamelCase(result));
      return;
    }

    if (query.mediaType === MEDIA_TYPE.VIDEO) {
      const result = await new VideoRealtimeService(ctx).getErrorTopNList(query);
      ctx.json(0, 'ok', this.toCamelCase(result));
      return;
    }

    if (query.mediaType === MEDIA_TYPE.CLASS) {
      // const result = await new ClassRealtimeService(ctx).getTimeline(query as IListClassQuery);
      ctx.json(0, 'ok', []);
      return;
    }
  }
};
