import BaseController from '../base/BaseController';
import AlertRuleService from '@services/media-log/alert-rule';
import { IYouzanFrameworkContext } from '@youzan/youzan-framework/definitions';
import mapKeysToSnakeCase from '@youzan/utils/string/mapKeysToSnakeCase';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import { IRule } from './type';
import { PageInfo } from '@definitions/base';
import AlertRuleModel from '@models/media-log/alertRule';
import isPlainObject from 'lodash/isPlainObject';

const transform = (query: IRule) => {
  const toSnakeCase = mapKeysToSnakeCase(query, false);
  toSnakeCase.rules = JSON.stringify(toSnakeCase.rules);
  toSnakeCase.notice = JSON.stringify(toSnakeCase.notice);

  return toSnakeCase;
};

const transformToWeb = (query: AlertRuleModel) => {
  const { ...object } = query;

  const toCamelCase = mapKeysToCamelCase(object);

  toCamelCase.rules = JSON.parse(toCamelCase.rules);
  toCamelCase.notice = JSON.parse(toCamelCase.notice);
  toCamelCase.id = Number(toCamelCase.id);

  return toCamelCase;
};

export = class AlertRuleController extends BaseController {
  public async list(ctx: IYouzanFrameworkContext) {
    const query: {
      page: PageInfo;
      appKey: string;
    } = ctx.getQueryParse();

    const result = await new AlertRuleService(ctx).getAll(query);
    result.dataset = result.dataset.map(o => transformToWeb(o));
    ctx.json(0, 'ok', result);
  }

  public async detail(ctx: IYouzanFrameworkContext) {
    const query = ctx.request.query || {};

    const result = await new AlertRuleService(ctx).getDetail(query.id);

    if (result) {
      ctx.json(0, 'ok', transformToWeb(result));
    } else {
      ctx.fail(29, '未找到该报警规则');
    }
  }

  public async create(ctx: IYouzanFrameworkContext) {
    const query: IRule = ctx.request.body || {};

    const result = await new AlertRuleService(ctx).create(transform(query));

    ctx.json(0, 'ok', result);
  }

  public async update(ctx: IYouzanFrameworkContext) {
    const query: IRule = ctx.request.body || {};

    const result = await new AlertRuleService(ctx).update(transform(query));
    ctx.json(0, 'ok', result);
  }

  public async delete(ctx: IYouzanFrameworkContext) {
    const query = ctx.request.body || {};

    const result = await new AlertRuleService(ctx).delete(query.id);
    ctx.json(0, 'ok', result);
  }
};
