import BaseController from '../base/BaseController';
import { IYouzanFrameworkContext } from '@youzan/youzan-framework/definitions';
import DataInsightService from '@services/media-log/data-insight';

interface IListQuery {
  mediaType: string;
  target: string;
  osType: string;
  appKey: string;
  startTime: number;
  endTime: number;
}

export = class InsightController extends BaseController {
  public async timelinelist(ctx: IYouzanFrameworkContext) {
    const query: IListQuery = ctx.request.query || {};
    const result = await new DataInsightService(ctx).getTimeline(query);
    ctx.json(0, 'ok', this.toCamelCase(result));
    return;
  }
};
