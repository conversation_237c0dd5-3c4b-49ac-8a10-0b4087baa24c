import BaseController from '../base/BaseController';
import { <PERSON><PERSON><PERSON><PERSON>FrameworkContext } from '@youzan/youzan-framework/definitions';
import TeeH5Service from '@services/api/robot/TeeH5Service';
import { PageError } from '@youzan/youzan-framework';
import { APP, APP_ID, CHAT_ID, STEP_STATUS_DICT_MP, tester, USER_ID, bizOwner, baseWscBranchName } from '@services/api/robot/config';
import moment from 'moment';
import args from '@youzan/utils/url/args';
import formatDate from '@youzan/utils/date/formatDate';
import _ from 'lodash';

enum AppStatusEnum {
  CREATED = 'CREATED',
  MERGING_BUS_BRANCH = 'MERGING_BUS_BRANCH',
  MERGE_BUS_BRANCH_SUC = 'MERGE_BUS_BRANCH_SUC',
  MERGE_BUS_BRANCH_FAIL = 'MERGE_BUS_BRANCH_FAIL',
  BUILDING = 'BUILDING',
  BUILD_OK = 'BUILD_OK',
  BUILD_FAIL = 'BUILD_FAIL',
  MERGE_MERGE_BRANCH_SUC = 'MERGE_MERGE_BRANCH_SUC',
  MERGE_MERGE_BRANCH_FAIL = 'MERGE_MERGE_BRANCH_FAIL',
  CHANGED = 'CHANGED',
  DEPLOY_SUCCESS = 'DEPLOY_SUCCESS',
  DEPLOYING = 'DEPLOYING',
  DEPLOY_FAIL = 'DEPLOY_FAIL',
  MERGE_MASTER_SUC = 'MERGE_MASTER_SUC', // 合并master分支成功
  MERGE_MASTER_FAIL = 'MERGE_MASTER_FAIL',
  DEPLOYING_MASTER = 'DEPLOYING_MASTER',
  DEPLOY_MASTER_SUCCESS = 'DEPLOY_MASTER_SUCCESS',
  DEPLOY_MASTER_FAIL = 'DEPLOY_MASTER_FAIL',
  BUILDING_MASTER = 'BUILDING_MASTER',
  BUILD_MASTER_FAIL = 'BUILD_MASTER_FAIL',
  BUILD_MASTER_OK = 'BUILD_MASTER_OK',
}

enum PipelineTypeEnum {
  CHECK = 1,
  MERGE_BUS = 2,
  MERGE_MASTER = 3,
}

enum PAGE_DIFF_RESULT {
  pending = 0,
  doing = 1,
  success = 9,
  fail = 2,
}

enum UITEST_STATUS {
  PENDING = 2,
  SUCCESS = 3,
  FAIL = 4,
}

enum PRE_PASS {
  NO,
}

enum SYNC_REPO_TAG_STAGE {
  PRE_VERIFY = 1,
  WHEN_MERGE_MASTER,
}

function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function getTester(dateString = moment().format('YYYY-MM-DD')) {
  // return tester['jiangsujiao'];
  const startTester = {
    tester: 'molili',
    date: '2023-11-29',
  };
  // return tester.zhangxiaolin;
  const currentTester = tester[startTester.tester];
  const testerList = Object.values(tester);
  const currentTesterIndex = testerList.findIndex(item => item.name === currentTester.name);
  const startMonday = moment(startTester.date).startOf('week');
  const currentMonday = moment(dateString, 'YYYY-MM-DD').startOf('week');
  const weeks = (currentMonday.unix() - startMonday.unix()) / 3600 / 24 / 7;
  let offset = weeks % Object.values(tester).length;
  let nextIndex = currentTesterIndex;
  while (offset > 0) {
    nextIndex += 1;
    if (nextIndex > testerList.length - 1) {
      nextIndex = 0;
    }
    offset--;
  }
  return testerList[nextIndex];
}

function parseMsgContent(message: { body: { content: string } }): Record<string, any> {
  let content: Record<string, unknown> = {};
  try {
    content = JSON.parse(message.body.content);
  } catch (e) {
    console.log('parseMsgContent fail: ', e);
  }
  return content;
}

export = class TeeH5Controller extends BaseController {
  ctx: IYouzanFrameworkContext;
  teeH5Service: TeeH5Service;
  busInfo: Record<string, any>;
  busId: number;
  chatId: string;
  query: Record<string, any>;

  constructor(ctx: IYouzanFrameworkContext) {
    super(ctx);
    this.ctx = ctx;
    this.teeH5Service = new TeeH5Service(ctx);
  }

  async init() {
    const { ctx } = this;
    this.query = ctx.getQueryParse();
    const { busId } = ctx.getQueryParse();
    this.busId = busId;
    this.busInfo = await this.teeH5Service.getBusInfo(busId);
    const busChat = await this.findChatGroupByBus();
    const { chatId } = busChat;
    this.chatId = chatId;
    this.joinTester();
  }

  async sizeDetect() {
    const { chatId, busId, busInfo } = this;
    const { branchInfos = [] } = this.busInfo;
    const detectStatus = {
      notLatest: '⚠️未合最新hotfix分支代码',
      success: '✅已通过检测',
      doing: '⏳检测中',
      fail: '❌未通过检测',
      oversize: '❌体积超出',
    };
    const detectStatusEnum = {
      10001: 'fail',
      0: 'doing',
      10: 'oversize',
      20: 'success',
    };
    let isAllPass = true;
    const detectionList = await this.teeH5Service.getDetectionList();
    const members = await this.teeH5Service.getChatMembers(chatId);
    // const wscVersionList = await this.teeH5Service.getWscVersionList();
    // console.log(wscVersionList);
    const branchContent = await branchInfos
      .filter(_ => /ext-*/.test(_.application))
      .reduce(async (prev, item: any) => {
        const content = [{ tag: 'text', text: `\n${item.application}\n` }];
        const latestCommit = (await this.teeH5Service.getCommits(baseWscBranchName))[0];
        const branchList = await item.branchInfos.reduce(async (prev, item) => {
          let branchDetectStatusText = `${item.branch}`;
          const commits = await this.teeH5Service.getCommits(item.branch);
          const matchingCommit = commits.find(commit => commit.id === latestCommit.id);
          const curMember = members.find((member: any) => member.name.includes(item.creater.realName));
          if (!!matchingCommit) {
            const matchedLatestDetectTask = detectionList.list.find(_ => _.branch === item.branch);
            if (!matchedLatestDetectTask) {
              if (isAllPass) {
                isAllPass = false;
              }
              branchDetectStatusText += ` ${item.creater.realName}  ❌未查到体积检测任务`;
            } else {
              if (matchedLatestDetectTask.commitId !== commits[0].id) {
                branchDetectStatusText += ` 任务ID(${matchedLatestDetectTask.id}) ${item.creater.realName} ❌体积检测的代码不是最新commit，请重新检测`;
              } else {
                if (matchedLatestDetectTask.status === 20) {
                  if (!!matchedLatestDetectTask.domainDiffOver) {
                    branchDetectStatusText += ` 任务ID(${matchedLatestDetectTask.id}) ${item.creater.realName} ${detectStatus.oversize}`;
                    if (isAllPass) {
                      isAllPass = false;
                    }
                  } else {
                    branchDetectStatusText += ` 任务ID(${matchedLatestDetectTask.id}) ${item.creater.realName} ${detectStatus[detectStatusEnum[matchedLatestDetectTask.status]]}`;
                  }
                } else {
                  if (isAllPass) {
                    isAllPass = false;
                  }
                  branchDetectStatusText += ` 任务ID(${matchedLatestDetectTask.id}) ${item.creater.realName} ${detectStatus[detectStatusEnum[matchedLatestDetectTask.status]]}`;
                }
              }
            }
          } else {
            if (isAllPass) {
              isAllPass = false;
            }
            branchDetectStatusText += ` ${item.creater.realName}  ❌weapp/wsc检测分支未合并${baseWscBranchName}代码`;
          }

          const content = [
            { tag: 'text', text: `${branchDetectStatusText}` },
            ...(branchDetectStatusText.includes('❌') ? [{ tag: 'at', user_id: curMember.memberId, user_name: curMember.memberName }] : []),
            { tag: 'text', text: `\n` },
          ];
          return [...(await prev), ...content];
        }, []);
        return [...(await prev), ...content, ...branchList];
      }, []);
    const content = [
      [
        { tag: 'text', text: '体积检测规则：\n' },
        { tag: 'text', text: '1. 体积检测任务由上车开发者自行创建 https://mmp.prod.qima-inc.com/volume/detection\n' },
        { tag: 'text', text: '2. 机器人会自动根据上车子仓库分支检索 gitlab:weapp/wsc 项目中同名分支作为检测分支\n' },
        { tag: 'text', text: '3. 检测分支必须合过最新hotfix分支代码\n' },
        { tag: 'text', text: '4. 机器人需要得到「体积未超出，可上车」的检测结果，如果体积超出请自行解决（优化代码或者申请体积）\n' },
        { tag: 'text', text: '5. 超过当天12:00未通过体积检测的分支将被自动下车\n' },
        ...branchContent,
        {
          tag: 'text',
          text: `通知时间: ${formatDate(new Date(), 'HH时mm分\n')}`,
        },
        {
          tag: 'text',
          text: `大巴车: https://bus.qima-inc.com/busDetailMP/${busId}\n`,
        },
      ],
    ];
    console.log(branchContent);
    if (!isAllPass) {
      await this.teeH5Service.sendMessage({ title: '体积检测', content }, 'post', 'chat_id', chatId);
    }
    return isAllPass;
  }

  async findChatGroupByBus() {
    const { busInfo, busId } = this;
    const { busBaseInfo, branchInfos = [] } = this.busInfo;
    const { busName } = busBaseInfo;
    const { teeH5Service } = this;
    const busChat = await teeH5Service.chatsSearch(busName, APP.BUS_SERVER);
    if (!busChat) {
      const chatId = await this.teeH5Service.createChatGroup(busId);
      await teeH5Service.joinChat(chatId, [APP_ID.ROBOT], APP.BUS_SERVER, 'app_id');
      await sleep(5 * 1000);
      return await this.findChatGroupByBus();
    }
    return busChat;
  }

  async checkTestAndCR() {
    const { teeH5Service, busInfo, busId, chatId } = this;
    const { busBaseInfo, branchInfos = [] } = busInfo;
    const members = await teeH5Service.getChatMembers(chatId);
    const info = branchInfos.reduce((prev: Record<string, any>, appInfo: any) => {
      const data = appInfo.branchInfos.reduce((prev2: any[], cur: any) => {
        if (cur.branchStatus !== 'CREATED' && !cur.reviewStatus.includes('未review')) {
          return prev2;
        }
        const todos = [];
        if (cur.branchStatus === 'CREATED') todos.push('测试');
        if (cur.reviewStatus.includes('未review')) todos.push('CR');
        const curMember = members.find((member: any) => member.name.includes(cur.creater.realName));
        return [
          ...prev2,
          {
            memberId: curMember.memberId || '',
            memberName: curMember.name || '未知',
            branchName: cur.branch,
            todos,
          },
        ];
      }, []);
      if (data.length === 0) {
        return prev;
      }
      return {
        ...prev,
        [appInfo.application]: data,
      };
    }, {});
    const todoApps = Object.keys(info);
    if (todoApps.length > 0) {
      const content = Object.keys(info).reduce((prev: any[], appName: any) => {
        const todoBranchs = info[appName];
        const msg = todoBranchs.reduce(
          (prev: any[], cur: any) => {
            return [
              ...prev,
              { tag: 'text', text: `分支: ${cur.branchName} 未完成${cur.todos.join('、')}` },
              { tag: 'at', user_id: cur.memberId, user_name: cur.memberName },
              { tag: 'text', text: `请尽快处理\n` },
            ];
          },
          [{ tag: 'text', text: `${appName}\n`, style: ['bold'] }]
        );
        return [...prev, msg];
      }, []);

      const res2 = await teeH5Service.sendMessage({ title: '测试&CR', content }, 'post', 'chat_id', chatId);
      return false;
    }
    return true;
  }

  async isFirstMerge(mergeBusLog) {
    const { busId, teeH5Service } = this;
    return mergeBusLog.mergeSucBranch.length === 0 && mergeBusLog.mergeFailBranch.length === 0;
  }

  // 通知分支失败的开发者
  async noticeMergeBusFail() {
    const { busId, chatId } = this;
    const mergeBusLog = await this.teeH5Service.getMergeBusLog(busId);
    const members = await this.teeH5Service.getChatMembers(chatId);
    let content = await mergeBusLog.mergeFailBranch.reduce(async (prev: any[], cur: any) => {
      const { branchInfos = [] } = cur;

      const data = await branchInfos.reduce(
        async (prev2: any[], cur2: any) => {
          const curMember = members.find((member: any) => member.name.includes(cur2.creater.realName));
          let ciText = '';
          if (cur.application === 'ext-tee-wsc-trade') {
            const latestPipeline = await this.teeH5Service.getLatestPipeline(cur2.branch);
            switch (latestPipeline.status) {
              case 'success':
                ciText = `CI状态: ✅通过`;
                break;
              case 'failed':
                ciText = `CI状态: ❌未通过`;
                break;
              default:
                ciText = `CI状态未知(${latestPipeline.status})`;
                break;
            }
          }
          return [
            ...(await prev2),
            { tag: 'text', text: `分支: ${cur2.branch} ❌同步失败 ${ciText}` },
            { tag: 'at', user_id: curMember.memberId, user_name: curMember.name },
            { tag: 'text', text: `请尽快处理\n` },
          ];
        },
        [{ tag: 'text', text: `${cur.application}\n`, style: ['bold'] }]
      );
      return [...(await prev), ...data];
    }, []);
    content = [
      [
        ...content,
        ...[
          {
            tag: 'text',
            text: '\n同步失败可能的原因与处理方式：\n',
            style: ['bold'],
          },
          {
            tag: 'text',
            text: '------------------------------\n',
          },
          {
            tag: 'text',
            text:
              '[优先确认]失败原因：ext-tee-wsc-trade CI未通过\n解决方式：前往 https://gitlab.qima-inc.com/weapp/ext-tee-wsc-trade/-/pipelines 找自己的开发分支看CI是否通过，如果不通过则根据具体原因进行处理\n',
          },
          {
            tag: 'text',
            text: '------------------------------\n',
          },
          {
            tag: 'text',
            text: '失败原因：代码冲突\n解决方式：本地将开发分支手动merge到bus分支处理完冲突后push\n',
          },
          {
            tag: 'text',
            text: '------------------------------\n',
          },
          {
            tag: 'text',
            text: '失败原因：机器人误报\n解决方式：如果你认为代码没问题，可以忽略等待机器人下阶段处理(一般不会出现重复误报2次以上的情况)\n',
          },
          {
            tag: 'text',
            text: `\n通知时间: ${formatDate(new Date(), 'HH时mm分\n')}`,
          },
          {
            tag: 'text',
            text: `大巴车: https://bus.qima-inc.com/busDetailMP/${busId}\n`,
          },
        ],
      ],
    ];
    await this.teeH5Service.sendMessage({ title: '发车失败', content }, 'post', 'chat_id', chatId);
  }

  async noticeText(text) {
    const { busId, chatId } = this;
    const content = [
      [
        { tag: 'text', text: `${text}\n` },
        {
          tag: 'text',
          text: `通知时间: ${formatDate(new Date(), 'HH时mm分\n')}`,
        },
        {
          tag: 'text',
          text: `大巴车: https://bus.qima-inc.com/busDetailMP/${busId}\n`,
        },
      ],
    ];
    await this.teeH5Service.sendMessage({ title: '进度通知', content }, 'post', 'chat_id', chatId);
  }

  async checkMergeMasterStatus() {
    const { busId, busInfo } = this;
    const { busBaseInfo, branchInfos = [] } = busInfo;
    if (busBaseInfo.pipelineType === PipelineTypeEnum.MERGE_MASTER) return true;
    const isAllBranchMergeMasterSuc = branchInfos.every((item: any) => ['MERGE_MASTER_SUC_SYNC_VERSION_SUC'].includes(item.deployStatus));
    const nodeApps = (await this.teeH5Service.getApplicationStatus(busId)).filter((item: any) => item.applicationName.includes('wsc-h5'));
    const isAllAppMergeMasterSuc = nodeApps.every((item: any) => ['MERGE_MASTER_SUC_SYNC_VERSION_SUC'].includes(item.appStatus));
    // console.log(isAllBranchMergeMasterSuc, isAllAppMergeMasterSuc);
    // throw new PageError(500, 'debug');
    if (!isAllBranchMergeMasterSuc || !isAllAppMergeMasterSuc) {
      await this.teeH5Service.syncRepoTag(SYNC_REPO_TAG_STAGE.WHEN_MERGE_MASTER, busInfo);
      if (isAllBranchMergeMasterSuc) {
        throw new PageError(500, '上车分支合入master失败');
      }
      if (!isAllAppMergeMasterSuc) {
        throw new PageError(500, '应用合入master失败');
      }
    }
    console.log('checkMergeMasterStatus 通过');
    return true;
  }

  async checkSyncStatus() {
    const { busId, busInfo } = this;
    const { busBaseInfo } = busInfo;
    // if (busBaseInfo.pipelineType === PipelineTypeEnum.MERGE_MASTER) return true;
    const nodeApps = (await this.teeH5Service.getApplicationStatus(busId)).filter((item: any) => item.applicationName.includes('wsc-h5'));
    // @ts-ignore
    console.log('checkSyncStatus nodeApps:', JSON.stringify(nodeApps));

    const isAllSuccess = nodeApps.every((item: any) => {
      return ['BUILDING_MASTER', 'DEPLOY_SUCCESS', 'MERGE_MASTER_SUC_SYNC_VERSION_SUC'].includes(item.appStatus);
    });

    if (!(nodeApps.length > 0 && isAllSuccess)) {
      const hasBuilding = nodeApps.some((item: any) => {
        return [AppStatusEnum.BUILDING, AppStatusEnum.DEPLOYING].includes(item.appStatus);
      });
      if (hasBuilding) {
        throw new PageError(500, '因为有构建中的应用，等待1分钟后再次检查');
      }
      const res = await this.teeH5Service.syncRepoTag(SYNC_REPO_TAG_STAGE.PRE_VERIFY, this.busInfo);
      // @ts-ignore
      throw new PageError('开始同步应用');
    }
    return true;
  }

  async checkBuiding() {
    const { busId } = this;
    const [nodeApps] = await Promise.all([(await this.teeH5Service.getApplicationStatus(busId)).filter((item: any) => item.applicationName.includes('wsc-h5'))]);
    const isAllBuilt = nodeApps.every(item => {
      return !['BUILDING', 'DEPLOYING'].includes(item.appStatus);
    });
    if (!isAllBuilt) {
      this.noticeText('node应用构建中，请等待');
      throw new PageError(200, 'node应用构建中，请等待');
    }
    const hasFailApp = nodeApps.some(item => {
      return ['DEPLOY_FAIL', 'BUILD_FAIL'].includes(item.appStatus);
    });
    if (hasFailApp) {
      this.noticeText('有应用发布失败，请检查');
      throw new PageError(200, '有应用发布失败，请检查');
    }
  }

  // 基础预发验证
  async checkPrePass() {
    const { busInfo, busId, chatId } = this;
    const members = await this.teeH5Service.getChatMembers(chatId);
    const { busBaseInfo, branchInfos = [] } = busInfo;
    let hasNoPrePassBranch = false;
    const content = branchInfos.reduce((prev: string[], cur: any) => {
      const data = cur.branchInfos.reduce(
        (curBranchInfosPrev: number[], curBranchInfosCur: any) => {
          if (curBranchInfosCur.prePass !== PRE_PASS.NO) {
            return curBranchInfosPrev;
          }
          if (!hasNoPrePassBranch) {
            hasNoPrePassBranch = true;
          }
          const curMember = members.find((member: any) => member.name.includes(curBranchInfosCur.creater.realName));
          console.log('checkpre', curMember, curBranchInfosCur);
          return [
            ...curBranchInfosPrev,
            { tag: 'text', text: `分支: ${curBranchInfosCur.branch} 未完成预发验证` },
            { tag: 'at', user_id: curMember.memberId, user_name: curMember.name },
            { tag: 'text', text: `请尽快处理\n` },
          ];
        },
        [{ tag: 'text', text: `\n${cur.application}\n`, style: ['bold'] }]
      );
      if (data.length <= 1) return prev;

      return [...prev, data];
    }, []);

    if (hasNoPrePassBranch) {
      const res = await this.teeH5Service.sendMessage(
        {
          title: '预发验证',
          content: [
            ...content,
            [
              {
                tag: 'text',
                text: `\n通知时间: ${formatDate(new Date(), 'HH时mm分\n')}`,
              },
              {
                tag: 'text',
                text: `大巴车: https://bus.qima-inc.com/busDetailMP/${busId}\n`,
              },
            ],
          ],
        },
        'post',
        'chat_id',
        chatId
      );
      return false;
    }
    return true;
  }

  /**
   * 检测自动化测试状态 - 需要全部用例都跑完到失败和成功
   */
  async checkAutoTestStatus() {
    const { chatId } = this;
    const msg = await this.findAutoTestMsg();
    let content = undefined
    if (msg && msg.body && msg.body.content && msg.body.content.content) {
      content = msg.body.content.content
    }
    let isAllFinished = true;
    const newContent = await Promise.all(
      content.map(async item => {
        return await item.reduce(async (prev, cur, index) => {
          const isMatched = /(wsc-h5|自动测试|页面对比|http)/.test(cur.text);
          if (!isMatched) return prev;
          let res = {
            total: 0,
            pending: 0,
            success: 0,
            fail: 0,
          };
          if (cur.tag === 'a') {
            if (cur.href.includes('uitest')) {
              const releaseId = args.get('releaseId', cur.href);
              const { count: total, rows }: { total: number; rows: any[] } = await this.teeH5Service.getAllTaskForUITest(releaseId);
              res = rows.reduce(
                (prev, cur) => {
                  switch (cur.status) {
                    case UITEST_STATUS.SUCCESS:
                      prev.success++;
                      break;
                    case UITEST_STATUS.FAIL:
                      prev.fail++;
                      break;
                    default:
                      prev.pending++;
                      break;
                  }
                  return prev;
                },
                {
                  total,
                  pending: 0,
                  success: 0,
                  fail: 0,
                }
              );
              console.log('ui-test', res);
            } else if (cur.href.includes('sd-tech')) {
              const id = cur.href.replace(/.*report-detail\//, '');
              const items = await this.teeH5Service.queryPageDiffReportDetails(id);
              res = items.reduce(
                (prev, cur) => {
                  switch (cur.result) {
                    case PAGE_DIFF_RESULT.success:
                      prev.success++;
                      break;
                    case PAGE_DIFF_RESULT.fail:
                      prev.fail++;
                      break;
                    default:
                      prev.pending++;
                      break;
                  }
                  return prev;
                },
                {
                  total: items.length,
                  pending: 0,
                  success: 0,
                  fail: 0,
                }
              );
              console.log('页面对比', res);
            }
            cur.text = `${res.pending > 0 ? `执行中(${Math.floor(((res.fail + res.success) / res.total) * 10000) / 100}%)` : res.fail > 0 ? '已完成' : '全部通过'} ${
              res.fail > 0 ? `失败:${res.fail}` : ''
            } ${cur.text}\n`;
          }
          if (res.pending > 0) isAllFinished = false;

          return [...(await prev), await cur];
        }, []);
      })
    );
    if (!isAllFinished) {
      await this.teeH5Service.sendMessage({ title: '自动化测试 - 结果', content: newContent }, 'post', 'chat_id', chatId);
    }
    return isAllFinished;
  }

  async checkAutoTestPass() {
    console.log('checkAutoTestPass 1');
    await this.startTest();
    console.log('checkAutoTestPass 2');
    if (!(await this.checkAutoTestStatus())) {
      throw new PageError(500, '自动化测试中，请等待');
    }
    console.log('checkAutoTestPass 3');

    const { busId, chatId } = this;
    const tester = getTester();
    const [nodeApps, allChatMessages] = await Promise.all([
      (await this.teeH5Service.getApplicationStatus(busId)).filter((item: any) => item.applicationName.includes('wsc-h5')),
      this.teeH5Service.getChatMessages(chatId),
    ]);
    const isMentionRobot = (message: { mentions: { id: string }[] }) => {
      return (message.mentions || []).some((mention: any) => mention.id === USER_ID.ROBOT);
    };
    // console.log(allChatMessages);
    const isYzok = allChatMessages.some(message => {
      if (message.deleted) return false;
      if (!isMentionRobot(message)) return false;
      if (message.sender.id !== tester.memberId) return false;
      const content = parseMsgContent(message);
      if (content && content.text) {
        return content.text.includes('yzok') || false;
      } else {
        return false
      }
    });
    if (!isYzok) {
      const noticeTeserContent = [
        [
          { tag: 'at', user_id: tester.memberId, user_name: tester.name },
          { tag: 'text', text: `所有自动化测试已完成，请检查是否通过\n` },
          { tag: 'text', text: `完成后回复「@营销日常提醒机器人 yzok」` },
        ],
      ];

      const res = await this.teeH5Service.sendMessage({ title: '自动化测试 - 完成', content: noticeTeserContent }, 'post', 'chat_id', chatId);
    }
    return isYzok;
  }

  /**
   * 寻找到机器人发送的最后一条自动化测试消息
   * 最新的一条记录
   */
  async findAutoTestMsg() {
    const { chatId } = this;
    const allMsgs = await this.teeH5Service.getChatMessages(chatId);
    const autoTestMsgs = allMsgs.filter(msg => {
      if (!(msg.sender.senderType === 'app' && msg.sender.id === APP_ID.ROBOT && msg.sender.idType === 'app_id' && !msg.deleted)) return false;
      const content = parseMsgContent(msg);
      return content.title === '自动化测试';
    });
    if (autoTestMsgs.length === 0) return null;
    const autoTestMsg = autoTestMsgs[autoTestMsgs.length - 1];
    return {
      ...autoTestMsg,
      body: {
        ...autoTestMsg.body,
        content: parseMsgContent(autoTestMsg),
      },
    };
  }

  async joinTester() {
    const { chatId } = this;
    const [members] = await Promise.all([this.teeH5Service.getChatMembers(chatId)]);
    const tester = getTester();
    const curMember = members.find((member: any) => member.name.includes(tester.name));
    if (!curMember) {
      await this.teeH5Service.joinChat(chatId, [tester.memberId]);
    }
  }

  async startTest() {
    const { busInfo, busId, chatId } = this;
    const autoTestMsg = await this.findAutoTestMsg();
    if (autoTestMsg) {
      return true;
    }
    console.log('startTest 1');
    const [members, nodeApps] = await Promise.all([
      this.teeH5Service.getChatMembers(chatId),
      (await this.teeH5Service.getApplicationStatus(busId)).filter((item: any) => item.applicationName.includes('wsc-h5')),
    ]);
    console.log('startTest 2');
    const tester = getTester();
    console.log(tester, members);
    const curMember = members.find((member: any) => member.name.includes(tester.name));
    console.log('startTest 3', curMember);
    if (!curMember) {
      await this.teeH5Service.joinChat(chatId, [tester.memberId]);
    }

    const content = await Promise.all(
      nodeApps.map(async (item: any) => {
        const appName = item.applicationName;
        if (['wsc-h5-trade', 'wsc-h5-goods'].includes(appName)) {
          const pathByUITest = await this.teeH5Service.startUITest(appName);
          let pathByPageDiff = [];
          if (['wsc-h5-trade', 'wsc-h5-goods'].includes(appName)) {
            pathByPageDiff = await this.teeH5Service.startPageDiff(appName);
          }
          return [
            { tag: 'text', text: `${appName}\n`, style: ['bold'] },
            { tag: 'text', text: `自动测试: ${pathByUITest}\n` },
            ...pathByPageDiff.map(url => {
              return { tag: 'text', text: `页面对比: ${url}\n` };
            }),
          ];
        } else {
          return [
            { tag: 'text', text: `${appName}\n`, style: ['bold'] },
            { tag: 'at', user_id: chatId, user_name: '尼莫' },
            { tag: 'text', text: `未知应用，请确认是否需要测试验证\n` },
          ];
        }
      })
    );
    console.log('startTest 3');
    const res = await this.teeH5Service.sendMessage({ title: '自动化测试', content }, 'post', 'chat_id', chatId);
    throw new PageError(500, '开始测试');
  }

  async getCurrentStatusCode() {
    const { ctx } = this;
    const { busId, chatId } = this;
    const [appList, allChatMessages] = await Promise.all([this.teeH5Service.getApplicationStatus(busId), this.teeH5Service.getChatMessages(chatId)]);

    const statusList = appList.reduce((prev, cur) => {
      let _buttonStatus = 99
      if (STEP_STATUS_DICT_MP[cur.appStatus] && STEP_STATUS_DICT_MP[cur.appStatus].buttonStatus) {
        _buttonStatus = STEP_STATUS_DICT_MP[cur.appStatus].buttonStatus
      }
      return [...prev, _buttonStatus];
    }, []);
    const currentStatus = Math.min(...statusList);
    return currentStatus;
  }

  async getPublishContent() {
    const { branchInfos = [] } = this.busInfo;
    let allText = '';
    branchInfos.forEach((branch: { branchInfos: Array<{ branch: string; isRollback: number; descr: string }> }) => {
      for (const b of branch.branchInfos) {
        const branchName = '分支名: ' + b.branch + '\n';
        const quickFB = b.isRollback === 1 ? '支持快速回滚：是' : '支持快速回滚：否';
        allText += `${branchName}${b.descr}\n${quickFB}\n\n`;
      }
    });
    return allText;
  }

  async getAllTicketIdFromChat() {
    const { chatId } = this;
    const [allMsgs] = await Promise.all([this.teeH5Service.getChatMessages(chatId)]);
    const applyTicketMsg = allMsgs.filter((msg: { body: { content: string }; sender: { senderType: string; id: string; idType: string }; deleted: boolean }) => {
      if (!(msg.sender.senderType === 'app' && msg.sender.id === APP_ID.ROBOT && msg.sender.idType === 'app_id' && !msg.deleted)) return false;
      const content = parseMsgContent(msg);
      return content.title === '工单审批';
    });
    const msg = parseMsgContent(applyTicketMsg[0]);
    const ticketIds = _.flatten(msg.content)
      .filter((_: any) => _.tag === 'a')
      .map((_: any) => {
        const hrefList = _.href.split('/');
        return hrefList[hrefList.length - 1];
      })
      .map(id => Number(id));

    return ticketIds;
  }

  async applyPublish() {
    const { busId, chatId } = this;
    const [nodeApps, allMsgs, members] = await Promise.all([
      (await this.teeH5Service.getApplicationStatus(busId)).filter((item: any) => item.applicationName.includes('wsc-h5')),
      this.teeH5Service.getChatMessages(chatId),
      this.teeH5Service.getChatMembers(chatId),
    ]);
    console.log(1);
    const tester = getTester();
    const applyTicketMsg = allMsgs.filter((msg: { body: { content: string }; sender: { senderType: string; id: string; idType: string }; deleted: boolean }) => {
      if (!(msg.sender.senderType === 'app' && msg.sender.id === APP_ID.ROBOT && msg.sender.idType === 'app_id' && !msg.deleted)) return false;
      const content = parseMsgContent(msg);
      return content.title === '工单审批';
    });
    console.log(2);

    const joinMemberIds = new Set<string>();
    for (const nodeApp of nodeApps) {
      const { applicationName } = nodeApp;
      const { memberId } = bizOwner[applicationName];
      const curMember = members.find((member: any) => member.id === memberId);
      if (!curMember) {
        joinMemberIds.add(memberId);
      }
    }
    console.log(3);
    if (joinMemberIds.size > 0) {
      await this.teeH5Service.joinChat(chatId, Array.from(joinMemberIds));
    }
    console.log(4);

    // 如果发车群中还没有通知审批工单，则认为是还没有申请工单，因此开始申请
    if (applyTicketMsg.length === 0) {
      const ticketList = await Promise.all(
        nodeApps.map(async (app: { applicationName: string }) => {
          const appInfo = await this.teeH5Service.getAppDetail(app.applicationName);
          const reason = await this.getPublishContent();
          return await this.teeH5Service.deployTicketByOPS(appInfo, reason);
        })
      );
      const content = ticketList.reduce((prev: any[], cur: any) => {
        return [...prev, [{ tag: 'text', text: `应用: ${cur.app_name}: https://flow.qima-inc.com/#/ticket/${cur.ticket_id}\n` }]];
      }, []);
      content.push([
        { tag: 'at', user_id: tester.memberId, user_name: tester.name },
        { tag: 'text', text: `请尽快完成审批\n` },
      ]);
      await this.teeH5Service.sendMessage({ title: '工单审批', content }, 'post', 'chat_id', chatId);
      throw new PageError(200, '创建工单，并通知QA审批');
    }

    console.log(5);
    // 如果发车群中已经有通知审批工单，则认为是已经申请了工单，判断下工单状态，并通知对应的审批人
    const msg = parseMsgContent(applyTicketMsg[0]);
    const ticketIds = await this.getAllTicketIdFromChat();
    const reqTicketInfos = ticketIds.map(id => {
      console.log(id);
      return this.teeH5Service.getTicketInfo(id);
    });
    console.log(6);
    const ticketInfos = await Promise.all(reqTicketInfos);
    console.log(ticketInfos);
    const needNoticeMembers = ticketInfos.reduce((prev, ticketInfo) => {
      console.log(ticketInfo);
      if (ticketInfo.current_node.node_id === '149') {
        prev.add(tester);
      } else if (ticketInfo.current_node.node_id === 'c7') {
        const applicationName = ticketInfo.title.match('wsc-h5-(trade|goods|assets|ump)')[0];
        prev.add(bizOwner[applicationName]);
      }
      return prev;
    }, new Set<{ user_id: string; user_name: string }>());
    console.log('needNoticeMembers.size > 0');
    if (needNoticeMembers.size > 0) {
      const atList = [];
      const content = msg.content.slice(0, msg.content.length - 1);
      const atMemberIds: string[] = [];
      for (const needNoticeMember of needNoticeMembers) {
        if (atMemberIds.includes(needNoticeMember.memberId)) {
          continue;
        }
        atMemberIds.push(needNoticeMember.memberId);
        atList.push({ tag: 'at', user_id: needNoticeMember.memberId, user_name: needNoticeMember.name });
      }
      atList.push({ tag: 'text', text: `请尽快完成审批\n` });
      content.push(atList);
      await this.teeH5Service.sendMessage({ title: msg.title, content }, 'post', 'chat_id', chatId);
      throw new PageError(200, '还有未完成审批的工单，已通知');
    }
  }

  // 发车
  async postRelaseJson() {
    await this.init();
    const { ctx, busId, busInfo } = this;

    // 检测是否所有分支都完成了测试 & CR
    let preCheck = true;
    try {
      if (!this.query.skipSizeDetect) {
        const res = await this.sizeDetect();
        if (preCheck) preCheck = res;
      }
    } catch (e) {
      console.log(e);
      if (preCheck) preCheck = false;
    }
    try {
      if (!this.query.skipCrCheck) {
        const res = await this.checkTestAndCR();
        if (preCheck) preCheck = res;
      }
    } catch (e) {
      console.log(e);
      if (preCheck) preCheck = false;
    }
    if (!preCheck) {
      return ctx.json(200, '前置校验未通过');
    }

    // let statusCode = 10;
    const statusCode = await this.getCurrentStatusCode();

    if (statusCode === 1 /* 准备阶段 */) {
      this.noticeText('开始发车');
      await this.teeH5Service.startMerge(busId);
      return ctx.json(200, '开始发车');
    }
    if (statusCode === 2 /* 合并大巴车分支中 */) {
      this.noticeText('大巴车分支合并中');
      return ctx.json(200, '合并大巴车分支中');
    }
    if (statusCode === 3 /* 合并大巴车分支失败 */) {
      await this.noticeMergeBusFail();
      await this.teeH5Service.startMerge(busId);
      return ctx.json(200, '合并大巴车分支失败, 已通知开发者');
    }

    // 同步配置信息(pre)
    if ([4 /* 合并大巴车分支成功 */, 5 /* 合并大巴车分支成功但未同步分支 */].includes(statusCode)) {
      await this.teeH5Service.syncRepoTag(SYNC_REPO_TAG_STAGE.PRE_VERIFY, this.busInfo);
      this.noticeText('已开始同步信息');
      return ctx.json(200, '已开始同步信息');
    }
    if (statusCode < 6 /* 合并大巴车分支成功且同步分支成功 */) {
      return ctx.json(200, `大巴车失败${statusCode}`);
    }

    await this.checkBuiding();

    // 检测是否所有分支都完成了预发验证
    if (!(await this.checkPrePass())) {
      throw new PageError(200, '基础预发验证中');
    }

    // 开始自动化测试回归
    if (!(await this.checkAutoTestPass())) {
      throw new PageError(200, '测试验证中');
    }

    // 开始合入master
    if (statusCode === 6 /* 基础预发同步成功，且完成预发验证&测试回归 */) {
      try {
        const checkBeforeFinishRes = await this.teeH5Service.checkBeforeFinish(busId);
        if (checkBeforeFinishRes.length > 0) {
          return ctx.json(500, '前置校验出现问题', checkBeforeFinishRes);
        }
        const res = this.teeH5Service.mergeIntoMaster(busId);
        await this.noticeText('开始合入master');
      } catch (e) {
        console.log('nemo debug', e);
      }
      return ctx.json(200, '开始合入master');
    }

    // 合入master
    if (statusCode === 7 /* 合并master失败 */) {
      this.noticeText('合入master失败，正在重试');
      this.teeH5Service.mergeIntoMaster(busId);
      return ctx.json(200, '合并master失败，开始重试');
    }
    if ([8 /* 合并master分支成功 */, 9 /* 合并master成功但未同步版本信息 */].includes(statusCode)) {
      await this.teeH5Service.syncRepoTag(SYNC_REPO_TAG_STAGE.WHEN_MERGE_MASTER, busInfo);
      this.noticeText('开始同步信息(master)');
      return ctx.json(200, '开始同步信息(master)');
    }
    if ([10 /* 合并master成功且同步版本信息成功 */, 11 /* 结束 */].includes(statusCode)) {
      await this.applyPublish();
    }

    const allMsgs = await this.teeH5Service.getChatMessages(this.chatId);
    const startBlueGreenMsg = allMsgs.filter(msg => {
      if (!(msg.sender.senderType === 'app' && msg.sender.id === APP_ID.ROBOT && msg.sender.idType === 'app_id' && !msg.deleted)) return false;
      const content = parseMsgContent(msg);
      return content.title === '开始蓝绿';
    });
    if (startBlueGreenMsg.length === 0) {
      const ticketIds = await this.getAllTicketIdFromChat();
      const content = await ticketIds.reduce(async (prev: any, id) => {
        const allFields = await this.teeH5Service.getTicketAllFields(id);
        const applicationName = allFields.find((_: any) => _.field_key === 'app_name')['field_value'];
        const deployId = allFields.find((_: any) => _.field_key === 'deploy_id')['field_value'];
        return [...(await prev), [{ tag: 'text', text: `${applicationName}的发布单: https://ops.qima-inc.com/#/applications/${applicationName}/deploy/${deployId}/publish\n` }]];
      }, []);
      content.push([{ tag: 'text', text: '增加蓝绿店铺ID请「@营销日常提醒机器人 <kdtId>」' }]);
      content.push([{ tag: 'at', user_id: 'all', user_name: '所有人' }]);
      await this.teeH5Service.sendMessage({ title: '开始蓝绿', content }, 'post', 'chat_id', this.chatId);
    }

    return ctx.json(200, '自动化流程结束');
  }
};
