import moment from 'moment';
import BaseController from '../base/BaseController';
import OnDutyService from '@services/api/robot/OnDutyService';
import {get} from 'lodash';
import {IYouzanFrameworkContext} from '@youzan/youzan-framework/definitions';
import {CHAT_ID, ON_DUTY_BITABLE} from '@services/api/robot/config';
// import { ICreateSentryIssueQuery, ICreateIssueData } from 'definitions/jira/Issue';

type Member = {
  memberId: string;
  name: string;
};

export = class OnDutyController extends BaseController {
  ctx: IYouzanFrameworkContext;

  constructor(ctx: IYouzanFrameworkContext) {
    super(ctx);
    this.ctx = ctx;
  }

  /**
   * 判断当天是否需要提醒
   * @param ctx
   */
  async isNeedAlert() {
    const { ctx } = this;
    const onDutyService = new OnDutyService(ctx);
    const [activeGoodsRecord, activeTradeRecord]: [unknown, unknown] = await Promise.all([
      ((
        await onDutyService.getBitableRecords({
          appToken: ON_DUTY_BITABLE.APP_TOKEN,
          tableId: ON_DUTY_BITABLE.TABLE_ID.GOODS,
        })
      ) as any).filter((item: never) => get(item, "fields['状态'][0].text") === 'ACTIVE')[0],
      ((
        await onDutyService.getBitableRecords({
          appToken: ON_DUTY_BITABLE.APP_TOKEN,
          tableId: ON_DUTY_BITABLE.TABLE_ID.TRADE,
        })
      ) as any).filter((item: never) => get(item, "fields['状态'][0].text") === 'ACTIVE')[0],
    ]);
    const activeDay = moment(Math.min(get(activeGoodsRecord, "fields['开始日期']"), get(activeTradeRecord, "fields['开始日期']")))
      .startOf('day')
      .unix();
    const today = moment()
      .startOf('day')
      .unix();
    console.log(activeGoodsRecord, activeTradeRecord);
    return activeDay === today;
  }

  async getNextRecord(type: 'trade' | 'goods') {
    let tableId = '';
    switch (type) {
      case "trade":
        tableId = ON_DUTY_BITABLE.TABLE_ID.TRADE;
        break;
      case "goods":
        tableId = ON_DUTY_BITABLE.TABLE_ID.GOODS;
        break;
    }
    const onDutyService = new OnDutyService(this.ctx);
    const tradeRecords = ((await onDutyService.getBitableRecords({
      appToken: ON_DUTY_BITABLE.APP_TOKEN,
      tableId,
    })) as any).filter((item: any) => Date.now() < item.fields['结束日期']);
    const curRecordIndex = tradeRecords.findIndex((item: any) => item.fields['状态'][0].text === 'ACTIVE');
    const curRecord = tradeRecords[curRecordIndex];
    if (curRecordIndex + 1 >= tradeRecords.length) {
      return null;
    }
    return tradeRecords[curRecordIndex + 1];
  }

  async buildMessage(config: {
    trade: {
      frontend: string,
      backend: string,
      producter: string,
      tester: string,
    },
    goods: {
      frontend: string,
      backend: string,
      producter: string,
      tester: string,
    }
  }, members: Member[]) {
    let tradeMember: Member = {
      memberId: '',
      name: '未知',
    }
    if (config.trade.frontend) {
      tradeMember = members.find(item => item.name.includes(config.trade.frontend)) as Member;
    }
    let goodsMember: Member = {
      memberId: '',
      name: '未知',
    }
    if (config.goods.frontend) {
      goodsMember = members.find(item => item.name.includes(config.goods.frontend)) as Member;
    }
    const leaderMember: Member = members.find(item => item.name.includes('河清')) as Member;
    const nextMember = {
      trade: await this.getNextRecord('trade'),
      goods: await this.getNextRecord('goods'),
    };

    const msg: any = {
      "zh_cn": {
        "title": "值班提醒",
        "content": [
          [{"tag": "text", "text": "交易值班: "}, {
            "tag": "at",
            "user_id": tradeMember.memberId,
            "user_name": tradeMember.name,
          }],
          [{
            "tag": "text",
            "text": `与你相关: 后端(${get(config, 'trade.backend', '未知')}) 测试(${config.trade.tester}) 产品(${config.trade.producter})`
          },],
          [{"tag": "text", "text": ""},],
          [{"tag": "text", "text": "商品值班: "}, {
            "tag": "at",
            "user_id": goodsMember.memberId,
            "user_name": goodsMember.name,
          }],
          [{
            "tag": "text",
            "text": `与你相关: 后端(${get(config, 'goods.backend', '未知')}) 测试(${config.goods.tester}) 产品(${config.goods.producter})`
          },],
        ]
      }
    }
    if (nextMember.trade || nextMember.goods) {
      msg.zh_cn.content = [
        ...msg.zh_cn.content,
        [{"tag": "text", "text": ""},],
      ];
      let arr: any = [
        {"tag": "text", "text": "下期值班:"},
      ]
      if (nextMember.goods) {
        const name = nextMember.goods.fields['前端值班'][0].text.replace('[前端]', '');
        nextMember.goods = members.find(item => item.name.includes(name)) as Member;
        arr = [
          ...arr,
          {"tag": "text", "text": "商品: "},
          {"tag": "at", "user_id": nextMember.goods.memberId, "user_name": nextMember.goods.name},
        ];
      }
      if (nextMember.trade) {
        const name = nextMember.trade.fields['前端值班'][0].text.replace('[前端]', '');
        nextMember.trade = members.find(item => item.name.includes(name)) as Member;
        arr = [
          ...arr,
          {"tag": "text", "text": "交易: "},
          {"tag": "at", "user_id": nextMember.trade.memberId, "user_name": nextMember.trade.name},
        ];
      }
      msg.zh_cn.content = [
        ...msg.zh_cn.content,
        arr,
      ]
    }
    msg.zh_cn.content = [
      ...msg.zh_cn.content,
      [{"tag": "text", "text": ""},],
      [{"tag": "text", "text": "----------------------------------------"},],
      [
        {"tag": "text", "text": "其他值班信息请查阅: "},
        {
          "tag": "a",
          "href": "https://j.youzan.com/bkrk40",
          "text": "产品技术汇总值班表"
        },
      ],
    ]
    if (!config.trade.frontend || !config.goods.frontend) {
      let domain = [];
      if (!config.goods.frontend) domain.push('商品');
      if (!config.trade.frontend) domain.push('交易');
      const nextNotice = [
        {"tag": "text", "text": `请尽快确认 ${domain.join('、')} 本期值班人员`},
        {"tag": "at", "user_id": leaderMember.memberId, "user_name": leaderMember.name,}
      ];
      msg.zh_cn.content.push(nextNotice);
    }
    if (!nextMember.trade || !nextMember.goods) {
      const domain = [];
      if (!nextMember.goods) domain.push('商品');
      if (!nextMember.trade) domain.push('交易');
      const nextNotice = [
        {"tag": "text", "text": `请尽快确认 ${domain.join('、')} 下期值班人员`},
        {"tag": "at", "user_id": leaderMember.memberId, "user_name": leaderMember.name,}
      ];
      msg.zh_cn.content.push(nextNotice);
    }

    return msg;
  }

  async postOnDutyAlertJson() {
    const { ctx } = this;
    const isNeedAlert = await this.isNeedAlert();
    if (!isNeedAlert) {
      return ctx.json(0, '无需通知', {
        isNeedAlert,
      });
    }

    const onDutyService = new OnDutyService(ctx);
    const [records, members] = await Promise.all([
      onDutyService.getBitableRecords({
        appToken: ON_DUTY_BITABLE.APP_TOKEN,
        tableId: ON_DUTY_BITABLE.TABLE_ID.CURRENT_ON_DUTY,
      }),
      onDutyService.getChatMembers(CHAT_ID.GOODS_AND_TRADE),
    ]);
    const tradeRecord: any[] = records.find((_: { fields: never }) => _.fields['团队'] === '社电交易');
    const goodsRecord: any[] = records.find((_: { fields: never }) => _.fields['团队'] === '社电商品+库存');

    const msg = await this.buildMessage(
      {
        trade: {
          frontend: get(tradeRecord, "fields['前端值班人员'][0].text").replace('[前端]', ''),
          backend: get(tradeRecord, "fields['后端值班人员'][0]").text,
          producter: get(tradeRecord, "fields['产品']"),
          tester: get(tradeRecord, "fields['测试']"),
        },
        goods: {
          frontend: get(goodsRecord, "fields['前端值班人员'][0].text").replace('[前端]', ''),
          backend: get(goodsRecord, "fields['后端值班人员'][0]").text,
          producter: get(goodsRecord, "fields['产品']"),
          tester: get(goodsRecord, "fields['测试']"),
        },
      },
      members
    );
    const res = await onDutyService.sendMessage(msg);
    ctx.json(0, 'ok', res);
  }
};
