const { join } = require('path');

module.exports = {
  root: true,
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: join(__dirname, '../tsconfig.json'),
    ecmaFeatures: {
      modules: true,
    },
  },
  env: {
    node: true,
    es6: true,
  },
  extends: ['eslint:recommended', 'google', 'plugin:@typescript-eslint/recommended', 'plugin:prettier/recommended'],
  plugins: ['lean-imports', '@typescript-eslint'],

  rules: {
    'max-len': [
      'error',
      {
        code: 125,
        tabWidth: 2,
        ignoreUrls: true,
        ignorePattern: '^\\s*var\\s.+=\\s*require\\s*\\(',
      },
    ],
    'require-jsdoc': 'off',
    'valid-jsdoc': 'off',
    'no-console': 'warn',
    'lean-imports/import': ['error', ['zan-utils', 'date-fns']],
    'prettier/prettier': 'warn',
    'require-atomic-updates': 'warn',
    camelcase: [
      'warn',
      {
        properties: 'always',
        allow: ['buyer_id', 'youzan_user_id', 'kdt_id'],
      },
    ],
    'no-invalid-this': 'off',
    // typescript lint 相关配置
    '@typescript-eslint/no-var-requires': 'off',
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/camelcase': 'warn',
    '@typescript-eslint/ban-ts-ignore': 'off',
  },
};
