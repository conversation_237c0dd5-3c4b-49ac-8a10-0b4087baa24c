import kafka from 'kafka-node';
import config from './config';

try {
  const Producer = kafka.HighLevelProducer;
  const client = new kafka.KafkaClient({ kafkaHost: config.kafka_server });
  const producer = new Producer(client);
  const kafkaTopic = config.kafka_topic;
  // eslint-disable-next-line
  console.log(kafkaTopic);
  const payloads = [
    {
      topic: kafkaTopic,
      messages: ['dada'],
    },
  ];

  producer.on('ready', async function() {
    // eslint-disable-next-line
    console.log('ready');

    const push_status = function() {
      producer.send(payloads, (err, data) => {
        if (err) {
          // eslint-disable-next-line
          console.log('[kafka-producer -> ' + kafkaTopic + ']: broker update failed', err);
        } else {
          // eslint-disable-next-line
          console.log('[kafka-producer -> ' + kafkaTopic + ']: broker update success', data);
        }
      });
    };

    setInterval(() => {
      push_status();
    }, 3000);
  });

  producer.on('error', function(err: Error) {
    // eslint-disable-next-line
    console.log(err);
    // eslint-disable-next-line
    console.log('[kafka-producer -> ' + kafkaTopic + ']: connection errored');
    throw err;
  });
} catch (e) {
  // eslint-disable-next-line
  console.log(e);
}
