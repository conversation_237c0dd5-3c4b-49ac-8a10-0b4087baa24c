import kafka from 'kafka-node';
import config from './config';
import SkynetLogService from '../../services/api/skynet-log/excuter/excuteService';

export default function(ctx: any) {
  function onError(error: Error) {
    // eslint-disable-next-line
    console.error(error);
    // eslint-disable-next-line
    console.error(error.stack);
  }

  // const skynetLog = new SkynetLogService(ctx);

  // const message = JSON.stringify({
  //   app: 'wsc-h5-vis',
  //   item: 'wsc-h5-vis 请求错误（排除了超时',
  //   time: 1591888915000,
  // });

  // const message = JSON.stringify({
  //   app: 'wsc-h5-vis',
  //   item: 'wsc-h5-vis 非超时报警',
  //   time: 1591336740000,
  // });

  // const message = JSON.stringify({
  //   app: 'wsc-pc-vis',
  //   item: 'wsc-pc-vis 非超时报警',
  //   time: 1590741643000,
  // });

  // const message = JSON.stringify({
  //   app: 'wsc-pc-statcenter',
  //   item: 'wsc-pc-statcenter 告警',
  //   time: 1593398534000,
  // });

  // skynetLog.excute(message);

  function onMessage(message: kafka.Message) {
    const skynetLog = new SkynetLogService(ctx);
    let alertMsgJson: any;
    try {
      alertMsgJson = JSON.parse(message.value as string);
    } catch {
      alertMsgJson = undefined;
    }

    // eslint-disable-next-line
    console.log('read msg Topic="%s" Partition=%s Offset=%d value=%s', message.topic, message.partition, message.offset, message.value);

    if (!alertMsgJson) {
      // eslint-disable-next-line
      console.log('alertMsgJson 失败')
    }

    skynetLog.excute(message.value as string);
  }
  try {
    const ConsumerGroup = kafka.ConsumerGroup;

    const consumerOptions = {
      kafkaHost: config.kafka_server,
      groupId: 'ExampleTestGroup-dd',
      // groupId: 'kafka_to_alarm',
      id: 'consumer1',
      sessionTimeout: 15000,
      // protocol: ['roundrobin'],
      // fromOffset: 'latest', // equivalent of auto.offset.reset valid values are 'none', 'latest', 'earliest'
    };

    const topics = [config.kafka_topic];

    const consumerGroup = new ConsumerGroup(consumerOptions, topics);

    // eslint-disable-next-line
    console.log('初始化 consumerGroup');
    consumerGroup.on('error', onError);
    consumerGroup.on('message', onMessage);
  } catch (error) {
    // eslint-disable-next-line
    console.error(error);
  }
}
