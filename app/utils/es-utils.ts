// const curl = new (require('curl-request'))();
import get from 'lodash/get';
import axios from 'axios';
import queryString from 'qs';

export const parseQuery = (querystring: string) => {
  const str = querystring;
  const parsed = queryString.parse(str);
  return parsed;
};


export const curlRequest = async (ES_URL: string, aggregation: string) => {
  return new Promise((resolve, reject) => {
    axios({
      method: 'post',
      url: ES_URL,
      data: aggregation,
    })
      .then((resp: any) => {
        resolve(resp.data);
      })
      .catch((e: any) => {
        reject(e);
      });
  });
};

export const parseProjectAggregations = (result: any) => {
  if (!result) {
    return;
  }

  const data = get(result, 'unique_project.buckets');
  const responseData: { name: string; type: string; count: number }[] = [];
  if (data) {
    data.map((item: { key: string; unique_type: any }) => {
      const { key, unique_type } = item;
      const subData = unique_type.buckets;
      if (subData) {
        for (const subquery of subData) {
          responseData.push({
            name: key,
            type: subquery.key,
            count: subquery.doc_count,
          });
        }
      }
      return item;
    });
  }
  return responseData;
};