import { MonitorDomain, TokenDomain, LogDomain } from '@constants/skynet-domain';
import { AxiosRequestConfig } from 'axios';

import qs from 'qs';
import { baseAjax } from './ajax';

interface INotifyLink {
  title: string;
  link: { url: string; alias: string };
}

interface ILogEvent {
  message: string;
  error?: string;
}

export interface ISkynetMsg {
  logUrl: string;
  message: string;
}

const env = (process.env.NODE_ENV || 'prod') as keyof typeof MonitorDomain;

const auth = {
  token: '',
  expireTime: 0,
};

/**
 * 查询对接 ops 的 token
 */
const opsJwtSign = (data: { key: string; password: string }) =>
  baseAjax({
    url: `${TokenDomain[env]}/apisix/plugin/ops-jwt/sign`,
    method: 'POST',
    data,
  }).catch(error => {
    // eslint-disable-next-line no-console
    console.error('获取 OPS JWTTOKEN 错误', error);
    throw error;
  });

const setJwtToken = async () => {
  const data = await opsJwtSign({
    key: 'qmg_rundeck',
    password: 'ef7e9089b1554897bb392455df6a5e8a',
  });
  auth.token = data;
  auth.expireTime = Date.now() + 43200000;
};

const transToArray = (str: string[] | string) => (Array.isArray(str) ? str : [str]);

/** 获取打印消息组合 */
const getMessageGroup = (logEvents: ILogEvent[]) => {
  return logEvents.reduce((result: Record<string, number>, logEvent) => {
    const sliceMsg = logEvent.message.slice(0, 500);
    const errorReg = /Error:(.*)\n/;
    const matchErrorMsg = errorReg.exec(logEvent.error || '');
    const errorMsg = matchErrorMsg ? matchErrorMsg[1].slice(0, 100) : '没有堆栈内容';
    const log = sliceMsg + '\n\nstack信息：' + errorMsg;
    if (!log) return result;
    if (!result[log]) {
      result[log] = 0;
    }
    result[log]++;
    return result;
  }, {});
};

async function skynetAjax(options: AxiosRequestConfig) {
  if (auth.expireTime <= Date.now()) {
    await setJwtToken();
  }
  options.headers = Object.assign({}, options.headers, { Authorization: auth.token });
  return baseAjax(options);
}

const getMonitorInfo = async (montiorId: number) => {
  return skynetAjax({
    url: `${MonitorDomain[env]}/v2/monitorWithNotifyingSettings/${montiorId}`,
  }).catch(error => {
    // eslint-disable-next-line no-console
    console.error('获取天网日志错误', error);
    throw error;
  });
};

export const getMonitorLink = async (montiorId: number) => {
  const monitorInfo = await getMonitorInfo(montiorId);
  const monitorLinkInfo = monitorInfo.notifyingLinks.find((notifyLink: INotifyLink) => notifyLink.title === '日志详情');
  return monitorLinkInfo.link.url;
};

export const getSkynetLog = async (url: string, searchLogTime?: number): Promise<ISkynetMsg> => {
  const queryStr = url.split('?')[1];
  const params = qs.parse(queryStr, { ignoreQueryPrefix: true });
  const timestampEndMs = Date.now();
  const totalTime = searchLogTime || 5;
  const timestampBeginMs = timestampEndMs - totalTime * 60 * 1000;
  const { tags = [] } = params;
  const tagConditions = transToArray(tags).map((tag: string) => {
    const res = [];
    const splitTag = tag.split('=');
    res.push(splitTag[0], 'eq', splitTag[1]);
    return res;
  });
  const { level } = params;
  const queryParams = {
    app: params.appName,
    levelArray: transToArray(level),
    timestampBeginMs,
    timestampEndMs,
    direction: 'DESC',
    tagConditions,
  };
  return skynetAjax({
    url: `${LogDomain[env]}/api/search/search`,
    method: 'POST',
    data: queryParams,
  }).then(data => {
    const { logEvents = [] } = data;
    const messageGroup = getMessageGroup(logEvents);
    const messages = Object.entries(messageGroup)
      .sort((a, b) => b[1] - a[1])
      .map(groupArr => `[日志次数：${groupArr[1]}]：${groupArr[0]}`);
    return {
      logUrl: url,
      message: messages.join('\n\n').slice(0, 5000),
    };
  });
};
export const getMessage = async (montiorId: number, searchLogTime?: number): Promise<ISkynetMsg> => {
  const url = await getMonitorLink(montiorId);
  return getSkynetLog(url, searchLogTime);
};
