import axios, { AxiosRequestConfig } from 'axios';
import { defaults, toLower } from 'lodash';

export function baseAjax(options: AxiosRequestConfig) {
  if (toLower(options.method) === 'post') {
    options.headers = defaults(options.headers, {
      'Content-Type': 'application/json',
    });
  }

  if (typeof options.method === 'undefined' || toLower(options.method) === 'get') {
    options.params = defaults(options.params, options.data);
    delete options.data;
  }

  return axios(options).then(({ data }) => data);
}
