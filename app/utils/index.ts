import os from 'os';

export function getIPAddress() {
  const interfaces = os.networkInterfaces();
  for (const interfaceKey in interfaces) {
    if (Object.prototype.hasOwnProperty.call(interfaces, interfaceKey)) {
      const iface = interfaces[interfaceKey];
      const len = iface.length;
      for (let i = 0; i < len; i++) {
        const ifaceObj = iface[i];
        if (ifaceObj.family === 'IPv4' && ifaceObj.address !== '127.0.0.1' && !ifaceObj.internal) {
          return ifaceObj.address;
        }
      }
    }
  }
}

export function isHttp(str: string) {
  return /^https?:\/\//.test(str);
}

export const transMap = (valueMap: Record<string, any>, textMap: Record<string, any>) => {
  return Object.keys(valueMap).reduce<Record<string, any>>((prev, curr) => {
    prev[valueMap[curr]] = textMap[curr];
    return prev;
  }, {});
};

export const transToMinute = (millisecond: number) => {
  return millisecond / 1000 / 60;
};
