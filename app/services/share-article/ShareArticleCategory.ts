import BaseService from '../base/BaseService';
import ShareArticleCategoryModel from '@models/share-article/ShareArticleCategory';
import { getManager, getConnection } from 'typeorm';
import { ICategoryQuery } from '@definitions/base';

export = class ShareArticleService extends BaseService {
  public async getAll(query: ICategoryQuery) {
    const where: any = {};
    if (query.category) {
      where.category = query.category;
    }
    const dataset = await getManager().find(ShareArticleCategoryModel, {
      where,
      order: {
        id: 'DESC',
      },
      skip: query.page - 1,
      take: query.pageSize,
    });
    const total = await getManager().count(ShareArticleCategoryModel);
    return {
      dataset,
      pageInfo: {
        page: query.page,
        pageSize: query.pageSize,
        total,
      },
    };
  }

  public async create(model: ShareArticleCategoryModel) {
    const result = await getConnection()
      .createQueryBuilder()
      .insert()
      .into(ShareArticleCategoryModel)
      .values(model)
      .execute();

    return result;
  }

  public async update(model: ShareArticleCategoryModel) {
    const id = model.id;
    delete model.id;

    const result = await getConnection()
      .createQueryBuilder()
      .update(ShareArticleCategoryModel)
      .set(model)
      .where('id = :id', { id })
      .execute();

    return result;
  }

  public async delete(id: number) {
    const result = await getConnection()
      .createQueryBuilder()
      .delete()
      .from(ShareArticleCategoryModel)
      .where('id = :id', { id })
      .execute();

    return result;
  }
};
