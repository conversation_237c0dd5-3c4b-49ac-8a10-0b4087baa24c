import BaseService from '../base/BaseService';
import ShareArticleModel from '@models/share-article/ShareArticle';
import { getManager, getConnection } from 'typeorm';
import { PageInfo } from '@definitions/base';

export = class ShareArticleService extends BaseService {
  public async getAll(query: PageInfo) {
    const dataset = await getManager().find(ShareArticleModel, {
      order: {
        id: 'DESC',
      },
      skip: query.page - 1,
      take: query.pageSize,
    });
    const total = await getManager().count(ShareArticleModel);
    return {
      dataset,
      pageInfo: {
        page: query.page,
        pageSize: query.pageSize,
        total,
      },
    };
  }

  public async create(article: ShareArticleModel) {
    const result = await getConnection()
      .createQueryBuilder()
      .insert()
      .into(ShareArticleModel)
      .values(article)
      .execute();

    return result;
  }

  public async update(article: ShareArticleModel) {
    const id = article.id;
    delete article.id;

    const result = await getConnection()
      .createQueryBuilder()
      .update(ShareArticleModel)
      .set(article)
      .where('id = :id', { id })
      .execute();

    return result;
  }

  public async delete(id: number) {
    const result = await getConnection()
      .createQueryBuilder()
      .delete()
      .from(ShareArticleModel)
      .where('id = :id', { id })
      .execute();

    return result;
  }
};
