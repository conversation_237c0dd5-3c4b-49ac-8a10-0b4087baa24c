import _ from 'lodash';
import { FE_TEAM } from '../../../constants/xiaolv';
import BaseService from '../../base/BaseService';
import WechatWorkService from '../wechatwork/WechatWorkService';
import { IYouzanFrameworkContext } from '@youzan/youzan-framework/definitions';
import { CHAT_ID } from '@services/api/robot/config';
import { v4 as uuidv4 } from "uuid";
import { ITaskResult } from 'definitions/xiaolv/Daily';
import { MarconiLark } from '@youzan/marconi-lark-sdk';

type Member = {
  memberId: string,
  name: string,
}

export = class OnDutyService extends BaseService {
  sdk: MarconiLark;

  constructor(ctx: IYouzanFrameworkContext) {
    super(ctx);
    this.sdk = new MarconiLark({
      app: {
        appId: '225e9c',
        secret: 'bd61b4508a604c8b8715c7da649e8ed4',
      },
    });
  }

  async getBitableRecords(config: { appToken: string; tableId: string }, retryTimes = 3): Promise<any> {
    const {appToken, tableId} = config;
    try {
      const res = await this.sdk.request({
        method: 'GET',
        url: `https://open.feishu.cn/open-apis/bitable/v1/apps/${appToken}/tables/${tableId}/records`,
      });
      return res.items;
    } catch (e) {
      return await this.getBitableRecords(config, retryTimes-1);
    }
  }

  async getChatMembers(chatId: string): Promise<Member[]> {
    const res = await this.sdk.request({
      method: 'GET',
      url: `https://open.feishu.cn/open-apis/im/v1/chats/${chatId}/members`,
    });
    return res.items;
  }

  async sendMessage(content: {}) {
    const res = this.sdk.request(({
      method: 'POST',
      url: `https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=chat_id`,
      data: {
        receive_id: CHAT_ID.GOODS_AND_TRADE,
        msg_type: "post",
        content: JSON.stringify(content),
        uuid: uuidv4(),
      }
    }));
    return res;
  }
};
