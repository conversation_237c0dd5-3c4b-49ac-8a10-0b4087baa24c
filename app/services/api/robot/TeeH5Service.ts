import BaseService from '../../base/BaseService';
import { IYouzanFrameworkContext } from '@youzan/youzan-framework/definitions';
import { v4 as uuidv4 } from 'uuid';
import { MarconiLark } from '@youzan/marconi-lark-sdk';
import axios from 'axios';
import ServiceError from '@youzan/youzan-framework/app/exceptions/ServiceError';
import {APP, USER_ID, headers, GITLAB_ACCESS_TOKEN} from '@services/api/robot/config';
import args from '@youzan/utils/url/args';

enum UITEST_ENV {
  PRE = 1,
}

function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

enum SYNC_REPO_TAG_STAGE {
  PRE_VERIFY = 1,
  WHEN_MERGE_MASTER,
}

export = class TeeH5Service extends BaseService {
  sdk: MarconiLark;

  constructor(ctx: IYouzanFrameworkContext) {
    super(ctx);
    this.sdk = new MarconiLark({ app: APP.ROBOT });
  }

  /**
   * 获取指定大巴车信息
   * @param busId
   */
  async getBusInfo(busId: number) {
    const resp = await axios.get(`https://bus.qima-inc.com/bus/bus/queryById?busId=${busId}`, {
      headers: headers.bus,
    });
    const res = resp.data;
    if (!res.success) {
      throw new ServiceError(res.code, res.message);
    }
    return res.data;
  }

  async getWscVersionList() {
      const res = await axios.post('https://mmp.prod.qima-inc.com/api/versions/list', {
          versionType: 0,
          shopType: 0,
          businessType: 1,
          accountType: 2,
      });
      // return res.data;
      // fetch("https://mmp.prod.qima-inc.com/api/versions/list", {
      //     "headers": {
      //         "accept": "application/json, text/plain, */*",
      //         "content-type": "application/json",
      //         "sec-ch-ua": "\"Chromium\";v=\"118\", \"Google Chrome\";v=\"118\", \"Not=A?Brand\";v=\"99\"",
      //         "sec-ch-ua-mobile": "?0",
      //         "sec-ch-ua-platform": "\"macOS\"",
      //         "Referer": "https://mmp.prod.qima-inc.com/version/list?mmpType=1&versionType=1&shopType=0&businessType=1&accountType=2",
      //         "Referrer-Policy": "strict-origin-when-cross-origin"
      //     },
      //     "body": "{\"versionType\":0,\"shopType\":0,\"businessType\":1,\"accountType\":2}",
      //     "method": "POST"
      // });
  }

  async getDetectionList() {
      const resp = await axios.get(`https://mmp.prod.qima-inc.com/mp-ops/volume/detection/list`, {
          params: {
              page: 1,
              size: 100,
          },
          headers: headers.mmp,
      });
      const res = resp.data;
      return res.data;
  }

  async chatsSearch(query: string, app = APP.ROBOT) {
    const sdk = new MarconiLark({ app });
    const url = args.add('https://open.feishu.cn/open-apis/im/v1/chats/search', {
      query,
    });
    const res = await sdk.request({ method: 'GET', url });
    return res.items![0];
  }

  async getChatList(app = APP.ROBOT) {
    const sdk = new MarconiLark({ app });
    const url = args.add('https://open.feishu.cn/open-apis/im/v1/chats', {
      sort_type: 'ByActiveTimeDesc',
      page_size: 100,
    });
    const res = await sdk.request({ method: 'GET', url });
    return res.items;
  }

  async sendMessage(content: any, msg_type = 'text', receive_id_type = 'open_id', receive_id = USER_ID.TEST) {
    if (msg_type === 'text') {
      content = JSON.stringify({
        text: content,
      });
    }
    if (msg_type === 'post') {
      content = JSON.stringify({
        zh_cn: {
          title: content.title || '通知',
          content: content.content,
        }
      });
    }
    // @ts-ignore
    console.log(content);
    try {
        const res = await this.sdk.request({
            method: 'POST',
            url: `https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=${receive_id_type}`,
            data: {
                receive_id,
                msg_type,
                content,
                uuid: uuidv4(),
            },
        });
        return res;
    } catch (e) {
        console.log(e);
    }
  }

  async createChatGroup(busId: number) {
    const res = await axios.get(`https://bus.qima-inc.com/bus/bus/createChatGroup?busId=${busId}`, {
      headers: headers.bus,
    });
    return res.data.data;
  }

  async getChatMembers(chatId: string): Promise<any[]> {
    const url = args.add(`https://open.feishu.cn/open-apis/im/v1/chats/${chatId}/members`, {
      page_size: 100,
    });
    const res = await this.sdk.request({ method: 'GET', url });
    return res.items;
  }

  async getMergeBusLog(busId: number) {
    const res = await axios.get(`https://bus.qima-inc.com/bus/merge/getMergeBusLog?busId=${busId}`, {
      headers: headers.bus,
    });
    return res.data.data;
  }

  // 同点击「开始发车」
  async startMerge(busId: number) {
    const res = await axios.get(`https://bus.qima-inc.com/bus/bus/checkStart?busId=${busId}`, {
      headers: headers.bus,
    });
    return res.data;
  }

  async queryByBranch(branchId: number) {
    const res = await axios.get(`https://bus.qima-inc.com/bus/review/queryByBranch?branchId=${branchId}`, {
      headers: headers.bus,
    });
    return res.data.data;
  }

  // 同点击「同步分支信息」
  async syncRepoTag(stage: SYNC_REPO_TAG_STAGE, busInfo: Record<string, any>) {
    // @ts-ignore
    console.log('执行同步分支信息');
    const mainRepo = {
      applicationName: "wsc-tee-h5",
      projectId: 11795,
    };
    const { busBaseInfo, branchInfos = [] } = busInfo;
    const { busName, busId } = busBaseInfo;
    const applicationList = branchInfos.reduce((prev: string[], cur: any) => {
      return [
        ...prev,
        cur.application,
      ];
    }, []);
    // @ts-ignore
    console.log(applicationList);
    const repoList = branchInfos.reduce((prev: string[], cur: any) => {
      const projectId = cur.branchInfos[0].gitId;
      if (prev.findIndex((item: any) => item.projectId === projectId) !== -1) return prev;
      return [
        ...prev,
        {
          branch: stage === SYNC_REPO_TAG_STAGE.PRE_VERIFY ? busBaseInfo.busMergeBranch : 'master',
          projectId,
        },
      ];
    }, [{
      branch: stage === SYNC_REPO_TAG_STAGE.PRE_VERIFY ? busBaseInfo.busMergeBranch : 'master',
      projectId: mainRepo.projectId
    }]);
    const mergeRequest = await branchInfos.reduce(async (prev: string[], cur: any) => {
      const key = cur.branchInfos[0].gitId;
      const mergeIds = await cur.branchInfos.reduce(async (curBranchInfosPrev: number[], curBranchInfosCur: any) => {
        const branchInfo = await this.queryByBranch(curBranchInfosCur.branchId);
        return [
          ...await curBranchInfosPrev,
          branchInfo.mergeId,
        ];
      }, []);
      return {
        ...await prev,
        [key]: mergeIds,
      }
    }, {});

    // 如果都是本地化的仓库 默认把wsc-tee-h5加到repoList中
    const data = {
      stage,
      commitMsg: stage === SYNC_REPO_TAG_STAGE.PRE_VERIFY ? "bus同步分支" : "bus同步版本",
      busId: busId,
      applicationList,
      repoList,
      busName,
      branch: busBaseInfo.busMergeBranch,
      mergeRequest,
    }

    const res = await axios.post('https://bus.qima-inc.com/bus/bus/syncRepoTag', data, { headers: headers.bus });
    return res.data;
  }

  // 获取当前的发车状态信息
  async getApplicationStatus(busId: number) {
    const res = await axios.get(`https://bus.qima-inc.com/bus/bus/applicationStatus?busId=${busId}`, {
      headers: headers.bus,
    });
    return res.data.data;
  }

  async getChatMessages(chatId: string, params: {
    page_size: string;
    page_token: string;
    start_time?: string;
  } = { page_size: '50', page_token: '' }): Promise<any[]> {
    const url = args.add('https://open.feishu.cn/open-apis/im/v1/messages', {
      container_id_type: 'chat',
      container_id: chatId,
      ...params,
    });
    const res = await this.sdk.request({ method: 'GET', url });
    if (res.hasMore) {
      return [
        ...res.items,
        ...await this.getChatMessages(chatId, {
          ...params,
          page_token: res.pageToken,
        }),
      ]
    }
    return res.items;
  }

  async queryPageDiffReportDetails(reportId, pageNo = 1, items = []) {
    const res = await axios.post('https://pagediff-front.prod.qima-inc.com/pagediff/report/queryReportDetails', {
      reportId,
      pageNo,
      pageSize: 50,
      userType: 'page',
    }, {
      headers: headers.uitest,
    });
    const { totalCount } = res.data.data.paginator;
    items = [...items, ...res.data.data.items];
    if (items.length < totalCount) {
      return await this.queryPageDiffReportDetails(reportId, pageNo+1, items);
    }
    return items;
  }

  // 将用户拉入群聊中
  async joinChat(chatId: string, userIds: string[], app = APP.ROBOT, member_id_type = 'open_id') {
    const sdk = new MarconiLark({ app });
    const url = args.add(`https://open.feishu.cn/open-apis/im/v1/chats/${chatId}/members`, {
      member_id_type,
    });
    const res = await sdk.request({
      method: 'POST',
      url,
      data: {
        id_list: userIds,
      }
    });
    console.log(res);
    return res.data;
  }

  // 将当前用户主动加入群聊中
  async joinMeToChat(chatId: string, userIds: string[]) {
    try {
      const res = await this.sdk.request({
        method: 'PATCH',
        url: `https://open.feishu.cn/open-apis/im/v1/chats/${chatId}/members/me_join`,
      });
      console.log(res);
      return res;
    } catch (e) {
      console.log(e);
    }
  }

  async getAllTaskForUITest(releaseId) {
    const url = args.add('https://uitest.prod.qima-inc.com/api/tasks/queryByPage', {
      releaseId,
    });
    const res = await axios.get(url, {
      headers: headers.uitest,
    });
    return res.data.data;
  }

  async getReportForPageDiff(id) {
    const url = args.add('https://pagediff-front.prod.qima-inc.com/pagediff/report/get', {
      id,
    });
    const res = await axios.get(url, {
      headers: headers.uitest,
    });
    return res.data;
  }

  /**
   * 开始ui-test
   * @param appName
   */
  async startUITest(appName: string) {
    const queryUITestApp = async (appName: string) => {
      const res = await axios.get(`https://uitest.prod.qima-inc.com/api/apps/queryByPage?current=1&name=${appName}&pageSize=20`, {
        headers: headers.uitest,
      });
      return res.data.data.rows[0];
    }
    const getTestListByAppId = async (appId: number) => {
      const res = await axios.get(`https://uitest.prod.qima-inc.com/api/apps/release?current=1&name=NaN&pageSize=5&id=${appId}`, {
        headers: headers.uitest,
      });
      return res.data.data.rows[0];
    }
    const uitestApp = await queryUITestApp(appName);
    const appId = uitestApp.id;
    await axios.post('https://uitest.prod.qima-inc.com/api/application/manual/apps', { appId, env: UITEST_ENV.PRE }, {
      headers: headers.uitest,
    });
    await sleep(1000);
    const testLog = await getTestListByAppId(appId);
    return `https://uitest.prod.qima-inc.com/tasks?crontabId&releaseId=${testLog.id}&usecaseId`;
  }

  // 开始页面对比
  async startPageDiff(appName: 'wsc-h5-trade' | 'wsc-h5-goods'): Promise<string[]> {
    const queryReport = async (executionId: number) => {
      const res = await axios.post('https://pagediff-front.prod.qima-inc.com/pagediff/report/queryReport', {
        pageNo: 1,
        pageSize: 20,
      }, {
        headers: headers.uitest,
      });
      return res.data.data.items.filter((item: any) => item.executionId === executionId)[0];
    }

    let suiteIds = [];
    switch (appName) {
      case 'wsc-h5-trade':
        suiteIds = [17];
        break;
      case "wsc-h5-goods":
        suiteIds = [10, 15];
        break;
    }

    if (!suiteIds) {
      // @ts-ignore
      console.log('测试集未知');
      return [];
    }

    const resList = await Promise.all(suiteIds.map((suiteId) => {
      return axios.post('https://pagediff-front.prod.qima-inc.com/pagediff/execution/suite/runExecuteSuite', {
        suiteId,
        creator: 'shenjingnan',
        projectKey: '',
      }, {
        headers: headers.uitest,
      });
    }));
    return resList.map((res) => {
      if (res.data.success) {
        return `https://sd-tech.prod.qima-inc.com/page-diff/report-manage/report-detail/${res.data.data}`;
      }
      // @ts-ignore
      console.log('创建页面对比任务失败', res.data);
      return null;
    })
  }

  // 开始合入master
  async mergeIntoMaster(busId: number) {
    const data = { busId };
    const res = await axios.post('https://bus.qima-inc.com/bus/publish/finish', data, { headers: headers.bus });
    return res.data;
  }

  async getAppDetail(appName: string) {
    const res = await axios.get(`https://bus.qima-inc.com/bus/app/getApp?appName=${appName}`, {
      headers: headers.bus,
    });
    return res.data.data;
  }

  async deployTicketByOPS(appInfo: { applicationName: string; repository: string }, reason: string) {
    const data = {
      support_rollback: true,
      branch: 'master',
      pub_type: 'bluegreen',
      alert_is_cover: 1,
      reason,
      cd_publish_plan_id: 0,
      rollback_plan: '回滚应用:\napollo回滚:\nscm回滚:\nddl回滚:\ndml回滚：\n卡门或网关回滚：\n白名单控制回滚：',
      app_name: appInfo.applicationName,
      zone: 'prod',
      git_repo: appInfo.repository,
      buId: 1,
    };

    const res = await axios.post('https://ops.qima-inc.com/api/v1.0/ops/deploy_ticket/', data, {
      headers: headers.ops,
      withCredentials: true,
    });
    return res.data.data;
  }

  async getTicketInfo(id: number) {
    const res = await axios.get(`https://flow.qima-inc.com/opsflow-plus/api/v3/tickets/${id.toString(16)}`, {
      headers: headers.flow,
    });
    return res.data.data;
  }

  async getTicketAllFields(id: number) {
    const res = await axios.get(`https://flow.qima-inc.com/opsflow-plus/api/v3/tickets/${id.toString(16)}/all_fields`, {
      headers: headers.flow,
    });
    return res.data.data;
  }

  async checkBeforeFinish(busId: number) {
    const data = { busId };
    const res = await axios.post('https://bus.qima-inc.com/bus/publish/check-before-finish', data, {
      headers: headers.bus,
    });
    return res.data.data;
  }

    async getCommits(branchName) {
        const baseURL = 'https://gitlab.qima-inc.com/api/v4';
        const projectId = '1414'; // weapp/wsc
        const url = `${baseURL}/projects/${projectId}/repository/commits`;
        const res = await axios.get(url, {
            params: {
                ref_name: branchName,
            },
            headers: {
                'Authorization': `Bearer ${GITLAB_ACCESS_TOKEN}`
            }
        });
        const commits = res.data;
        return commits;
    };

    async getLatestPipeline(branchName) {
        const baseURL = 'https://gitlab.qima-inc.com/api/v4';
        const projectId = '12047'; // weapp/ext-tee-wsc-trade
        const url = `${baseURL}/projects/${projectId}/pipelines/latest`;
        const res = await axios.get(url, {
            params: {
                ref: branchName,
            },
            headers: {
                'Authorization': `Bearer ${GITLAB_ACCESS_TOKEN}`
            }
        });
        return res.data;
    };
};
