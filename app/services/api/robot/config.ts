export const ON_DUTY_BITABLE = {
  APP_TOKEN: 'bascnFLgvsASr5pQOjTJ4n3Ia4b',
  TABLE_ID: {
    CURRENT_ON_DUTY: 'tblHaGYAq9H9aia2', // 当前值班: https://qima.feishu.cn/wiki/wikcn2dZSll2wePaW6f7oJJWTed?table=tblHaGYAq9H9aia2&view=vewrozAnQP
    GOODS: 'tblW2FLSLErEbNQL', // 社电交易: https://qima.feishu.cn/wiki/wikcn2dZSll2wePaW6f7oJJWTed?table=tblI1m2dAdhzkvar&view=vewQPmIP2W
    TRADE: 'tblI1m2dAdhzkvar', // 社电商品+库存: https://qima.feishu.cn/wiki/wikcn2dZSll2wePaW6f7oJJWTed?table=tblW2FLSLErEbNQL&view=vewyTBK1sM
  },
};

export const APP = {
  BUS_SERVER: {
    appId: '21ec35',
    secret: '415d74154d1543e586e73984d5a309ca',
  },
  ROBOT: {
    appId: '225e9c',
    secret: 'bd61b4508a604c8b8715c7da649e8ed4',
  },
};

export const APP_ID = {
  ROBOT: 'cli_a164c52e28fb100d', // 营销日常提醒机器人的appId
};

export const CHAT_ID = {
  GOODS_AND_TRADE: 'oc_bfa15cbe5716389cc9ccac8077fb59d2', // 商品交易群
  TEST: 'oc_d072de6323bd091202e3f02d82724c64', // 机器人测试群
  GOODS_AND_TRADE_PUBLISH: 'oc_c8446eef8f9afc344e13cf149faec1bc', // 商品交易前端发布群
};

export const USER_ID = {
  TEST: 'ou_c000ec12204511475ce74f9e02f4f991', // 沈菁楠
  SHENJINGNAN: 'ou_c000ec12204511475ce74f9e02f4f991', // 沈菁楠
  ROBOT: 'ou_81470dd5664e995c7733e2d3ba1daeb4', // 营销日常提醒机器人的userId
};

export const GITLAB_ACCESS_TOKEN = '********************';

export const tester: Record<string, { name: string; memberId: string; memberIdType: string }> = {
  jiangsujiao: {
    name: '阿娇(姜素娇)',
    memberId: 'ou_7999c24e7001d5b730938c8e5248b0b5',
    memberIdType: 'open_id',
  },
  molili: {
    name: '莫丽丽',
    memberId: 'ou_463ee6ff5e8b3549ecc20d85387fa126',
    memberIdType: 'open_id',
  },
  zhangxiaolin: {
    memberId: 'ou_8e51eec50c52526a14158aca0426c6b6',
    memberIdType: 'open_id',
    name: '安歌(章晓琳)',
  },
};

export const bizOwner: Record<string, { name: string; memberId: string; memberIdType: string }> = {
  'wsc-h5-trade': {
    memberId: 'ou_c1d005fb62626be41c9623f3b8d959b3',
    memberIdType: 'open_id',
    name: '河清(王庆合)',
  },
  'wsc-h5-goods': {
    memberId: 'ou_c1d005fb62626be41c9623f3b8d959b3',
    memberIdType: 'open_id',
    name: '河清(王庆合)',
  },
  'wsc-h5-ump': {
    memberId: 'ou_ad6fe746c84b5f2433fcfb036dde92e3',
    memberIdType: 'open_id',
    name: '钱勇俊',
  },
  'wsc-h5-assets': {
    memberId: 'ou_31a77fc681ccb9dba8394ae63a35c5f7',
    memberIdType: 'open_id',
    name: '子一(王雄兵)',
  },
};

export const ticketStatusMap: Record<string, { msg: string; code: number }> = {
  '149': {
    msg: '应用QA审批',
    code: 1,
  },
  'c7': {
    code: 2,
  },
  'c9': {
    code: 200,
  },
};

export const baseWscBranchName = 'hotfix/v2.160';

export const headers = {
    bus: {
        token: '123',
        user: 'shenjingnan',
        cookie: 'DO_CHECK_YOU_VERSION=1; _ga=GA1.2.1572368238.1683877615; dfp=470d6bb46610ce153ea15ea1fce3c6fe; KDTSESSIONID=YZ1181310600849850368YZIweMvMJY; zan-bus.sid=Qh4t23BVnWhiOGPPM2zb/rox0kgUVxeU67hRopHkvnBmMSNcZkXwMJ3ZarSFDA+3aTXIpSHlcxZCSqoSWHu/nsWxxSi8fxeqPeq6P18uICIYzeA8lxSalpXJ67o0yF4q9fRZv0p5QtVEEAiux5qPuxX8eX3w/Xa+pGrme6zQ4W5vEjnDFtslL1ELMEKuADPsBbRVde6ERZPHzIne73x9sA==; zan-bus.sid.sig=FRSjbixZrcYmNJCTIaIsK2uF5vQ; cas_username=shenjingnan; access_user=14869_1; yz_log_uuid=2050fb4c-734f-8982-be40-ff3b478a5df7; yz_log_ftime=1702864250610; XIAOLV_SESSION_ID_prod=siTgx9XG01Uuh9ZJIjYaxxalnpcqKYPzn3wC68gd; loc_dfp=166da404d72229be6af7145ed7646986; cas=3e198bb3febd1a4492ef3019fb1456b7d4680d7c319abca3ba9c2b4e8a7c0259591; iamp.sid=9Kx2Hs7tMLHT2HjtdZUpHDLtxe1OxQAqqNqEDJZprgfW+ebz74HmJB6cKB2kxUs+BDfI3X48VPDWuXAYVioQz6nCIV/eoXi/MPzvIFQHSB27rlSg1wGBHFxTczb6cjMTOcS9ofOaoEjOvf5bPoEq5MSFJA3ZeODz2HzXYhkAZscowv0bvtZvFC77KfrIswO3ufz3ftGEzDYGXKIHt4ZOAQ==; yz_log_seqb=1703037421182; TSID=e13c3851f9194eadb31ceab363c7545a; JSESSIONID=C6C86377D0165BBB59053FCD9F8DB340; yz_log_seqn=21',
    },
    uitest: {
        cookie:
        'DO_CHECK_YOU_VERSION=1; _ga=GA1.2.1572368238.1683877615; dfp=470d6bb46610ce153ea15ea1fce3c6fe; KDTSESSIONID=YZ1181310600849850368YZIweMvMJY; cas_username=shenjingnan; access_user=14869_1; yz_log_uuid=2050fb4c-734f-8982-be40-ff3b478a5df7; yz_log_ftime=1702864250610; XIAOLV_SESSION_ID_prod=siTgx9XG01Uuh9ZJIjYaxxalnpcqKYPzn3wC68gd; uitest-app-prod.sid=sBOlafBnD6ahuwu3ByzGNHIec9YhuyWtmwIpfxOTwiD52h2DPzWW+Y5m53Uwm0l6uu3UAeyw2OgcdFVXf6j5G64s0aoGR0A0+DSw8HEGcX+MnTC1n9buLTYN/LaN6WNynKQWCEzwrPWTPZUARuyNWuitf1ZesomD4BJtja6doThEGtv4xQyb8NtnmYDU1taK9vq5qv1HoowgnsX6Wo1kfg==; uitest-app-prod.sid.sig=WvioBkdqeBFHydBfL6SfGkXg1HRbjSnp9qquUFR9c6Q; loc_dfp=166da404d72229be6af7145ed7646986; cas=3e198bb3febd1a4492ef3019fb1456b7d4680d7c319abca3ba9c2b4e8a7c0259591; iamp.sid=9Kx2Hs7tMLHT2HjtdZUpHDLtxe1OxQAqqNqEDJZprgfW+ebz74HmJB6cKB2kxUs+BDfI3X48VPDWuXAYVioQz6nCIV/eoXi/MPzvIFQHSB27rlSg1wGBHFxTczb6cjMTOcS9ofOaoEjOvf5bPoEq5MSFJA3ZeODz2HzXYhkAZscowv0bvtZvFC77KfrIswO3ufz3ftGEzDYGXKIHt4ZOAQ==; TSID=e13c3851f9194eadb31ceab363c7545a; yz_log_seqb=1703037421182; yz_log_seqn=27',
    },
    flow: {
      authorization:
      'Bearer eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJvcHNmbG93LXBsdXMiLCJleHAiOjE3MDMxMjM4ODEsImlhdCI6MTcwMjUxOTA4MSwic3ViIjoie1widXNlcm5hbWVcIjpcInNoZW5qaW5nbmFuXCIsXCJuaWNrbmFtZVwiOlwi5bC86I6rXCIsXCJ0ZW5hbnRJZFwiOlwiMVwifSJ9.RNd3Dc1oWkHopeW0ygNvqNLlw5mbTG9DUnm8wLEZkH4',
      cookie:
      'DO_CHECK_YOU_VERSION=1; _ga=GA1.2.1572368238.1683877615; dfp=470d6bb46610ce153ea15ea1fce3c6fe; KDTSESSIONID=YZ1181310600849850368YZIweMvMJY; cas_username=shenjingnan; access_user=14869_1; yz_log_uuid=2050fb4c-734f-8982-be40-ff3b478a5df7; yz_log_ftime=1702864250610; is_admin=false; username=shenjingnan; OPS_JWT_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTQ4NjksImFsaWFzbmFtZSI6IuWwvOiOqyIsInJlYWxuYW1lIjoi5rKI6I-B5qWgIiwiZW1haWwiOiJzaGVuamluZ25hbkB5b3V6YW4uY29tIiwibW9iaWxlIjoiMTU5NjgxNDg2MDkiLCJleHAiOjE3MDMyNTk5MTgsInVzZXJuYW1lIjoic2hlbmppbmduYW4iLCJ1c2VyX2lkIjoxNDg2OSwiaXNfYWRtaW4iOmZhbHNlLCJ0aW1lc3RhbXAiOjE3MDIyNTk5MTksImdlbmRlciI6ZmFsc2UsImtleSI6InNoZW5qaW5nbmFuIn0.3k1cKJ6sD6mGJ1fVWAqvGGxIEHBaiiUpvCO0OBlTZTg; XIAOLV_SESSION_ID_prod=siTgx9XG01Uuh9ZJIjYaxxalnpcqKYPzn3wC68gd; loc_dfp=166da404d72229be6af7145ed7646986; cas=3e198bb3febd1a4492ef3019fb1456b7d4680d7c319abca3ba9c2b4e8a7c0259591; iamp.sid=9Kx2Hs7tMLHT2HjtdZUpHDLtxe1OxQAqqNqEDJZprgfW+ebz74HmJB6cKB2kxUs+BDfI3X48VPDWuXAYVioQz6nCIV/eoXi/MPzvIFQHSB27rlSg1wGBHFxTczb6cjMTOcS9ofOaoEjOvf5bPoEq5MSFJA3ZeODz2HzXYhkAZscowv0bvtZvFC77KfrIswO3ufz3ftGEzDYGXKIHt4ZOAQ==; TSID=e13c3851f9194eadb31ceab363c7545a; yz_log_seqb=1703037421182; yz_log_seqn=27',
    },
    mmp: {
        Referer: "https://mp-ops.prod.qima-inc.com/volume/detection",
        cookie:
        'DO_CHECK_YOU_VERSION=1; _ga=GA1.2.1572368238.1683877615; dfp=470d6bb46610ce153ea15ea1fce3c6fe; KDTSESSIONID=YZ1181310600849850368YZIweMvMJY; cas_username=shenjingnan; access_user=14869_1; yz_log_uuid=2050fb4c-734f-8982-be40-ff3b478a5df7; yz_log_ftime=1702864250610; XIAOLV_SESSION_ID_prod=siTgx9XG01Uuh9ZJIjYaxxalnpcqKYPzn3wC68gd; loc_dfp=166da404d72229be6af7145ed7646986; cas=3e198bb3febd1a4492ef3019fb1456b7d4680d7c319abca3ba9c2b4e8a7c0259591; iamp.sid=9Kx2Hs7tMLHT2HjtdZUpHDLtxe1OxQAqqNqEDJZprgfW+ebz74HmJB6cKB2kxUs+BDfI3X48VPDWuXAYVioQz6nCIV/eoXi/MPzvIFQHSB27rlSg1wGBHFxTczb6cjMTOcS9ofOaoEjOvf5bPoEq5MSFJA3ZeODz2HzXYhkAZscowv0bvtZvFC77KfrIswO3ufz3ftGEzDYGXKIHt4ZOAQ==; last_view_path=%2Fshop-manage%2Findex; yz_log_seqb=1703037417504; yz_log_seqb=1703037421182; yz_log_seqn=28; yz_log_seqn=2',
    },
    ops: {
        token: '123',
        user: 'shenjingnan',
        cookie:
        'DO_CHECK_YOU_VERSION=1; _ga=GA1.2.1572368238.1683877615; dfp=470d6bb46610ce153ea15ea1fce3c6fe; KDTSESSIONID=YZ1181310600849850368YZIweMvMJY; cas_username=shenjingnan; access_user=14869_1; yz_log_uuid=2050fb4c-734f-8982-be40-ff3b478a5df7; yz_log_ftime=1702864250610; authority=user; is_admin=false; username=shenjingnan; OPS_JWT_TOKEN=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhbGlhc25hbWUiOiLlsLzojqsiLCJnZW5kZXIiOmZhbHNlLCJtb2JpbGUiOiIxNTk2ODE0ODYwOSIsInVzZXJuYW1lIjoic2hlbmppbmduYW4iLCJrZXkiOiJzaGVuamluZ25hbiIsImVtYWlsIjoic2hlbmppbmduYW5AeW91emFuLmNvbSIsImlkIjoxNDg2OSwiZXhwIjoxNzAzNTE4ODQyLCJ1c2VyX2lkIjoxNDg2OSwiaXNfYWRtaW4iOmZhbHNlLCJyZWFsbmFtZSI6IuayiOiPgealoCIsInRpbWVzdGFtcCI6MTcwMjUxODg0M30.rowOWydhrBPt0G3hOMWnjjlnM1SMdhLBB7QWv333A9k; buId=1; XIAOLV_SESSION_ID_prod=siTgx9XG01Uuh9ZJIjYaxxalnpcqKYPzn3wC68gd; loc_dfp=166da404d72229be6af7145ed7646986; cas=3e198bb3febd1a4492ef3019fb1456b7d4680d7c319abca3ba9c2b4e8a7c0259591; iamp.sid=9Kx2Hs7tMLHT2HjtdZUpHDLtxe1OxQAqqNqEDJZprgfW+ebz74HmJB6cKB2kxUs+BDfI3X48VPDWuXAYVioQz6nCIV/eoXi/MPzvIFQHSB27rlSg1wGBHFxTczb6cjMTOcS9ofOaoEjOvf5bPoEq5MSFJA3ZeODz2HzXYhkAZscowv0bvtZvFC77KfrIswO3ufz3ftGEzDYGXKIHt4ZOAQ==; yz_log_seqb=1703037421182; yz_log_seqn=30',
    },
};

export const STEP_STATUS_DICT_MP = {
  CREATED: {
    step: 'check',
    value: 'wait',
    description: '准备阶段',
    buttonStatus: 1,
  },
  CHANGED: {
    step: 'check',
    value: 'wait',
    description: '准备阶段',
    buttonStatus: 1,
  },
  MERGING_BUS_BRANCH: {
    step: 'mergeBus',
    value: 'process',
    description: '合并大巴车分支中',
    buttonStatus: 2,
  },
  MERGE_BUS_BRANCH_FAIL: {
    step: 'mergeBus',
    value: 'error',
    description: '合并大巴车分支失败',
    buttonStatus: 3,
  },
  MERGE_BUS_BRANCH_SUC: {
    step: 'mergeBus',
    value: 'process',
    description: '合并大巴车分支成功',
    buttonStatus: 4,
  },

  MERGE_BUS_SUC_UN_SYNC_BRANCH: {
    step: 'mergeBus',
    value: 'process',
    description: '合并大巴车分支成功但未同步分支',
    buttonStatus: 5,
  },
  MERGE_BUS_SUC_SYNC_BRANCH_SUC: {
    step: 'mergeBus',
    value: 'process',
    description: '合并大巴车分支成功且同步分支成功',
    buttonStatus: 6,
  },
  MERGE_MASTER_FAIL: {
    step: 'mergeMaster',
    value: 'error',
    description: '合并master失败',
    buttonStatus: 7,
  },
  MERGE_MASTER_SUC: {
    step: 'mergeMaster',
    value: 'process',
    description: '合并master分支成功',
    buttonStatus: 8,
  },
  MERGE_MASTER_SUC_UN_SYNC_VERSION: {
    step: 'mergeMaster',
    value: 'process',
    description: '合并master成功但未同步版本信息',
    buttonStatus: 9,
  },

  MERGE_MASTER_SUC_SYNC_VERSION_SUC: {
    step: 'mergeMaster',
    value: 'process',
    description: '合并master成功且同步版本信息成功',
    buttonStatus: 10,
  },
  FINISH: {
    step: 'mergeMaster',
    value: 'process',
    description: '结束',
    buttonStatus: 11,
  },
};
