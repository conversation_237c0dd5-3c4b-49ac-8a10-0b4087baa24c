import ownersConfigs from './config';
import { Isource } from '../typing';
interface IMakeArrange {
  (source: Isource, res: any, arranges: any): any;
}

const makeArrange: IMakeArrange = (source, res, arranges) => {
  const appName = source.app;
  const ownersConfig = arranges[appName] || {};

  const content: any[] = res.content;
  const withOwnersContent = content.map(o => {
    const referer = o.referer;
    let owners: any[] = ownersConfig.default || [];
    Object.keys(ownersConfig).forEach(o => {
      if (referer.search(o) > -1) {
        owners = ownersConfig[o];
      }
    });
    return {
      ...(o as any),
      owners: owners,
    };
  });

  res.content = withOwnersContent;
  return res;
};

export default makeArrange;
