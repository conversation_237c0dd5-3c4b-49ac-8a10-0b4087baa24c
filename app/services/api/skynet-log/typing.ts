export interface Isource {
  app: string; // 应用名
  alertName: string; // 报警名
  level: string; // 报警等级
  condition?: string; // 搜日志的内容
  logIndex?: string; // 日志所有
  logLevel?: string; // 日志的等级
}

export interface ISkynetlogParams {
  start: string;
  end: string;
  cache: boolean;
  skynetProject: Isource['app'];
  logIndexName: Isource['logIndex'];
  content?: Isource['condition'];
  level: Isource['logLevel'];
}

export type ILog = any;

export interface IAlertMsgJson {
  app: string;
  item: string;
  time: string;
  level: string;
  msg: string;
}
