import { Isource } from '../typing';
const sources: Isource[] = [
  {
    app: 'wsc-h5-vis',
    alertName: 'wsc-h5-vis 非超时报警',
    level: 'WARNING',
    condition: 'NOT 504 NOT timeout NOT time NOT data:context deadline exceeded NOT 网络超时 NOT fetch shared components error',
    logIndex: 'wsc_wap_vis_logs',
  },
  {
    app: 'wsc-h5-vis',
    alertName: 'wsc-h5-vis 请求错误（排除了超时',
    level: 'WARNING',
    condition: '[resquestError] NOT BusinessServiceError NOT timeout NOT 504',
    logIndex: 'wsc_wap_vis_logs',
    logLevel: 'warn',
  },
  {
    app: 'wsc-pc-vis',
    alertName: 'wsc-pc-vis 非超时报警',
    level: 'WARNING',
    condition: 'NOT 504 NOT timeout NOT time NOT context deadline exceeded',
    logIndex: 'wsc_pc_vis_log',
  },
];
export default sources;
