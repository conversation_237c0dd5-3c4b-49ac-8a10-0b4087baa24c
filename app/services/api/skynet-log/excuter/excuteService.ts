// @ts-ignore
import startRecordNodeLog from '@youzan/skynet-log';
import { FORMAT_TIME, IS_DEVELOPMENT_MODE } from '../constant';
import format from 'date-fns/format';
import BaseService from '../../../base/BaseService';
import { getSource, getSkynetlogParams, sleep, retryApolloRequest } from '../utils';
import makeArrange from '../arrange';
import { getNotifyMsg, getWechatFormatMsg, getStartNotifyMsg } from '../utils/notify';
import { getJiraList, getJiraNotify } from '../utils/jira';
import { getXiaolvList, getXiaolvNotify } from '../utils/xiaolv';
import WechatWorkService from '../../wechatwork/WechatWorkService';
import JiraService from '../../jira/IssueService';
import XiaolvService from '../../xiaolv/DemandApi';
import { IAlertMsgJson, Isource } from '../typing';

const advancedTimeDiff = 1000 * 60 * 5; // 5min

// import _ from 'lodash';

export = class IssueService extends BaseService {
  countIndex = 0;
  alertMsgJson: IAlertMsgJson | null = null;

  service: any;
  jiraService: any;
  xiaolvService: any;

  laterTime = 0;
  startStr = '';
  endSrt = '';

  init(msg: string) {
    this.countIndex = 0;

    let alertMsgJson;
    try {
      alertMsgJson = JSON.parse(msg);
    } catch {
      alertMsgJson = null;
    }

    this.alertMsgJson = alertMsgJson;

    if (alertMsgJson) {
      return true;
    }
  }

  initService() {
    this.service = new WechatWorkService(this.ctx);
    this.jiraService = new JiraService(this.ctx);
    this.xiaolvService = new XiaolvService(this.ctx);
  }

  initTime() {
    const { time } = this.alertMsgJson as IAlertMsgJson;
    // time 会抹掉实际时间的秒数，所以这里增加 1min
    const laterTime = Number(time + 1000 * 60);
    const startStr = format(laterTime - advancedTimeDiff, FORMAT_TIME);
    const endSrt = format(laterTime, FORMAT_TIME);

    this.laterTime = laterTime;
    this.startStr = startStr;
    this.endSrt = endSrt;

    // eslint-disable-next-line
    console.log(startStr, endSrt, laterTime, advancedTimeDiff);
  }

  async excute(msg: string) {
    if (this.init(msg) && this.alertMsgJson !== null) {
      // 1. 获取 source
      // 2. 获取 机器人
      // 3. 开始通知
      // 4. 兜底检索日志
      // 5. 获取 urlmap
      // 6. 得到 owner 对应的日志
      // 7. 通知 错误日志列表
      // 8. 创建 jira
      // 9. 通知 jira 信息

      // 1. 获取 source
      const source = await this.getSource();

      if (typeof source !== 'undefined' && source.logIndex) {
        // 实例化 service
        this.initService();

        // 初始化时间相关
        this.initTime();

        // 2. 获取 机器人
        const weChatRobot = await this.getWechatRobot(source);

        // 3. 开始通知
        await this.startNotify(source, weChatRobot);

        for (;;) {
          this.countIndex++;

          if (this.countIndex > 20) {
            await this.service.webhookMessageSend(getWechatFormatMsg('日志检索失败，请自行前往天网查询错误'), weChatRobot);
            return false;
          }

          try {
            const logResult = await this.recordLogs(source);

            if (!(logResult.content || []).length) {
              await sleep(8000);
              continue;
            }

            // TODO: 过滤白名单逻辑

            try {
              // 6. 得到 owner 对应的日志
              const resWithOwners = await this.getResWithOwners(source, logResult);

              // 7. 通知 错误日志列表
              await this.notifyMsg(source, resWithOwners, weChatRobot);

              // 8. 创建 通知 jira
              await this.makeJira(source, resWithOwners, weChatRobot);
            } catch (error) {
              // eslint-disable-next-line
              console.log('skynet-log-getOwnerserr', error);
              await this.service.webhookMessageSend(getWechatFormatMsg(error.message), 'dadaTest');
            }
          } catch (err) {
            // eslint-disable-next-line
            console.log('skynet-log-recordLogserr', err);
            try {
              await this.service.webhookMessageSend(getWechatFormatMsg(err.message), 'dadaTest');
            } catch (error) {
              // eslint-disable-next-line
              console.log(error);
            }
          }

          return true;
        }
      }
    }
  }

  async getSources() {
    const sources = retryApolloRequest(3, () => {
      return this.ctx.apolloClient.getConfig({
        appId: 'ebiz-fe-platform',
        namespace: 'skynet-log',
        key: 'sources',
      });
    });
    return sources || [];
  }

  async getArranges() {
    const sources = retryApolloRequest(3, async () => {
      return this.ctx.apolloClient.getConfig({
        appId: 'ebiz-fe-platform',
        namespace: 'skynet-log',
        key: 'owners-map',
      });
    });

    return sources || {};
  }

  // 获取 apollo 配置本质上不需要 异步
  async getWechatRobots() {
    const sources = retryApolloRequest(3, async () => {
      return this.ctx.apolloClient.getConfig({
        appId: 'ebiz-fe-platform',
        namespace: 'skynet-log',
        key: 'wechat-robots',
      });
    });

    return sources || {};
  }

  getJiraTypes() {
    const sources = retryApolloRequest(3, () => {
      return this.ctx.apolloClient.getConfig({
        appId: 'ebiz-fe-platform',
        namespace: 'skynet-log',
        key: 'trace-configs',
      });
    });

    return sources || {};
  }

  // 获取 source
  async getSource() {
    const { app: appName, item } = this.alertMsgJson as IAlertMsgJson;
    const sources = await this.getSources();

    const source = getSource(appName, item, sources);
    return source;
  }

  // 获取 机器人
  async getWechatRobot(source: Isource): Promise<string> {
    const weChatRobots = (await this.getWechatRobots()) || {};
    return IS_DEVELOPMENT_MODE ? 'dadaTest' : weChatRobots[source.app] || 'skynetAlert';
  }

  // 开始通知
  async startNotify(source: Isource, weChatRobot: string) {
    const time = { startStr: this.startStr, endSrt: this.endSrt };
    const startNotifyMsg = getStartNotifyMsg(source, this.alertMsgJson, '', time);
    await this.service.webhookMessageSend(getWechatFormatMsg(startNotifyMsg), weChatRobot);
  }

  // 检索日志
  async recordLogs(source: Isource): Promise<Record<string, any>> {
    return await startRecordNodeLog(getSkynetlogParams(this.startStr, this.endSrt, source));
  }

  // 带 owners 信息的日志
  async getResWithOwners(source: Isource, logResult: any) {
    const arranges = await this.getArranges();
    const resWithOwners = makeArrange(source, logResult, arranges);
    // eslint-disable-next-line
    console.log('resWithOwners', resWithOwners);
    return resWithOwners;
  }

  // 往微信发送错误日志
  async notifyMsg(source: Isource, resWithOwners: any, weChatRobot: string) {
    const time = { startStr: this.startStr, endSrt: this.endSrt };
    const notifyMsgs = getNotifyMsg(source, this.alertMsgJson, resWithOwners, time);
    for (const msg of notifyMsgs) {
      const notifyMsg = getWechatFormatMsg(msg);
      await this.service.webhookMessageSend(notifyMsg, weChatRobot);
    }
  }

  // 创建 jira
  async makeJira(source: Isource, resWithOwners: any, weChatRobot: string) {
    const jiraType = (this.getJiraTypes() || {})[source.app] || {};
    const enable = jiraType.enable;

    if (enable) {
      const jiraList = getJiraList(source, resWithOwners);

      if (jiraList.length) {
        const jiraInfoList = await this.jiraService.batchCreateIssue(jiraList);
        const jiraWechatMsg = getWechatFormatMsg(getJiraNotify(jiraList, jiraInfoList));
        await this.service.webhookMessageSend(jiraWechatMsg, weChatRobot);
      }
    }
  }

  // 创建效率
  async makeXiaolv(source: Isource, resWithOwners: any, weChatRobot: string) {
    const jiraType = (this.getJiraTypes() || {})[source.app] || 'EBIZSENTRY';
    const jiraList = getXiaolvList(source, resWithOwners, jiraType);

    if (jiraList.length && !IS_DEVELOPMENT_MODE) {
      const jiraInfoList = await this.xiaolvService.batchCreateDailyDemand(jiraList);
      const jiraWechatMsg = getWechatFormatMsg(getXiaolvNotify(jiraList, jiraInfoList));
      await this.service.webhookMessageSend(jiraWechatMsg, weChatRobot);
    }
  }
};
