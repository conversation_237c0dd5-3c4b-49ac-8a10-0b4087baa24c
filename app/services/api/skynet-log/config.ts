/* eslint-disable */
export default {
  // kafka_topic: 'edu-data-statistics-class',
  // kafka_topic: 'edufe.skynet.errorlog',
  kafka_topic: 'skynet.metrics.alert',
  // kafka_server: 'hdp-kafka-qa0.s.qima-inc.com:9092,hdp-kafka-qa1.s.qima-inc.com:9092,hdp-kafka-qa2.s.qima-inc.com:9092', // qa-bigdata
  // kafka_server: 'qabb-qa-kafka000:9092', // qa
  kafka_server: 'hdp-kafka-inner-g50-bd0.s.qima-inc.com:9092,hdp-kafka-inner-g50-bd1.s.qima-inc.com:9092,hdp-kafka-inner-g50-bd2.s.qima-inc.com:9092', // prod
};
/* eslint-enable */
