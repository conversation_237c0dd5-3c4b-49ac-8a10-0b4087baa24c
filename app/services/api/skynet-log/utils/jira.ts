import { Isource, ILog } from '../typing';
import { ICreateIssueData } from 'definitions/jira/issue';
import { IS_DEVELOPMENT_MODE, DEVELOPMENT_OWNER } from '../constant';

interface IJiraList {
  (source: Isource, resWithOwners: any): ICreateIssueData[];
}

interface IOwnerJira {
  owner: string;
  jira: string;
}

const getOwnerName = (email: string) => {
  return (email || '').replace('@youzan.com', '');
};

const getSummary = (appName: Isource['app'], log: ILog): string => {
  return `${appName} => ${log.name}`;
};

const getDescription = (log: ILog): string => {
  return `${log.name} \n 错误次数：${log.cnt} \n 来源页面：${log.referer} \n 错误原因：${log.errorReason} \n ${log.checkTip}`;
};

export const getJiraList: IJiraList = (source, resWithOwners) => {
  return resWithOwners.content
    .filter((o: ILog) => {
      return !o.isTimeout;
    })
    .map((o: ILog) => {
      return {
        issuetypeId: '10600',
        projectKey: 'EBIZSENTRY',
        reporter: 'qianyongjun',
        priorityId: '3',
        assignee: IS_DEVELOPMENT_MODE ? getOwnerName(DEVELOPMENT_OWNER) : getOwnerName(o.owners[0]),
        summary: getSummary(source.app, o),
        description: getDescription(o),
      };
    })
    .filter((o: ICreateIssueData) => {
      return o.assignee;
    });
};

export const getJiraNotify = (jiraList: ICreateIssueData[], jiraKeyList: any[]) => {
  const result: IOwnerJira[] = [];
  jiraList.forEach((o, i) => {
    result.push({
      owner: `${o.assignee}@youzan.com`,
      jira: jiraKeyList[i].key,
    });
  });
  return result.reduce((acc, curr) => {
    const jiraLink = `https://jira.qima-inc.com/browse/${curr.jira}`;
    return `${acc} \n<@${curr.owner}> => [${jiraLink}](${jiraLink})`;
  }, '');
};
