import { Isource, ISkynetlogParams } from '../typing';
// import sources from '../config/source';

// eslint-disable-next-line
export const getSource: (app: string, alertName: string, sources: Isource[], level?: string) => Isource | undefined = (app, alertName, sources) => {
  return sources.filter(o => o.app === app && o.alertName === alertName)[0];
};

// eslint-disable-next-line
export const getSkynetlogParams: (startTime: string, endTime: string, source: Isource) => ISkynetlogParams = (startTime, endTime, source) => {
  return {
    start: startTime,
    end: endTime,
    cache: false,
    skynetProject: source.app,
    logIndexName: source.logIndex,
    content: source.condition || '',
    level: source.logLevel || 'error',
  };
};

// export const waitForResult = async (config, fn: any) => {
//   let countIndex = 0;
//   for(;;) {
//     countIndex++;

//     if (countIndex > 20) {
//       throw new Error('遍历满');
//     }

//     await fn();
//   }
// }

export const retryApolloRequest = (maxCount: number, fn: any) => {
  let count = 0;
  for (;;) {
    count++;

    if (count > maxCount) {
      return undefined;
    }

    try {
      const result = fn();

      if (!result) {
        continue;
      } else {
        return result;
      }
    } catch (error) {
      continue;
    }
  }
};

export const sleep = (ms: number) =>
  new Promise((resolve, reject) => {
    setTimeout(() => {
      resolve();
    }, ms);
  });

export function getStrBytes(str: string) {
  let length = 0;
  const reg = /[\u4e00-\u9fa5]/;
  for (let i = 0; i < str.length; i++) {
    if (reg.test(str.charAt(i))) {
      length += 2;
    } else {
      length++;
    }
  }
  return length;
}
