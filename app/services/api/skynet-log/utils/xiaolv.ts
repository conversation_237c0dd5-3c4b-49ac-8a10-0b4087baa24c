import { Isource, ILog } from '../typing';

interface IJiraList {
  (source: Isource, resWithOwners: any, xiaolvType: string): any[];
}

interface IOwnerJira {
  owner: string;
  jira: string;
}

const getOwnerName = (email: string) => {
  return (email || '').replace('@youzan.com', '');
};

const getSummary = (appName: Isource['app'], log: ILog): string => {
  return `${appName} => ${log.name}`;
};

const getDescription = (log: ILog): string => {
  return `${log.name} \n 错误次数：${log.cnt} \n 来源页面：${log.referer} \n 错误原因：${log.errorReason} \n ${log.checkTip}`;
};

export const getXiaolvList: IJiraList = (source, resWithOwners, xiaolvType) => {
  return resWithOwners.content
    .filter((o: ILog) => {
      return !o.isTimeout;
    })
    .map((o: ILog) => {
      return {
        name: getSummary(source.app, o),
        description: getDescription(o),
        category: 1,
        ownerOaId: 1882,
        creatorOaId: 887,
        business: 78,
        otherBusiness: [78],
        tagIds: [215],
      };
    })
    .filter((o: any) => {
      return o.assignee;
    });
};

export const getXiaolvNotify = (jiraList: any[], jiraKeyList: any[]) => {
  const result: IOwnerJira[] = [];
  jiraList.forEach((o, i) => {
    result.push({
      owner: `${o.assignee}@youzan.com`,
      jira: jiraKeyList[i].key,
    });
  });
  return result.reduce((acc, curr) => {
    const jiraLink = `https://jira.qima-inc.com/browse/${curr.jira}`;
    return `${acc} \n<@${curr.owner}> => [${jiraLink}](${jiraLink})`;
  }, '');
};
