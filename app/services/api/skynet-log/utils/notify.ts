/* eslint-disable max-len */
import zanAjax from 'zan-ajax';
import { uniq, get, flatten } from 'lodash';
import { Isource } from '../typing';
import qs from 'querystring';
import { getStrBytes } from './index';

const SENR_WECHAT = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5ae5b47c-468c-4e62-a835-18f358c06e6b';

const WeChatMarkdownMaxBytes = 3800; // 4096
interface ITrackUrlParams {
  uuid: string;
  mobile: string;
  yzUid: string;
  kdtId: string;
  start: string;
  end: string;
}

function getTrackUrl(params: ITrackUrlParams) {
  const nextParams = Object.entries(params).reduce((total, [key, value]) => {
    if (typeof value === 'undefined') {
      return total;
    }
    return Object.assign(total, { [key]: value });
  }, {});
  const url = `https://fe-ops.qima-inc.com/tool/tracetrack?` + qs.stringify(nextParams);
  return url;
}

interface INotifyTime {
  startStr: string;
  endSrt: string;
}
interface IMakeNotify {
  (source: Isource, alertMsg: any, res: any, time: INotifyTime): void;
}

interface InotifyMsg<T = string> {
  (source: Isource, alertMsg: any, res: any, time: INotifyTime): T;
}

interface IExceptionInfo {
  yzLogUuid: string;
  mobile: string;
  kdtId: string;
  yzUid: string;
  timestamp: string;
  referer: string;
  errorReason: string;
  checkTip: string;
  isTimeout: boolean;
  errorType: string;
  name: string;
  cnt: number;
  owners: string[];
}

function formatExceptionContent(exceptionInfo: IExceptionInfo, time: any) {
  // eslint-disable-next-line max-len
  const { yzLogUuid: uuid, mobile, kdtId, yzUid, timestamp, referer, errorReason, checkTip, isTimeout, errorType, name, cnt, owners = [] } = exceptionInfo;

  // eslint-disable-next-line max-len
  const timeoutTemp = `<font color=${isTimeout ? '"comment"' : '"warning"'}>${errorType}</font> ${name} 次数<font color="warning">${cnt}</font>`;

  // 1.来源页面
  // eslint-disable-next-line
  const comeFrom = referer && `<font color=\"warning\">来源页面：</font>${referer}`;

  // 2.错误原因
  // eslint-disable-next-line
  const errorReasonTemp = errorReason && `<font color=\"warning\">错误原因：</font>${errorReason.substr(0, 242)}`;

  const yzUidTemp = yzUid && `<font color="warning">yzUid：</font> ${yzUid}`;

  // yzLogUuid
  const uuidTemp = uuid && `<font color="warning">uuid：</font> ${uuid}`;
  // mobile
  const mobileTemp = mobile && `<font color="warning">mobile：</font> ${mobile}`;
  // kdtId
  const kdtIdTemp = kdtId && `<font color="warning">kdtId：</font> ${kdtId}`;

  const timestampTemp = timestamp && `<font color="warning">timestamp：</font> ${timestamp}`;
  // trackUrl
  const params = { uuid: uuid, mobile, yzUid, start: time.startStr, end: time.endSrt, kdtId };
  const trackUrl = `<font color="warning">用户行为追溯：</font> [点击跳转⬇️](${getTrackUrl(params)})`;

  const ownersTemp = owners.reduce((ownerAcc: any, ownerO: any) => {
    return `${ownerAcc} <@${ownerO}>`;
  }, '<font color="warning">负责人：</font>');
  const gapTemp = ` `;
  // eslint-disable-next-line max-len
  const infos =
    [gapTemp, timeoutTemp, comeFrom, errorReasonTemp, uuidTemp, yzUidTemp, mobileTemp, kdtIdTemp, timestampTemp, trackUrl, checkTip, ownersTemp]
      .filter(d => !!d)
      .map(d => '>' + d)
      .join('\n') + '\n';
  return infos;
}

// 动态分配好需要发送的消息数量；max = 4096；
function exceptionBodySlice(header: string, body: string[], footer: string) {
  const maxBytes = WeChatMarkdownMaxBytes;
  const totalItems = [header, ...body, footer];
  const result = [];
  let preBytes = 0;
  let preUnit = '';
  const len = totalItems.length;
  for (let index = 0; index < len; index++) {
    const item = totalItems[index];
    const itemBytes = getStrBytes(item);
    if (preBytes + itemBytes > maxBytes) {
      result.push(preUnit);
      preUnit = '';
      preBytes = 0;
    }
    preUnit += item;
    preBytes += itemBytes;

    if (index === len - 1) {
      result.push(preUnit);
    }
  }
  return result;
}

// eslint-disable-next-line
export const getNotifyMsg: InotifyMsg<string[]> = (source, alertMsg, res, time) => {
  const appName = source.app;

  const getOwnerListMsg = () => {
    const ownersList = res.content.map((o: any) => uniq(o.owners));

    const uniqList = uniq(flatten(ownersList));

    const uniqListStr = uniqList.reduce((ownerAcc: any, ownerO: any) => {
      return `${ownerAcc} <@${ownerO}>`;
    }, '');

    return `> 请以下负责人重点关注线上问题 => ${uniqListStr}`;
  };

  const exceptionItems = get(res, 'content', []).map((item: any) => formatExceptionContent(item, time));

  let header = `## ${appName} 时间段：${time.startStr} - ${time.endSrt} 发生报警 \n`;

  header += `> ### 报警来源：\n > ${alertMsg.msg} \n \n 请前往 https://skynet.qima-inc.com/zan-alert.html#/detail 点击我在处理！\n \n`;

  header += `### 错误分析：\n `;
  const footer = `### owners 跟进：\n ${getOwnerListMsg()}`;
  const contents = exceptionBodySlice(header, exceptionItems, footer);
  return contents;
};

export const getStartNotifyMsg: InotifyMsg = (source, alertMsg, res, time) => {
  const appName = source.app;

  let content = `${appName} 时间段：${time.startStr} - ${time.endSrt} 发生报警， 请关注 \n`;

  content += `> ${alertMsg.msg} \n \n`;

  content += `> 开始进行日志检索... 请稍后 \n`;

  return content;
};

const sendMsg = async (content: string) => {
  await zanAjax({
    url: SENR_WECHAT,
    method: 'post',
    data: JSON.stringify({
      msgtype: 'markdown',
      markdown: {
        content,
      },
    }),
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
  });
};

const makeNotify: IMakeNotify = async (source, alertMsg, res, time) => {
  const notifyMsgs = getNotifyMsg(source, alertMsg, res, time);
  for (const msg of notifyMsgs) {
    await sendMsg(msg);
  }
};

export const getWechatFormatMsg: (msg: string, type?: string) => object = (msg, type = 'markdown') => {
  // markdown 内容字节超出限制，简单处理，截断至3000；
  if (type === 'markdown' && getStrBytes(msg) >= WeChatMarkdownMaxBytes) {
    msg = msg.substr(0, 3000);
  }

  return {
    msgtype: 'markdown',
    markdown: {
      content: msg,
    },
  };
};

export default makeNotify;
