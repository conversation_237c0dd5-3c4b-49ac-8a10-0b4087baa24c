import ServiceError from '@youzan/youzan-framework/app/exceptions/ServiceError';
import BaseService from '../../base/BaseService';
import { IAjaxOptions } from 'zan-ajax';
import { groupRobots, corpid } from '../../../constants/wechatwrok';
import { isHttp } from '@utils/index';

export = class extends BaseService {
  /**
   * 群机器人消息
   * @param message 微信消息体
   * @param group constants里的key或直接传入一个url
   */
  async webhookMessageSend(message: object, group: string): Promise<any> {
    let url: string;
    if (isHttp(group)) {
      url = group;
    } else {
      url = `https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${groupRobots[group]}`;
    }

    const ajaxOptions: IAjaxOptions = {
      url,
      method: 'POST',
      data: JSON.stringify(message),
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    };
    const result = await this.proxyCall(ajaxOptions);
    if (result.errcode !== 0) {
      throw new ServiceError(500, result.errmsg);
    }
    return result;
  }

  // 发送应用消息之前先使用这个方法获取token
  async getToken(corpsecret: string) {
    const ajaxOptions: IAjaxOptions = {
      url: `https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${corpid}&corpsecret=${corpsecret}`,
      method: 'GET',
      headers: {
        Accept: 'application/json',
      },
    };
    const result = await this.proxyCall(ajaxOptions);
    if (result.errcode !== 0) {
      throw new ServiceError(500, result.errmsg);
    }
    return result;
  }

  // 应用消息
  async appMessageSend(token: string, message: object) {
    const ajaxOptions: IAjaxOptions = {
      url: `https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=${token}`,
      method: 'POST',
      data: JSON.stringify(message),
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    };
    const result = await this.proxyCall(ajaxOptions);
    if (result.errcode !== 0) {
      throw new ServiceError(500, result.errmsg);
    }
    return result;
  }
};
