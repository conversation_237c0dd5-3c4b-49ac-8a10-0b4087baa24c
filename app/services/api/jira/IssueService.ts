// import _ from 'lodash';
import BaseService from '../../base/BaseService';
import { ICreateIssueData, ICreateIssueDTO } from 'definitions/jira/Issue';

export = class IssueService extends BaseService {
  async createIssue(data: ICreateIssueData) {
    const dto = this.formatCreateIssueDTO(data);
    return await this.jiraCall({
      url: `${this.getConfig('JIRA_URL')}/issue`,
      method: 'POST',
      data: dto,
    });
  }

  async batchCreateIssue(dto: ICreateIssueData[]) {
    const promises = dto.map(item => this.createIssue(item));
    return await Promise.all(promises);
  }

  private formatCreateIssueDTO(data: ICreateIssueData): ICreateIssueDTO {
    const { summary, description, issuetypeId, projectKey, priorityId, reporter, assignee } = data;
    return {
      fields: {
        summary,
        description,
        issuetype: {
          id: issuetypeId,
        },
        project: {
          key: project<PERSON><PERSON>,
        },
        reporter: {
          name: reporter,
        },
        priority: {
          id: priorityId,
        },
        assignee: {
          name: assignee,
        },
      },
    };
  }
};
