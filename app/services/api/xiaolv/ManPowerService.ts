import formatDate from '@youzan/utils/date/formatDate';
import WechatWorkService from '../wechatwork/WechatWorkService';
import BaseService from '../../base/BaseService';

type WeekTimeRange = {
  start: string;
  end: string;
};
type ManPowerItem = {
  name: string;
  email: string;
  totalManPowerPercent: number;
};

export = class ManPowerService extends BaseService {
  public async getHumanInputData() {
    return await this.notifyUnReachedManPowerGoal();
  }

  public async keepAlive() {
    const cookie: string = await this.getCookieFromApollo();
    const result = await this.httpCall({
      url: 'http://xiaolv.s.qima-inc.com/homepage/getMenuBubbleRecord',
      headers: {
        Cookie: cookie,
      },
      dataType: 'json',
    });

    // 当保活失败时，发送手机通知
    if (result.code === 9999) {
      await this.sendErrorMsg('效率平台Cookie保活失败', result);
    }
    return result;
  }

  /**
   * 发送提醒
   */
  public async notifyUnReachedManPowerGoal() {
    const peoples = await this.getManPowerPercentLessThen55();
    if (!peoples.length) return;

    const message = {
      msgtype: 'text',
      text: {
        content: '大家记得在效率平台拖动一下项目及日常进度\n技术改造可以使用迭代/日常/项目的方式来记录。\n----------------\n',
        // eslint-disable-next-line
        mentioned_list: peoples.map(p => p.email),
      },
    };

    const wechatworkService = new WechatWorkService(this.ctx);
    const result = await wechatworkService.webhookMessageSend(message, 'frontjinfeng');

    return result;
  }

  /**
   * 过虑及格式化人力资源数据
   * 透明度55为团队目标
   */
  private async getManPowerPercentLessThen55(): Promise<Array<ManPowerItem>> {
    const result = await this.getCurrentWeekHumanInput();

    // 55有点高，提醒的人有点多，修改为40吧
    const powerTarget = 40;
    const exceptPeoples: Array<string> = ['劲风', '凯文', '钱勇俊'];

    const unReachedPeople: Array<ManPowerItem> = Array.from(result.data.outputStatisticals)
      .filter((people: any) => {
        // 排除掉特定的人
        if (exceptPeoples.includes(people.user.nickname)) return false;

        // status 10表示在职，200表示离职
        return people.totalManPowerPercent < powerTarget && people.user.status === 10;
      })
      .map((people: any) => {
        return {
          name: people.user.nickname,
          email: people.user.email,
          totalManPowerPercent: people.totalManPowerPercent,
        };
      });
    return unReachedPeople;
  }

  /**
   * 从Apollo平台获取效率平台的cookie
   */
  private async getCookieFromApollo(): Promise<string> {
    const cookie: string = await this.ctx.apolloClient.getConfig({
      appId: 'ebiz-fe-platform',
      namespace: 'application',
      key: 'xiaolv_jinfeng_cookie',
    });

    return cookie;
  }

  /**
   * 从效率平台获取原始数据
   */
  private async getCurrentWeekHumanInput() {
    const currWeekRange = this.getCurrentWeekRange();
    const query = {
      type: 'TOTAL_HUMAN_INPUT',
      page: 1,
      pageSize: 50,
      departmentId: 256,
      startTime: currWeekRange.start,
      endTime: currWeekRange.end,
      projectShow: true,
      dailyShow: true,
      iterationShow: true,
    };

    const cookie: string = await this.getCookieFromApollo();

    const result = await this.httpCall({
      url: 'http://xiaolv.s.qima-inc.com/homepage/getOutputList',
      data: query,
      headers: {
        Cookie: cookie,
      },
      dataType: 'json',
    });
    return result;
  }

  /**
   * 取当前的星期，周一到周五
   * @returns Object {start: '2020-03-22', end: '2020-03-27'}
   */
  private getCurrentWeekRange(): WeekTimeRange {
    const weekRange: WeekTimeRange = {
      start: '',
      end: '',
    };

    const now = new Date();

    // set date to monday
    now.setDate(now.getDate() - now.getDay() + 1);
    weekRange.start = formatDate(now, 'YYYY-MM-DD');

    // set date to friday
    now.setDate(now.getDate() + 4);
    weekRange.end = formatDate(now, 'YYYY-MM-DD');

    return weekRange;
  }

  /**
   * 发送手机消息通知
   */
  private async sendErrorMsg(title: string, msg: string) {
    await this.httpCall({ url: `https://api.day.app/i6ESaSpRtE3tVG2rd3d4PH/${encodeURIComponent(title)}/${encodeURIComponent(JSON.stringify(msg))}` });
  }
};
