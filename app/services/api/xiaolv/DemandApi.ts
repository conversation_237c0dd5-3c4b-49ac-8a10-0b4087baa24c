import BaseService from '../../base/BaseService';

/** com.youzan.et.xiaolv.api.DemandApi -  */
export = class Demand<PERSON><PERSON> extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.et.xiaolv.api.DemandApi';
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/794253
   *
   *  @param {Object} createInput -
   *  @param {Array.<Array>} createInput.userValue[] -
   *  @param {number} createInput.business -
   *  @param {number} createInput.creatorOaId -
   *  @param {Array.<Array>} createInput.businessValue[] -
   *  @param {Array.<Array>} createInput.tagIds[] -
   *  @param {string} createInput.name -
   *  @param {number} createInput.creatorId -
   *  @param {string} createInput.description -
   *  @param {Array.<Array>} createInput.otherBusiness[] -
   *  @param {number} createInput.ownerOaId -
   *  @param {number} createInput.category -
   *  @param {number} createInput.ownerId -
   *  @return {Promise}
   */
  async createDailyDemand(createInput): Promise<number> {
    return this.invoke('createDailyDemand', [createInput]);
  }

  // async batchCreateDailyDemand(createInputs: any): Promise<number[]> {
  //   const promiseList = createInputs.map((o: any) => {
  //     return new Promise(resolve => {
  //       setTimeout(async () => {
  //         let result;
  //         try {
  //           result = await this.createDailyDemand(o);
  //         } catch (error) {
  //           result = undefined;
  //         }
  //         resolve(result);
  //       }, 500);
  //     });
  //   });
  //   const result = await Promise.all(promiseList);
  //   return result;
  // }
};
