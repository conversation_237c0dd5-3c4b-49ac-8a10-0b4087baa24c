import _ from 'lodash';
import { FE_TEAM } from '../../../constants/xiaolv';
import BaseService from '../../base/BaseService';
import WechatWorkService from '../wechatwork/WechatWorkService';

import { ITaskResult } from 'definitions/xiaolv/Daily';

export = class DailyService extends BaseService {
  async getTeamDailyDemand(data) {
    return await this.xiaolvCall({
      url: `${this.getConfig('XIAOLV_URL')}/getTeamDailyDemand`,
      data: {
        business: 256,
        pageSize: 20,
        ...data,
      },
    });
  }

  async getAllTeamDailyDemand(data) {
    return await this.recursiveTasks(_data => this.getTeamDailyDemand(_data || data));
  }

  async notifyUnSplitDailyTask() {
    const request = page =>
      this.getTeamDailyDemand({
        status: 500,
        page: page || 1,
      });
    const [mentions, tasks] = this.processUnSplitTaskData(await this.recursiveTasks(request));
    const content = this.getDailyTaskUnSplitContent(tasks);
    const taskNotify = {
      msgtype: 'markdown',
      markdown: {
        content,
      },
    };
    const mentionsNotify = {
      msgtype: 'text',
      text: {
        content: '未拆分任务请处理：',
        mentioned_list: mentions,
      },
    };
    const service = new WechatWorkService(this.ctx);
    await service.webhookMessageSend(taskNotify, 'frontjinfeng');
    await service.webhookMessageSend(mentionsNotify, 'frontjinfeng');
  }

  async notifyUnResolvedDailyTask() {
    const request1 = page =>
      this.getTeamDailyDemand({
        status: 600,
        page: page || 1,
      });
    const request2 = page =>
      this.getTeamDailyDemand({
        status: 800,
        page: page || 1,
      });
    const [data1, data2] = await Promise.all([this.recursiveTasks(request1), this.recursiveTasks(request2)]);
    const [mentions, tasks] = this.processUnResolvedTaskData([...data1, ...data2]);
    const content = this.getDailyTaskUnResolvedContent(tasks);
    const taskNotify = {
      msgtype: 'markdown',
      markdown: {
        content,
      },
    };
    const mentionsNotify = {
      msgtype: 'text',
      text: {
        content: '逾期任务请处理：',
        mentioned_list: mentions,
      },
    };
    const service = new WechatWorkService(this.ctx);
    await service.webhookMessageSend(taskNotify, 'frontjinfeng');
    await service.webhookMessageSend(mentionsNotify, 'frontjinfeng');
  }

  private async recursiveTasks(promiseCreater) {
    const result: any[] = [];
    async function recursion(data?: number) {
      const res: ITaskResult = (await promiseCreater(data)).data;
      result.push(...res.list);
      if (res.list.length === res.pageSize) {
        return recursion(++res.page);
      }
    }
    await recursion();
    return result;
  }

  private processUnSplitTaskData(data: any) {
    return _.chain(data)
      .filter(task => !task.dueDate.dueTime)
      .map(task => {
        const baseInfo = _.pick(task, ['id', 'name']);
        const users = _.get(task, 'partner.developer');
        return _.chain(users)
          .filter(user => FE_TEAM.includes(user.nickname))
          .map(user => {
            return {
              user: user.email,
              nickname: user.nickname,
              ...baseInfo,
            };
          })
          .value();
      })
      .flatten()
      .reduce(
        (result, value) => {
          result.users.add(value.user);
          result.tasks.set(value.id, value);
          return result;
        },
        { users: new Set(), tasks: new Map() }
      )
      .map(item => Array.from(item.values()))
      .value();
  }

  private processUnResolvedTaskData(data: any) {
    return _.chain(data)
      .filter(task => task.dueDate.tagType === 'danger')
      .map(task => {
        const baseInfo = _.pick(task, ['id', 'name']);
        const users = _.get(task, 'partner.developer');
        return _.chain(users)
          .filter(user => FE_TEAM.includes(user.nickname))
          .map(user => {
            return {
              user: user.email,
              nickname: user.nickname,
              ...baseInfo,
            };
          })
          .value();
      })
      .flatten()
      .reduce(
        (result, value) => {
          result.users.add(value.user);
          result.tasks.set(value.id, value);
          return result;
        },
        { users: new Set(), tasks: new Map() }
      )
      .map(item => Array.from(item.values()))
      .value();
  }

  getDailyTaskUnResolvedContent(data) {
    let content = '日常任务已逾期：\n';
    _.chain(data)
      .groupBy('nickname')
      .forEach((value, key) => {
        const taskMsg = _.reduce(
          value,
          (msg, item, index) =>
            `${msg}[问题${index + 1}](http://xiaolv.qima-inc.com/#/demand/search?show=true&ids=${item.id})\n`,
          ''
        );
        content += `${key}:\n${taskMsg}`;
      })
      .value();
    return content;
  }

  getDailyTaskUnSplitContent(data) {
    let content = '日常任务未拆分：\n';
    _.chain(data)
      .groupBy('nickname')
      .forEach((value, key) => {
        const taskMsg = _.reduce(
          value,
          (msg, item) => `${msg}[${item.name}](http://xiaolv.qima-inc.com/#/demand/search?show=true&ids=${item.id})\n`,
          ''
        );
        content += `${key}:\n${taskMsg}`;
      })
      .value();
    return content;
  }
};
