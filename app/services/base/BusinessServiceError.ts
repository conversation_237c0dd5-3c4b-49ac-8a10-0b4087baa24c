export = class BusinessServiceError extends Error {
  code: number;
  msg: string;
  errorContent: {
    code: number;
    msg: string;
    extra: object;
  };
  errorType: string;

  constructor(code: number, msg: string, extra = {}) {
    super(`code: ${code} message: ${msg}`);
    this.code = code;
    this.msg = msg;
    this.errorContent = {
      code,
      msg,
      extra,
    };
    this.errorType = 'BusinessServiceError';
  }
};
