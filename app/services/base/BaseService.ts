import assert from 'assert';
import { YouzanBaseService } from '@youzan/youzan-framework';
import BusinessServiceError from './BusinessServiceError';
import { auth as jiraAuth } from '../../constants/jira';
import { auth as xiaolvAuth } from '../../constants/xiaolv';

import { IAjaxOptions } from 'zan-ajax';

const basicAuth = new Buffer(`${jiraAuth.username}:${jiraAuth.password}`).toString('base64');

export = class BaseService extends YouzanBaseService {
  /**
   * 调用 Dubbo 服务
   * @param  {String} serviceName 服务名
   * @param  {String} methodName  方法名
   * @param  {Object} args        请求参数
   * @param  {Object} options     额外参数
   * @param {String} options.key
   * @param {Number} options.timeout 设置ajax超时
   * @param {Boolean} options.allowBigNumberInJSON
   * @param {Number} options.cache 设置缓存时间，单位秒
   * @param  {Object} options.headers 额外参数
   * @param  {Number} options.headers['X-Timeout'] 设置tether超时
   */
  async invoke(serviceName: string, methodName: string, args: any, options = {}) {
    // 如果第二个参数是一个数组，说明业务调用服务的时候省略了服务名，则默认使用 Service 的 SERVICE_NAME
    if (Array.isArray(methodName)) {
      options = args || {};
      args = methodName;
      methodName = serviceName;
      serviceName = this.SERVICE_NAME;
    }
    assert(serviceName, 'serviceName cannot be empty!');
    assert(methodName, 'methodName cannot be empty!');

    const result = await this.dubboCall(serviceName, methodName, args, options);

    // 数据格式1，参考 data1.js 文件
    if (result.code && typeof result.message === 'string' && typeof result.success === 'boolean') {
      if (result.code === 200) {
        return result.data;
      } else {
        throw new BusinessServiceError(result.code, result.message, {
          serviceName,
          methodName,
          args,
        });
      }
    }

    return result;
  }

  async jiraCall(ajaxOptions: IAjaxOptions) {
    const { headers = {}, ...restAjaxOptions } = ajaxOptions;
    const result = await this.httpCall(
      {
        ...restAjaxOptions,
        headers: {
          ...headers,
          'Content-Type': 'application/json',
          Accept: 'application/json',
          Authorization: `Basic ${basicAuth}`,
        },
      },
      {
        plainResult: true,
      }
    );
    if (result && result.data) {
      return result.data;
    }
  }

  async xiaolvCall(ajaxOptions: IAjaxOptions) {
    const { headers = {}, ...restAjaxOptions } = ajaxOptions;
    return await this.httpCall({
      ...restAjaxOptions,
      headers: {
        ...headers,
        ...xiaolvAuth,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    });
  }
};
