import BaseService from '../base/BaseService';
/**
 * los es接口集合
 * @class
 */
export = class ReadmeSearchService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.owl.console.api.search.SearchFacade';
  }

  /**
   *  es数据接口查询
   *  @param {Object} requestDTO -
   *  @return {Object}
   */
  async search(requestDTO: any) {
    const result = await this.invoke(this.SERVICE_NAME, 'search', [ requestDTO ], { headers: {
      'X-Service-Chain': '{"name":"prj006821"}' }
    });
    return result;
  }
};
