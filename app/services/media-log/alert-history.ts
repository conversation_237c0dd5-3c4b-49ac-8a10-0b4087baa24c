import BaseService from '../base/BaseService';
import AlertHistoryModel from '@models/media-log/alertHistory';
import AlertRuleModel from '@models/media-log/alertRule';
import { sortBy } from 'lodash';
import format from 'date-fns/format';
import { getManager, getConnection } from 'typeorm';
import { PageInfo } from '@definitions/base';

export = class AlertHistoryService extends BaseService {
  public async getAll(query: {
    page: PageInfo;
    appKey?: string;
    mediaType: string;
  }) {
    const whereFilter: { app_key?: string; media_type?: string } = query.appKey ? { app_key: query.appKey } : {};
    whereFilter.media_type = query.mediaType;

    const dataset = await getManager().find(AlertHistoryModel, {
      order: {
        id: 'DESC',
      },
      skip: query.page.page - 1,
      take: query.page.pageSize,
      where: whereFilter,
    });
    const total = await getManager().count(AlertHistoryModel, {
      where: whereFilter,
    });
    return {
      dataset,
      pageInfo: {
        page: query.page.page,
        pageSize: query.page.pageSize,
        total,
      },
    };
  }

  public async getDetail(id: number) {
    const dataset = await getManager().findOne(AlertRuleModel, {
      where: { id },
    });
    return dataset;
  }

  public async create(alertRule: AlertHistoryModel): Promise<number> {
    const result = await getConnection()
      .createQueryBuilder()
      .insert()
      .into(AlertHistoryModel)
      .values(alertRule)
      .execute();
    return result.raw.insertId;
  }

  public async update(alertRule: AlertRuleModel) {
    const id = alertRule.id;
    delete alertRule.id;

    const result = await getConnection()
      .createQueryBuilder()
      .update(AlertRuleModel)
      .set(alertRule)
      .where('id = :id', { id })
      .execute();
    return id;
  }

  public async delete(id: number) {
    const result = await getConnection()
      .createQueryBuilder()
      .delete()
      .from(AlertRuleModel)
      .where('id = :id', { id })
      .execute();

    return id;
  }
};
