import BaseService from '@services/base/BaseService';
import { getConnection } from 'typeorm';
import format from 'date-fns/format';
import parse from 'date-fns/parse';
import { sortBy } from 'lodash';
import { IDataInsightSqlRes, ITimelineRes, ITimelineSqlRes } from '@definitions/media-log/base';
import { DEMENSION_LOGIN, DEMENSION_QUALITY, DEMENSION_TEXT_MAP, DEMENSION_WATCH, TARGET } from '../../constants/media-log';
import InsightDetail = require('@models/media-log/insightDetail');

interface IErrorTimelineProps {
  osType: string;
  appKey: string;
  startTime: number;
  endTime: number;
  mediaType: string;
  target: string;
}

export = class DataInsightService extends BaseService {
  public async getTimeline(props: IErrorTimelineProps) {
    const osType = props.osType;
    const appKey = props.appKey;
    const startTime = props.startTime;
    const endTime = props.endTime;
    const mediaType = props.mediaType;
    const target = props.target;
    const dimensionList: Record<string, string[]> = {
      [TARGET.LOGIN]: Object.values(DEMENSION_LOGIN),
      [TARGET.QUALITY]: Object.values(DEMENSION_QUALITY),
      [TARGET.WATCH]: Object.values(DEMENSION_WATCH),
    };
    const querys = dimensionList[target].map(async query => {
      return getConnection()
        .createQueryBuilder()
        .select()
        .from(InsightDetail, 'detail')
        .where('detail.target = :target', { target: target })
        .andWhere('detail.dimension = :dimension', { dimension: query })
        .andWhere('detail.os = :os', { os: osType })
        .andWhere('detail.media_type = :mediaType', { mediaType: mediaType })
        .andWhere('detail.app_key = :appKey', { appKey: appKey })
        .andWhere('detail.cur_time between str_to_date(:start, "%Y-%m-%d %H") and str_to_date(:end, "%Y-%m-%d %H")',
          { start: format(+startTime, 'YYYY-MM-DD HH'), end: format(+endTime, 'YYYY-MM-DD HH') })
        .getRawMany<IDataInsightSqlRes>();
    });

    try {
      const queryResults = await Promise.all(querys);
      const deMapped = queryResults.reduce<ITimelineRes[]>((prev, curr, index) => {
        const prevList = prev;
        curr.forEach(currentItem => {
          const originalMatrix = Number((Number(currentItem.matrix_target) / Number(currentItem.matrix_all)).toFixed(6));
          prevList.push({
            ...currentItem,
            cur_time: format(parse(currentItem.cur_time), 'YYYY-MM-DD HH:00:00'),
            matrix: originalMatrix,
            dimension: DEMENSION_TEXT_MAP[target][dimensionList[target][index]],
            dimension_origin: dimensionList[target][index],
          });
        });
        return prevList;
      }, []);

      this.ctx.localLog('log', {
        queryResults,
        msg: 'queryResults',
      });

      return sortBy(deMapped, [(o) => {
        return +new Date(o.cur_time);
      }]);
    } catch (error) {
      this.ctx.localLog('error', error);
      throw error;
    }
  }

  public async getQualityListTimeline(props: IErrorTimelineProps) {
    const osType = props.osType;
    const appKey = props.appKey;
    const startTime = props.startTime;
    const endTime = props.endTime;
    const mediaType = props.mediaType;
    const target = props.target;
    const queryResults = await
       getConnection()
        .createQueryBuilder()
        .select()
        .from(InsightDetail, 'detail')
        .where('detail.target = :target', { target: target })
        .andWhere('detail.dimension = :dimension', { dimension: "error" })
        .andWhere('detail.os = :os', { os: osType })
        .andWhere('detail.media_type = :mediaType', { mediaType: mediaType })
        .andWhere('detail.app_key = :appKey', { appKey: appKey })
        .andWhere('detail.cur_time between str_to_date(:start, "%Y-%m-%d %H") and str_to_date(:end, "%Y-%m-%d %H")',
          { start: format(+startTime, 'YYYY-MM-DD HH'), end: format(+endTime, 'YYYY-MM-DD HH') })
        .getRawMany<ITimelineSqlRes>();
    ;

    this.ctx.localLog('log', {
      queryResults,
      msg: 'queryResults',
    });
    return queryResults;
  }
};
