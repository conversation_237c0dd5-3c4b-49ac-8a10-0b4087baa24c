import BaseService from '../base/BaseService';
import ClassDetailModel from '@models/media-log/classDetail';
import { sortBy } from 'lodash';
import format from 'date-fns/format';
import { getManager, getConnection } from 'typeorm';
import { PageInfo } from '@definitions/base';
import { DEMENSION_TEXT_MAP, TARGET, DEMENSION_ERROR, DEMENSION_LOGIN_TIME } from '@constants/media-log';
import { ITimelineRes, ITimelineSqlRes } from '@definitions/media-log/base';

interface IErrorTimelineProps {
  osType: string;
  appKey: string;
  startTime: number;
  endTime: number;
  sdkVersion: string;
  target: string;
}

export = class ClassRealtimeService extends BaseService {
  public async getTimeline(props: IErrorTimelineProps) {
    const osType = props.osType;
    const appKey = props.appKey;
    const startTime = props.startTime;
    const endTime = props.endTime;
    const target = props.target;
    // const sdkVersion = props.sdkVersion; // 版本选择先屏蔽

    const dimensionList: Record<string, string[]> = {
      [TARGET.ERROR]: Object.values(DEMENSION_ERROR),
      [TARGET.LOGIN_TIME]: Object.values(DEMENSION_LOGIN_TIME),
    };

    const querys = dimensionList[target].map(async (query, index) => {
      const subQery = await getConnection()
        .createQueryBuilder()
        .select()
        .from(ClassDetailModel, 'detail')
        .where('detail.target = :target', { target: target })
        .andWhere('detail.dimension = :dimension', { dimension: query })
        .andWhere('detail.os = :os', { os: osType })
        .andWhere('detail.app_key = :appKey', { appKey: appKey })
        .andWhere('detail.cur_time between str_to_date(:start, "%Y-%m-%d %H:%i") and str_to_date(:end, "%Y-%m-%d %H:%i")',
        { start: format(+startTime, 'YYYY-MM-DD HH:mm'), end: format(+endTime, 'YYYY-MM-DD HH:mm') });

      return getConnection()
        .createQueryBuilder()
        .select('cur_time')
        .addSelect('sum(matrix)', 'matrix')
        .from('(' + subQery.getQuery() + ')', 'detail')
        .setParameters(subQery.getParameters())
        .groupBy('cur_time')
        .getRawMany<ITimelineSqlRes>();
    });

    try {
      const queryResults = await Promise.all(querys);
      const deMapped = queryResults.reduce<ITimelineRes[]>((prev, curr, index) => {
        const prevList = prev;
        curr.forEach(currItme => {
          prevList.push({
            ...currItme,
            matrix: Number(currItme.matrix),
            dimension: DEMENSION_TEXT_MAP[target][dimensionList[target][index]],
            dimension_origin: dimensionList[target][index],
          });
        });
        return prevList;
      }, []);

      this.ctx.localLog('log', {
        queryResults,
        msg: 'queryResults',
      });

      return sortBy(deMapped, [(o) => { return +new Date(o.cur_time) }]);
    } catch (error) {
      this.ctx.localLog('error', error);
      throw error;
    }
  }
};
