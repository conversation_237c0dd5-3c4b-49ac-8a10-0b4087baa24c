import BaseService from '../base/BaseService';
import AlertRuleModel from '@models/media-log/alertRule';
import { sortBy } from 'lodash';
import format from 'date-fns/format';
import { getManager, getConnection } from 'typeorm';
import { PageInfo } from '@definitions/base';

interface IErrorTimelineProps {
  osType: string;
  appKey: string;
  startTime: number;
  endTime: number;
  sdkVersion: string;
}

export = class AlertRuleService extends BaseService {
  public async getAll(query: {
    page: PageInfo;
    appKey?: string;
  }) {
    const whereFilter = query.appKey ? { app_key: query.appKey } : {};

    const dataset = await getManager().find(AlertRuleModel, {
      order: {
        id: 'DESC',
      },
      skip: query.page.page - 1,
      take: query.page.pageSize,
      where: whereFilter,
    });
    const total = await getManager().count(AlertRuleModel);
    return {
      dataset,
      pageInfo: {
        page: query.page.page,
        pageSize: query.page.pageSize,
        total,
      },
    };
  }

  public async getDetail(id: number) {
    const dataset = await getManager().findOne(AlertRuleModel, {
      where: { id },
    });
    return dataset;
  }

  public async create(alertRule: AlertRuleModel) {
    const result = await getConnection()
      .createQueryBuilder()
      .insert()
      .into(AlertRuleModel)
      .values(alertRule)
      .execute();
    return result.raw.insertId;
  }

  public async update(alertRule: AlertRuleModel) {
    const id = alertRule.id;
    delete alertRule.id;

    const result = await getConnection()
      .createQueryBuilder()
      .update(AlertRuleModel)
      .set(alertRule)
      .where('id = :id', { id })
      .execute();
    return id;
  }

  public async delete(id: number) {
    const result = await getConnection()
      .createQueryBuilder()
      .delete()
      .from(AlertRuleModel)
      .where('id = :id', { id })
      .execute();

    return id;
  }
};
