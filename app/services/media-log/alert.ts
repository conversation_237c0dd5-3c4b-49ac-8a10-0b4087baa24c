/* eslint-disable */
import BaseService from '../base/BaseService';
import AudioModel from '@models/media-log/audio';
import VideoModel from '@models/media-log/video';
import ClassModel from '@models/media-log/class';
import AudioDetailModel from '@models/media-log/audioDetail';
import VideoDetailModel from '@models/media-log/videoDetail';
import ClassDetailModel from '@models/media-log/classDetail';
import { groupBy, sortBy } from 'lodash';
import format from 'date-fns/format';
import { getManager, getConnection } from 'typeorm';
import { PageInfo } from '@definitions/base';
import { IRule } from '@controllers/media-log/type';
import { ISqlRuleResult, ISqlSubruleResult, Operator } from '@definitions/media-log/base';
import { OPERATOR, RULE_NAMECOUNT_SQL_SELECT, TARGET } from '@constants/media-log';

interface IResult {
  rule: Record<string, string | number>[];
  subRule: Record<string, string | number>[][];
}[];

export default class AlertService extends BaseService {
  public async findVideo(props: IRule) {
    this.ctx.localLog('log', {
      msg: 'findVideo',
      data: {},
    });

    const ruleResultPromises = props.rules.map(async rule => {
      const startTime = format(Date.now() - rule.matchRule[0] * 60 * 1000, 'YYYY-MM-DD HH:mm:ss');
      // const startTime = format(Date.now() - rule.matchRule[0] * 60 * 1000 * 30 * 24 * 20, 'YYYY-MM-DD HH:mm:ss'); //  TODO: 需要改回来
      const endTime = format(Date.now(), 'YYYY-MM-DD HH:mm:ss');

      const startTimeMinute = format(Date.now() - rule.matchRule[0] * 60 * 1000, 'YYYY-MM-DD HH:mm:ss');
      // const startTimeMinute = format(Date.now() - rule.matchRule[0] * 60 * 1000 * 30 * 24 * 20, 'YYYY-MM-DD HH:mm'); //  TODO: 需要改回来
      const endTimeMinute = format(Date.now(), 'YYYY-MM-DD HH:mm');

      const selectAndGroupbyArray = rule.granularity.split('&').map(o => `${o}`);

      let sql = getConnection()
        .createQueryBuilder()
        .select('count(*)', 'nameCount');

      // 如果不是 all 粒度，则添加 nameCount 列，用于 having 筛选规则
      if (rule.granularity !== 'all') {
        sql = sql.addSelect(selectAndGroupbyArray);
      }

      sql.from(VideoModel, 'detail')
        .where('detail.timestamps_str between str_to_date(:start, "%Y-%m-%d %H:%i:%s") and str_to_date(:end, "%Y-%m-%d %H:%i:%s")', { start: startTime, end: endTime })
        .andWhere('detail.app_key = :appKey', { appKey: props.appKey });

        // 具体对某指标的筛选过滤条件
        // 可枚举递增
      if (rule.target === 'error') {
        // 错误指标
        // sql = sql.andWhere('detail.action_type = :actionType', { actionType: 'playback' }); //  TODO: 需要改回来
        sql = sql.andWhere('detail.action_type = :actionType', { actionType: 'error' });
      }

      if (rule.granularity !== 'all') {
        selectAndGroupbyArray.map(o => {
          sql = sql.addGroupBy(o);
        });
        sql = sql.having('nameCount >= :count', { count: rule.matchRule[1] });
      }

      // 限制 50 条，防止命中规则的数量太多导致查询缓慢
      sql = sql.limit(50);

      // const tags = sql.getSql();

      let ruleResults = await sql.getRawMany<ISqlRuleResult>();

      // 如果粒度是 all 的情况下，在 sql 有结果后才能对其 nameCount 进行比较
      if (rule.granularity === 'all') {
        if (rule.matchRule[2] === Operator.gte) {
          if (Number(ruleResults[0].nameCount) < rule.matchRule[1]) {
            ruleResults = [];
          }
        } else if (rule.matchRule[2] === Operator.lte) {
          if (Number(ruleResults[0].nameCount) > rule.matchRule[1]) {
            ruleResults = [];
          }
        }
      }

      let subRuleResult: ISqlSubruleResult[][] = [];

      // 判断是否有 subMatch 规则，有的话需要且逻辑计算（只有 granularity === all 是才会有 subMatch
      if (rule.granularity === 'all' && rule.subMatchRule.length && ruleResults.length) {
        const subRuleResultPromises = rule.subMatchRule.map(async subRule => {
          return await getConnection()
            .createQueryBuilder()
            .select(['dimension', 'target'])
            .addSelect('avg(matrix)', 'nameCount')
            .from(VideoDetailModel, 'detail')
            .where('detail.app_key = :appKey', { appKey: props.appKey })
            .andWhere('detail.target = :target', { target: rule.target })
            .andWhere('detail.dimension = :dimension', { dimension: subRule[1] })
            .andWhere('detail.cur_time between str_to_date(:start, "%Y-%m-%d %H:%i") and str_to_date(:end, "%Y-%m-%d %H:%i")', { start: startTimeMinute, end: endTimeMinute })
            .groupBy('dimension')
            .having(`nameCount ${OPERATOR[subRule[3]]} :count`, { count: subRule[2] })
            .getRawMany<ISqlSubruleResult>();
        });
        try {
          subRuleResult = await Promise.all(subRuleResultPromises);
          this.ctx.localLog('log', {
            msg: 'subRuleResult',
            data: { subRuleResult },
          });
        } catch (error) {
          this.ctx.localLog('log', {
            msg: '查询 subRule 失败',
            data: { error },
          });
        }
      }

      this.ctx.localLog('log', {
        msg: 'ruleResult',
        data: { ruleResults, rule },
      });

      // return tags;
      return {
        rule: ruleResults,
        subRule: subRuleResult,
      };
    });

    return await Promise.all(ruleResultPromises);
  }

  public async findAudio(props: IRule) {
    this.ctx.localLog('log', {
      msg: 'findAudio',
      data: {},
    });

    const ruleResultPromises = props.rules.map(async rule => {
      const startTime = format(Date.now() - rule.matchRule[0] * 60 * 1000, 'YYYY-MM-DD HH:mm:ss');
      // const startTime = format(Date.now() - rule.matchRule[0] * 60 * 1000 * 30 * 24 * 20, 'YYYY-MM-DD HH:mm:ss'); // TODO: 需要改回来
      const endTime = format(Date.now(), 'YYYY-MM-DD HH:mm:ss');

      const startTimeMinute = format(Date.now() - rule.matchRule[0] * 60 * 1000, 'YYYY-MM-DD HH:mm:ss');
      // const startTimeMinute = format(Date.now() - rule.matchRule[0] * 60 * 1000 * 30 * 24 * 20, 'YYYY-MM-DD HH:mm'); // TODO: 需要改回来
      const endTimeMinute = format(Date.now(), 'YYYY-MM-DD HH:mm');

      const selectAndGroupbyArray = rule.granularity.split('&').map(o => `${o}`);

      let sql = getConnection()
        .createQueryBuilder()
        .select('count(*)', 'nameCount');

      if (rule.granularity !== 'all') {
        sql = sql.addSelect(selectAndGroupbyArray);
      }

      sql.from(AudioModel, 'detail')
        .where('detail.timestamps_str between str_to_date(:start, "%Y-%m-%d %H:%i:%s") and str_to_date(:end, "%Y-%m-%d %H:%i:%s")', { start: startTime, end: endTime })
        .andWhere('detail.app_key = :appKey', { appKey: props.appKey });

        // 具体对某指标的筛选过滤条件
        // 可枚举递增
      if (rule.target === 'error') {
        // 错误指标
        sql = sql.andWhere('detail.action_type = :actionType', { actionType: 'error' });
      }

      if (rule.granularity !== 'all') {
        selectAndGroupbyArray.map(o => {
          sql = sql.addGroupBy(o);
        });
        sql = sql.having('nameCount >= :count', { count: rule.matchRule[1] });
      }

      // 限制 50 条，防止命中规则的数量太多导致查询缓慢
      sql = sql.limit(50);

      // const tags = sql.getSql();

      let ruleResults = await sql.getRawMany<ISqlRuleResult>();

      // 如果粒度是 all 的情况下，在 sql 有结果后才能对其 nameCount 进行比较
      if (rule.granularity === 'all') {
        if (rule.matchRule[2] === Operator.gte) {
          if (Number(ruleResults[0].nameCount) < rule.matchRule[1]) {
            ruleResults = [];
          }
        } else if (rule.matchRule[2] === Operator.lte) {
          if (Number(ruleResults[0].nameCount) > rule.matchRule[1]) {
            ruleResults = [];
          }
        }
      }

      let subRuleResult: ISqlSubruleResult[][] = [];

      // 判断是否有 subMatch 规则，有的话需要且逻辑计算（只有 granularity === all 是才会有 subMatch
      // 主规则有结果命中时才会触发子规则的查询
      if (rule.granularity === 'all' && rule.subMatchRule.length && ruleResults.length) {
        const subRuleResultPromises = rule.subMatchRule.map(async subRule => {
          return await getConnection()
            .createQueryBuilder()
            .select(['dimension', 'target'])
            .addSelect('avg(matrix)', 'nameCount')
            .from(AudioDetailModel, 'detail')
            .where('detail.app_key = :appKey', { appKey: props.appKey })
            .andWhere('detail.target = :target', { target: rule.target })
            .andWhere('detail.dimension = :dimension', { dimension: subRule[1] })
            .andWhere('detail.cur_time between str_to_date(:start, "%Y-%m-%d %H:%i") and str_to_date(:end, "%Y-%m-%d %H:%i")', { start: startTimeMinute, end: endTimeMinute })
            .groupBy('dimension')
            .having(`nameCount ${OPERATOR[subRule[3]]} :count`, { count: subRule[2] })
            .getRawMany<ISqlSubruleResult>();
        });
        try {
          subRuleResult = await Promise.all(subRuleResultPromises);
          this.ctx.localLog('log', {
            msg: 'subRuleResult',
            data: { subRuleResult },
          });
        } catch (error) {
          this.ctx.localLog('log', {
            msg: '查询 subRult 失败',
            data: { error },
          });
        }
      }

      this.ctx.localLog('log', {
        msg: 'ruleResult',
        data: { ruleResults, rule },
      });

      return {
        rule: ruleResults,
        subRule: subRuleResult,
      };
    });

    return await Promise.all(ruleResultPromises);
  }

  public async findClass(props: IRule) {
    this.ctx.localLog('log', 'findClass');

    const ruleResultPromises = props.rules.map(async rule => {
      const startTime = format(Date.now() - rule.matchRule[0] * 60 * 1000, 'YYYY-MM-DD HH:mm:ss');
      // const startTime = format(Date.now() - rule.matchRule[0] * 60 * 1000 * 30 * 24 * 20, 'YYYY-MM-DD HH:mm:ss'); //  TODO: 需要改回来
      const endTime = format(Date.now(), 'YYYY-MM-DD HH:mm:ss');

      const startTimeMinute = format(Date.now() - rule.matchRule[0] * 60 * 1000, 'YYYY-MM-DD HH:mm:ss');
      // const startTimeMinute = format(Date.now() - rule.matchRule[0] * 60 * 1000 * 30 * 24 * 20, 'YYYY-MM-DD HH:mm'); //  TODO: 需要改回来
      const endTimeMinute = format(Date.now(), 'YYYY-MM-DD HH:mm');

      const selectAndGroupbyArray = rule.granularity.split('&').map(o => `${o}`);

      let sql = getConnection()
        .createQueryBuilder()
        .select(RULE_NAMECOUNT_SQL_SELECT[rule.target], 'nameCount');

      // 如果不是 all 粒度，则添加 nameCount 列，用于 having 筛选规则
      if (rule.granularity !== 'all') {
        sql = sql.addSelect(selectAndGroupbyArray);
      }

      sql.from(ClassModel, 'detail')
        .where('detail.timestamps_str between str_to_date(:start, "%Y-%m-%d %H:%i:%s") and str_to_date(:end, "%Y-%m-%d %H:%i:%s")',
        { start: startTime, end: endTime })
        .andWhere('detail.app_key = :appKey', { appKey: props.appKey });

      // 具体对某指标的筛选过滤条件
      if (rule.target === TARGET.ERROR) {
        // 错误指标
        sql = sql.andWhere('detail.action_type = :actionType', { actionType: 'error' })
        .andWhere('detail.action = :action', { action: 'loginroom-error' });
      } else if (rule.target === TARGET.LOGIN_TIME) {
        sql = sql.andWhere('detail.action_type = :actionType', { actionType: 'login' })
        .andWhere('detail.action = :action', { action: 'loginroom-time' });
      }

      if (rule.granularity !== 'all') {
        selectAndGroupbyArray.map(o => {
          sql = sql.addGroupBy(o);
        });
        sql = sql.having('nameCount >= :count', { count: rule.matchRule[1] });
      }

      // 限制 50 条，防止命中规则的数量太多导致查询缓慢
      sql = sql.limit(50);

      // const tags = sql.getSql();

      let ruleResults = await sql.getRawMany<ISqlRuleResult>();

      // 如果粒度是 all 的情况下，在 sql 有结果后才能对其 nameCount 进行比较
      if (rule.granularity === 'all') {
        if (rule.matchRule[2] === Operator.gte) {
          if (Number(ruleResults[0].nameCount) < rule.matchRule[1]) {
            ruleResults = [];
          }
        } else if (rule.matchRule[2] === Operator.lte) {
          if (Number(ruleResults[0].nameCount) > rule.matchRule[1]) {
            ruleResults = [];
          }
        }
      }

      let subRuleResult: ISqlSubruleResult[][] = [];

      // 判断是否有 subMatch 规则，有的话需要且逻辑计算（只有 granularity === all 是才会有 subMatch
      if (rule.granularity === 'all' && rule.subMatchRule.length && ruleResults.length) {
        const subRuleResultPromises = rule.subMatchRule.map(async subRule => {
          return await getConnection()
            .createQueryBuilder()
            .select(['dimension', 'target'])
            .addSelect('avg(matrix)', 'nameCount')
            .from(ClassDetailModel, 'detail')
            .where('detail.app_key = :appKey', { appKey: props.appKey })
            .andWhere('detail.target = :target', { target: rule.target })
            .andWhere('detail.dimension = :dimension', { dimension: subRule[1] })
            .andWhere('detail.cur_time between str_to_date(:start, "%Y-%m-%d %H:%i") and str_to_date(:end, "%Y-%m-%d %H:%i")',
            { start: startTimeMinute, end: endTimeMinute })
            .groupBy('dimension')
            .having(`nameCount ${OPERATOR[subRule[3]]} :count`, { count: subRule[2] })
            .getRawMany<ISqlSubruleResult>();
        });
        try {
          subRuleResult = await Promise.all(subRuleResultPromises);
          this.ctx.localLog('log', {
            msg: 'subRuleResult',
            data: subRuleResult,
          });
        } catch (error) {
          this.ctx.localLog('log', {
            msg: '查询 subRule 失败',
            data: error,
          });
        }
      }

      this.ctx.localLog('log', {
        msg: 'ruleResult',
        data: JSON.stringify({ ruleResults, rule }),
      });

      return {
        rule: ruleResults,
        subRule: subRuleResult,
      };
    });

    return await Promise.all(ruleResultPromises);
  }
};
