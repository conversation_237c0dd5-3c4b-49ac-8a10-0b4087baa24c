import BaseService from '../base/BaseService';
import AudioModel from '@models/media-log/audio';
import AudioDetailModel from '@models/media-log/audioDetail';
import { sortBy } from 'lodash';
import format from 'date-fns/format';
import { getManager, getConnection } from 'typeorm';
import { PageInfo } from '@definitions/base';
import { DEMENSION_ERROR, DEMENSION_TEXT_MAP, TARGET } from '@constants/media-log';
import { IErrorTopNSqlRes, ITimelineRes, ITimelineSqlRes } from '@definitions/media-log/base';

interface IErrorTimelineProps {
  osType: string;
  appKey: string;
  startTime: number;
  endTime: number;
  target: string;
}

interface ITopNProps {
  osType: string;
  appKey: string;
  startTime: number;
  endTime: number;
}

export = class AudioRealtimeService extends BaseService {
  public async getErrorTimeline(props: IErrorTimelineProps) {
    const osType = props.osType;
    const appKey = props.appKey;
    const startTime = props.startTime;
    const endTime = props.endTime;
    const target = props.target;

    const dimensionList: Record<string, string[]> = {
      [TARGET.ERROR]: Object.values(DEMENSION_ERROR),
    };

    const querys = dimensionList[target].map(async query => {
      return getConnection()
        .createQueryBuilder()
        .select()
        .from(AudioDetailModel, 'detail')
        .where('detail.target = :target', { target: 'error' })
        .andWhere('detail.dimension = :dimension', { dimension: query })
        .andWhere('detail.os = :os', { os: osType })
        .andWhere('detail.app_key = :appKey', { appKey: appKey })
        .andWhere('detail.cur_time between str_to_date(:start, "%Y-%m-%d %H:%i") and str_to_date(:end, "%Y-%m-%d %H:%i")',
        { start: format(+startTime, 'YYYY-MM-DD HH:mm'), end: format(+endTime, 'YYYY-MM-DD HH:mm') })
        .getRawMany<ITimelineSqlRes>();
    });

    try {
      const queryResults = await Promise.all(querys);
      const deMapped = queryResults.reduce<ITimelineRes[]>((prev, curr, index) => {
        const prevList = prev;
        curr.forEach(currItme => {
          prevList.push({
            ...currItme,
            matrix: Number(currItme.matrix),
            dimension: DEMENSION_TEXT_MAP[target][dimensionList[target][index]],
            dimension_origin: dimensionList[target][index],
          });
        });
        return prevList;
      }, []);

      this.ctx.localLog('log', {
        queryResults,
        msg: 'queryResults',
      });

      return sortBy(deMapped, [(o) => { return +new Date(o.cur_time) }]);
    } catch (error) {
      this.ctx.localLog('error', error);
      throw error;
    }
  }

  public async getErrorTopNList(props: ITopNProps) {
    const osType = props.osType;
    const appKey = props.appKey;
    const startTime = props.startTime;
    const endTime = props.endTime;

    const queryResults = await getConnection()
      .createQueryBuilder()
      .select(['action', 'action_data'])
      .addSelect('count(*)', 'nameCount')
      .from(AudioModel, 'detail')
      .where('detail.os = :os', { os: osType })
      .andWhere('detail.action_type = "error"')
      .andWhere('detail.app_key = :appKey', { appKey: appKey })
      // eslint-disable-next-line
      .andWhere('detail.timestamps_str between str_to_date(:start, "%Y-%m-%d %H:%i:%s") and str_to_date(:end, "%Y-%m-%d %H:%i:%s")',
      { start: format(+startTime, 'YYYY-MM-DD HH:mm:ss'), end: format(+endTime, 'YYYY-MM-DD HH:mm:ss') })
      .groupBy('action')
      .addGroupBy('action_data')
      .orderBy('nameCount', 'DESC')
      .limit(10)
      .getRawMany<IErrorTopNSqlRes>();

    this.ctx.localLog('log', {
      queryResults,
      msg: 'queryResults',
    });

    return queryResults;
  }
};
