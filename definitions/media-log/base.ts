export interface ISqlRuleResult {
  string: unknown;
  nameCount: string;
}

export interface ISqlSubruleResult {
  string: unknown;
  nameCount: string;
}

export enum Operator {
  gte = 'gte',
  lte = 'lte',
}

export interface ITimelineSqlRes {
  matrix: string;
  cur_time: string;
}

export interface IDataInsightSqlRes {
  matrix_target: string;
  matrix_all: string;
  cur_time: string;
}

export interface IErrorTopNSqlRes {
  action: string;
  action_data: string;
  nameCount: string;
}

export interface ITimelineRes {
  matrix: number;
  cur_time: string;
  dimension: string;
  dimension_origin: string;
}
