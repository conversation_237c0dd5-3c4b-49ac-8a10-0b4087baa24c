export interface ICreateSentryIssueQuery {
  summary: string;
  description: string;
  path: string;
}

export interface ICreateIssueData {
  summary: string;
  description: string;
  issuetypeId: string;
  projectKey: string;
  priorityId: string;
  assignee: string;
  reporter: string;
}

export interface ICreateIssueDTO {
  fields: {
    summary: string;
    description: string;
    issuetype: {
      id: string;
    };
    project: {
      key: string;
    };
    reporter: {
      name: string;
    };
    assignee: {
      name: string;
    };
    priority: {
      id: string;
    };
  };
}
