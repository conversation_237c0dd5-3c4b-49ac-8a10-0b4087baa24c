import { IAstroboyContext } from 'astroboy/definitions';
import { IYouzanFrameworkApplication, IYouzanFrameworkDefine, IYouzanFrameworkContext } from '@youzan/youzan-framework/definitions';
import { Sequelize } from 'sequelize';

/**
 * 描述JS语言环境下的number数据，路由参数中的数字与字符串可能存在模糊
 * * 在必要的时候应该使用Number强转
 */
declare type JSNumber = string | number;

/** JS环境下的路由参数boolean-true类型 */
declare type JSTrue = 'true' | true;
/** JS环境下的路由参数boolean-false类型 */
declare type JSFalse = 'false' | false;

/**
 * 描述JS语言环境下的boolean数据，路由参数中的boolean与字符串可能存在模糊
 * * 在必要的时候应该进行鉴别处理
 */
declare type JSBoolean = JSTrue | JSFalse;

declare interface IPureObject {
  [key: string]: any;
}

declare module '@youzan/skynet-log';
export interface IEbizFePlatformApplication extends IYouzanFrameworkApplication {
  rds: Sequelize;
}
export interface IEbizFePlatformDefine extends IYouzanFrameworkDefine {
  app: IEbizFePlatformApplication;
}
export interface IEbizFePlatformContext<DEFINE extends IEbizFePlatformDefine = IEbizFePlatformDefine>
  extends IAstroboyContext<DEFINE>,
    IYouzanFrameworkExtendsContext,
    IYouzanFrameworkContextMixins<DEFINE>,
    IYouzanFrameworkContext<DEFINE> {}

export interface PageInfo {
  page: number;
  pageSize: number;
}

export interface ICategoryQuery extends PageInfo {
  category?: string;
}
