{"compilerOptions": {"baseUrl": ".", "outDir": "./dist", "strict": true, "allowJs": true, "checkJs": false, "module": "commonjs", "target": "ES2017", "lib": ["ES2017"], "esModuleInterop": true, "skipLibCheck": true, "noEmitHelpers": true, "importHelpers": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "typeRoots": ["./node_modules/@types", "./node_modules/@youzan-types", "./node_modules/@qima-inc"], "types": ["node", "astroboy", "koa", "koa-router"], "paths": {"@definitions/*": ["./definitions/*"], "@controllers/*": ["./app/controllers/*"], "@services/*": ["./app/services/*"], "@models/*": ["./app/models/*"], "@lib/*": ["./app/lib/*"], "@utils/*": ["./app/utils/*"], "@constants/*": ["./app/constants/*"]}}, "include": ["./definitions", "./app/**/*", "./config/**/*", "./plugins/**/*", "boot.ts"], "exclude": ["**/node_modules"]}