/**
 * 默认配置文件
 */
const path = require('path');

module.exports = {
  view: {
    root: [path.resolve(__dirname, '../app/views')],

    script: {
      crossorigin: true,
    },
  },
  apolloConfig: [
    // 设置 namespace 所有 key 的数据转换函数，默认执行 JSON.parse
    {
      appId: 'ebiz-fe-platform',
      namespace: 'application',
    },
    {
      appId: 'ebiz-fe-platform',
      namespace: 'skynet-log',
    },
  ]
};
