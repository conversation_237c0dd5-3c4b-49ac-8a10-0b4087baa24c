const fs = require('fs');
const path = require('path');
const chalk = require('chalk');
const { isLowerCaseOrHyphen } = require('./case-utils');

const { log } = console;
const routerPath = path.join(__dirname, '../../app/routers');
const fileCheckList = [];
const errorRouterList = {};

function generator(dir) {
  const dirEntries = fs.readdirSync(dir);
  const routerDir = [];
  dirEntries.forEach(entry => {
    const stat = fs.statSync(path.join(dir, entry));
    if (stat.isFile()) {
      return fileCheckList.push(path.join(dir, entry));
    }
    routerDir.push(path.join(dir, entry));
  });
  routerDir.forEach(d => generator(d));
}
// 检查出错的router 文件列表
function checkRouter(path, file) {
  let actionDesc = path.split('/').slice(-1)[0];
  if (/.json/.test(actionDesc)) {
    actionDesc = actionDesc.split('/').slice(0, 1);
    if (!isLowerCaseOrHyphen(actionDesc)) {
      if (!errorRouterList[file]) {
        errorRouterList[file] = [];
      }
      errorRouterList[file].push(actionDesc[0]);
    }
  }
}

function routerChecker() {
  // 生成所有的router file
  generator(routerPath);

  // 读取所有router 对其中的文件进行校验
  fileCheckList.forEach(file => {
    // eslint-disable-next-line
    const router = require(file);
    router.forEach(r => {
      if (Array.isArray(r[1])) {
        return r[1].forEach(path => checkRouter(path, file));
      }
      checkRouter(r[1], file);
    });
  });


  log(chalk.bgRed('以下router文件中路径命名中有不规范的部分，具体细节如下请修正'));

  const pathList = Object.keys(errorRouterList);

  pathList.forEach(path => {
    log(chalk.bgYellow(path));
    log(errorRouterList[path].join('\n'));
  });
}

module.exports = routerChecker;
