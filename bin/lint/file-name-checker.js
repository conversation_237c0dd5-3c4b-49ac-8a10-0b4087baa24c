const fs = require('fs');
const path = require('path');
const chalk = require('chalk');
const {
  isLowerCaseOrHyphen,
  isPascalCase,
  pascalCaseForVue
} = require('./case-utils');

const { log } = console;

const DIR_TO_CHECK = ['../../app', '../../client'];
// 需要忽略的文件夹
const DIR_TO_IGNORE = ['node_modules', 'build', 'local', 'shared'];

// 特殊校验规则文件夹
const SPECIAL_PASCAL_DIR = ['app/controllers', 'app/service'];

const errorDirList = [];
const errorJsOrCssList = [];
const errorVueList = [];
const errorSpecialList = [];

const ruleMap = {
  '.vue': pascalCaseForVue,
  '.js': isLowerCaseOrHyphen,
  '.css': isLowerCaseOrHyphen,
  '.scss': isLowerCaseOrHyphen,
  special: isPascalCase
};

const ErrorMap = {
  lowerCaseOrHyphen: '必须是小写，以中划线分割',
  pascalCase: '必须是大驼峰形式',
  pascalVueCase: '除index.vue 其余均需大驼峰'
};

function checkFileName(parent, fileName) {
  if (fileName[0] === '.') return;
  const fileFullName = `${parent}/${fileName}`;
  let extName = path.extname(fileFullName);
  fileName = fileName.split('.').slice(0, -1).join('.');
  // 特殊规则文件夹做特殊校验
  if (SPECIAL_PASCAL_DIR.some(dir => fileFullName.indexOf(dir) > -1)) extName = 'special';
  // 根据不同文件名进行校验
  if (ruleMap[extName] && !ruleMap[extName](fileName)) {
    if (extName === '.vue') return errorVueList.push(fileFullName);
    if (extName === 'special') return errorSpecialList.push(fileFullName);
    return errorJsOrCssList.push(fileFullName);
  }
}

function checkDirName(parent, dirName) {
  const dirFullName = `${parent}/${dirName}`;
  if (!isLowerCaseOrHyphen(dirName)) {
    errorDirList.push(dirFullName);
  }
}

function logErrors(nameList, errorMsg) {
  if (!nameList.length) return;
  log(chalk.bgRed(errorMsg));
  nameList.forEach(name => {
    log(chalk.underline(name));
  });
}

function checkName(root, options = {}) {
  // 拿到当前目录的入口的一些文件(夹)名
  const dirEntries = fs.readdirSync(root);

  if (dirEntries.length === 0) return true;

  const dirs = [];

  dirEntries.forEach(entry => {
    const fullPath = path.join(root, entry);
    const stat = fs.statSync(fullPath);

    if (stat.isFile()) return checkFileName(root, entry);

    if (stat.isDirectory() && !DIR_TO_IGNORE.includes(entry)) {
      dirs.push(entry);
      return checkDirName(root, entry);
    }
  });

  dirs.forEach(d => checkName(path.join(root, d), options));
}

function fileNameChecker(options) {
  DIR_TO_CHECK.forEach(dir => {
    dir = path.join(__dirname, dir);
    try {
      checkName(dir, options);
    } catch (err) {
      console.error(err.message);
      process.exit(101);
    }
  });
  logErrors(errorDirList, `文件夹名错误${ErrorMap.lowerCaseOrHyphen}`);
  logErrors(errorJsOrCssList, `css或js或scss文件名错误 ${ErrorMap.lowerCaseOrHyphen}`);
  logErrors(errorVueList, `vue 文件名错误 ${ErrorMap.pascalVueCase}`);
  logErrors(errorSpecialList, `controller或service 文件命名错误 ${ErrorMap.pascalCase}`);
}

module.exports = fileNameChecker;
