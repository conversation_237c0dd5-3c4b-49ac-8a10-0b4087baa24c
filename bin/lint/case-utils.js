function isLowerCaseOrHyphen(str) {
  return /^(_)?[a-z][0-9a-z-.]*$/.test(str);
}

function isSassMixinFileName(str) {
  return /^_[a-z][0-9a-z-]*$/.test(str);
}

function isCamelCase(str) {
  return /^[A-Z][a-zA-Z0-9]*$/.test(str);
}

function isLowerCase(str) {
  return /^[a-z][0-9a-z]*$/.test(str);
}

function isPascalCase(str) {
  return /^[A-Z][a-z0-9]+(?:[A-Z][a-z0-9]+)*$/.test(str);
}

function pascalCaseForVue(str) {
  // 除index.vue 其余需大驼峰
  if (str === 'index') return true;
  return isPascalCase(str);
}

module.exports = {
  isLowerCaseOrHyphen,
  isSassMixinFileName,
  isCamelCase,
  isLowerCase,
  isPascalCase,
  pascalCaseForVue
};
