# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@^7.0.0":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/code-frame/download/@babel/code-frame-7.8.3.tgz#33e25903d7481181534e12ec0a25f16b6fcf419e"
  integrity sha1-M+JZA9dIEYFTThLsCiXxa2/PQZ4=
  dependencies:
    "@babel/highlight" "^7.8.3"

"@babel/helper-validator-identifier@^7.9.0":
  version "7.9.5"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.9.5.tgz#90977a8e6fbf6b431a7dc31752eee233bf052d80"
  integrity sha1-kJd6jm+/a0MafcMXUu7iM78FLYA=

"@babel/highlight@^7.8.3":
  version "7.9.0"
  resolved "http://registry.npm.qima-inc.com/@babel/highlight/download/@babel/highlight-7.9.0.tgz#4e9b45ccb82b79607271b2979ad82c7b68163079"
  integrity sha1-TptFzLgreWBycbKXmtgse2gWMHk=
  dependencies:
    "@babel/helper-validator-identifier" "^7.9.0"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/runtime-corejs3@^7.8.3":
  version "7.10.4"
  resolved "http://registry.npm.qima-inc.com/@babel/runtime-corejs3/download/@babel/runtime-corejs3-7.10.4.tgz#f29fc1990307c4c57b10dbd6ce667b27159d9e0d"
  integrity sha1-8p/BmQMHxMV7ENvWzmZ7JxWdng0=
  dependencies:
    core-js-pure "^3.0.0"
    regenerator-runtime "^0.13.4"

"@babel/runtime@^7.0.0":
  version "7.9.2"
  resolved "http://registry.npm.qima-inc.com/@babel/runtime/download/@babel/runtime-7.9.2.tgz#d90df0583a3a252f09aaa619665367bae518db06"
  integrity sha1-2Q3wWDo6JS8JqqYZZlNnuuUY2wY=
  dependencies:
    regenerator-runtime "^0.13.4"

"@hapi/address@2.x.x":
  version "2.1.4"
  resolved "http://registry.npm.qima-inc.com/@hapi/address/download/@hapi/address-2.1.4.tgz#5d67ed43f3fd41a69d4b9ff7b56e7c0d1d0a81e5"
  integrity sha1-XWftQ/P9QaadS5/3tW58DR0KgeU=

"@hapi/hoek@6.x.x":
  version "6.2.4"
  resolved "http://registry.npm.qima-inc.com/@hapi/hoek/download/@hapi/hoek-6.2.4.tgz#4b95fbaccbfba90185690890bdf1a2fbbda10595"
  integrity sha1-S5X7rMv7qQGFaQiQvfGi+72hBZU=

"@hapi/hoek@^8.3.0":
  version "8.5.1"
  resolved "http://registry.npm.qima-inc.com/@hapi/hoek/download/@hapi/hoek-8.5.1.tgz#fde96064ca446dec8c55a8c2f130957b070c6e06"
  integrity sha1-/elgZMpEbeyMVajC8TCVewcMbgY=

"@hapi/joi@15.1.0":
  version "15.1.0"
  resolved "http://registry.npm.qima-inc.com/@hapi/joi/download/@hapi/joi-15.1.0.tgz#940cb749b5c55c26ab3b34ce362e82b6162c8e7a"
  integrity sha1-lAy3SbXFXCarOzTONi6CthYsjno=
  dependencies:
    "@hapi/address" "2.x.x"
    "@hapi/hoek" "6.x.x"
    "@hapi/marker" "1.x.x"
    "@hapi/topo" "3.x.x"

"@hapi/marker@1.x.x":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@hapi/marker/download/@hapi/marker-1.0.0.tgz#65b0b2b01d1be06304886ce9b4b77b1bfb21a769"
  integrity sha1-ZbCysB0b4GMEiGzptLd7G/shp2k=

"@hapi/topo@3.x.x":
  version "3.1.6"
  resolved "http://registry.npm.qima-inc.com/@hapi/topo/download/@hapi/topo-3.1.6.tgz#68d935fa3eae7fdd5ab0d7f953f3205d8b2bfc29"
  integrity sha1-aNk1+j6uf91asNf5U/MgXYsr/Ck=
  dependencies:
    "@hapi/hoek" "^8.3.0"

"@mrmlnc/readdir-enhanced@^2.2.1":
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/@mrmlnc/readdir-enhanced/download/@mrmlnc/readdir-enhanced-2.2.1.tgz#524af240d1a360527b730475ecfa1344aa540dde"
  integrity sha1-UkryQNGjYFJ7cwR17PoTRKpUDd4=
  dependencies:
    call-me-maybe "^1.0.1"
    glob-to-regexp "^0.3.0"

"@napi-rs/snappy-android-arm64@^1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@napi-rs/snappy-android-arm64/download/@napi-rs/snappy-android-arm64-1.0.2.tgz#98897013e3f1fd49eec90262b3928743067768a3"
  integrity sha1-mIlwE+Px/UnuyQJis5KHQwZ3aKM=

"@napi-rs/snappy-darwin-arm64@^1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@napi-rs/snappy-darwin-arm64/download/@napi-rs/snappy-darwin-arm64-1.0.2.tgz#95cff8e68e6dab36bde25fbcf9475cc87fcdc16e"
  integrity sha1-lc/45o5tqza94l+8+UdcyH/NwW4=

"@napi-rs/snappy-darwin-x64@^1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@napi-rs/snappy-darwin-x64/download/@napi-rs/snappy-darwin-x64-1.0.2.tgz#40ecf4496f55e084f3114eb4f5a9da6f593311dc"
  integrity sha1-QOz0SW9V4ITzEU609anab1kzEdw=

"@napi-rs/snappy-freebsd-x64@^1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@napi-rs/snappy-freebsd-x64/download/@napi-rs/snappy-freebsd-x64-1.0.2.tgz#43b32e51e7c838fa39a80d47a00fe769ba91b861"
  integrity sha1-Q7MuUefIOPo5qA1HoA/nabqRuGE=

"@napi-rs/snappy-linux-arm-gnueabihf@^1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@napi-rs/snappy-linux-arm-gnueabihf/download/@napi-rs/snappy-linux-arm-gnueabihf-1.0.2.tgz#a7707915c04ebbeb2b286f9efa77e90df27d69a5"
  integrity sha1-p3B5FcBOu+srKG+e+nfpDfJ9aaU=

"@napi-rs/snappy-linux-arm64-gnu@^1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@napi-rs/snappy-linux-arm64-gnu/download/@napi-rs/snappy-linux-arm64-gnu-1.0.2.tgz#5c5ae1d0d3982812b7d546d8d247d65b9e7e6135"
  integrity sha1-XFrh0NOYKBK31UbY0kfWW55+YTU=

"@napi-rs/snappy-linux-arm64-musl@^1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@napi-rs/snappy-linux-arm64-musl/download/@napi-rs/snappy-linux-arm64-musl-1.0.2.tgz#1ec4b3caec2adead05a0b2750527e439406d9ec4"
  integrity sha1-HsSzyuwq3q0FoLJ1BSfkOUBtnsQ=

"@napi-rs/snappy-linux-x64-gnu@^1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@napi-rs/snappy-linux-x64-gnu/download/@napi-rs/snappy-linux-x64-gnu-1.0.2.tgz#aff802428f07a0cc2b73bf56aa3fe28661881be6"
  integrity sha1-r/gCQo8HoMwrc79Wqj/ihmGIG+Y=

"@napi-rs/snappy-linux-x64-musl@^1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@napi-rs/snappy-linux-x64-musl/download/@napi-rs/snappy-linux-x64-musl-1.0.2.tgz#78b8fd5e83ec613580f79e4200d3b1fe463a6d11"
  integrity sha1-eLj9XoPsYTWA955CANOx/kY6bRE=

"@napi-rs/snappy-win32-arm64-msvc@^1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@napi-rs/snappy-win32-arm64-msvc/download/@napi-rs/snappy-win32-arm64-msvc-1.0.2.tgz#e8f46b3c055f4418e5f96c45f577bc843df500a0"
  integrity sha1-6PRrPAVfRBjl+WxF9Xe8hD31AKA=

"@napi-rs/snappy-win32-ia32-msvc@^1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@napi-rs/snappy-win32-ia32-msvc/download/@napi-rs/snappy-win32-ia32-msvc-1.0.2.tgz#57399ecd56fbde6f1366140e0c77ad859f7380db"
  integrity sha1-VzmezVb73m8TZhQODHethZ9zgNs=

"@napi-rs/snappy-win32-x64-msvc@^1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@napi-rs/snappy-win32-x64-msvc/download/@napi-rs/snappy-win32-x64-msvc-1.0.2.tgz#a27a3a91ebbed4a6a00376bed15fa0eddc506891"
  integrity sha1-ono6keu+1KagA3a+0V+g7dxQaJE=

"@napi-rs/snappy@^1.0.1":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@napi-rs/snappy/download/@napi-rs/snappy-1.0.2.tgz#43dcc7d0578edaf4fa683b713d907f7e8c8aaa36"
  integrity sha1-Q9zH0FeO2vT6aDtxPZB/foyKqjY=
  dependencies:
    "@node-rs/helper" "^1.2.0"
  optionalDependencies:
    "@napi-rs/snappy-android-arm64" "^1.0.2"
    "@napi-rs/snappy-darwin-arm64" "^1.0.2"
    "@napi-rs/snappy-darwin-x64" "^1.0.2"
    "@napi-rs/snappy-freebsd-x64" "^1.0.2"
    "@napi-rs/snappy-linux-arm-gnueabihf" "^1.0.2"
    "@napi-rs/snappy-linux-arm64-gnu" "^1.0.2"
    "@napi-rs/snappy-linux-arm64-musl" "^1.0.2"
    "@napi-rs/snappy-linux-x64-gnu" "^1.0.2"
    "@napi-rs/snappy-linux-x64-musl" "^1.0.2"
    "@napi-rs/snappy-win32-arm64-msvc" "^1.0.2"
    "@napi-rs/snappy-win32-ia32-msvc" "^1.0.2"
    "@napi-rs/snappy-win32-x64-msvc" "^1.0.2"

"@napi-rs/triples@^1.1.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@napi-rs/triples/download/@napi-rs/triples-1.1.0.tgz#88c35b72e79a20b79bb4c9b3e2817241a1c9f4f9"
  integrity sha1-iMNbcueaILebtMmz4oFyQaHJ9Pk=

"@node-rs/crc32-android-arm-eabi@1.4.3":
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/@node-rs/crc32-android-arm-eabi/download/@node-rs/crc32-android-arm-eabi-1.4.3.tgz#d4a01314130b44ca775461652743701d3eeb8094"
  integrity sha1-1KATFBMLRMp3VGFlJ0NwHT7rgJQ=

"@node-rs/crc32-android-arm64@1.4.3":
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/@node-rs/crc32-android-arm64/download/@node-rs/crc32-android-arm64-1.4.3.tgz#eea433a9bace8b6e7c3a649ff2746c76dbd10e9e"
  integrity sha1-7qQzqbrOi258OmSf8nRsdtvRDp4=

"@node-rs/crc32-darwin-arm64@1.4.3":
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/@node-rs/crc32-darwin-arm64/download/@node-rs/crc32-darwin-arm64-1.4.3.tgz#26bcd0d345bd03f092d8449e3fda88fb7f0658b1"
  integrity sha1-JrzQ00W9A/CS2ESeP9qI+38GWLE=

"@node-rs/crc32-darwin-x64@1.4.3":
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/@node-rs/crc32-darwin-x64/download/@node-rs/crc32-darwin-x64-1.4.3.tgz#882ca6ce9605dc43890ed2d3a2c0d4c99d9f357c"
  integrity sha1-iCymzpYF3EOJDtLTosDUyZ2fNXw=

"@node-rs/crc32-freebsd-x64@1.4.3":
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/@node-rs/crc32-freebsd-x64/download/@node-rs/crc32-freebsd-x64-1.4.3.tgz#7c863962b382dd400edd9780ab9cef791d90a5b2"
  integrity sha1-fIY5YrOC3UAO3ZeAq5zveR2QpbI=

"@node-rs/crc32-linux-arm-gnueabihf@1.4.3":
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/@node-rs/crc32-linux-arm-gnueabihf/download/@node-rs/crc32-linux-arm-gnueabihf-1.4.3.tgz#a5fc5bfc79d9f555ad814e81205af4c3fc194cfd"
  integrity sha1-pfxb/HnZ9VWtgU6BIFr0w/wZTP0=

"@node-rs/crc32-linux-arm64-gnu@1.4.3":
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/@node-rs/crc32-linux-arm64-gnu/download/@node-rs/crc32-linux-arm64-gnu-1.4.3.tgz#c24ec98a3fd8828d11a109b13414dc6996256890"
  integrity sha1-wk7Jij/Ygo0RoQmxNBTcaZYlaJA=

"@node-rs/crc32-linux-arm64-musl@1.4.3":
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/@node-rs/crc32-linux-arm64-musl/download/@node-rs/crc32-linux-arm64-musl-1.4.3.tgz#975ff5845bb2b95abd901747792b86346cd89f3f"
  integrity sha1-l1/1hFuyuVq9kBdHeSuGNGzYnz8=

"@node-rs/crc32-linux-x64-gnu@1.4.3":
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/@node-rs/crc32-linux-x64-gnu/download/@node-rs/crc32-linux-x64-gnu-1.4.3.tgz#b2ddfebd49369d79d1b4e7d2a9dd009a7e034c99"
  integrity sha1-st3+vUk2nXnRtOfSqd0Amn4DTJk=

"@node-rs/crc32-linux-x64-musl@1.4.3":
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/@node-rs/crc32-linux-x64-musl/download/@node-rs/crc32-linux-x64-musl-1.4.3.tgz#2692bbfa99a19fa3b7e046b18bf01bb4118a1f38"
  integrity sha1-JpK7+pmhn6O34Eaxi/AbtBGKHzg=

"@node-rs/crc32-win32-arm64-msvc@1.4.3":
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/@node-rs/crc32-win32-arm64-msvc/download/@node-rs/crc32-win32-arm64-msvc-1.4.3.tgz#e7f27f6f8380c9221fd41c006a6a991c79e9470a"
  integrity sha1-5/J/b4OAySIf1BwAamqZHHnpRwo=

"@node-rs/crc32-win32-ia32-msvc@1.4.3":
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/@node-rs/crc32-win32-ia32-msvc/download/@node-rs/crc32-win32-ia32-msvc-1.4.3.tgz#693e5243e10c77f64e1fc1ae719d665cf062c65e"
  integrity sha1-aT5SQ+EMd/ZOH8GucZ1mXPBixl4=

"@node-rs/crc32-win32-x64-msvc@1.4.3":
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/@node-rs/crc32-win32-x64-msvc/download/@node-rs/crc32-win32-x64-msvc-1.4.3.tgz#03d06d8f6a1d868f9678985c26ae90b1b569511c"
  integrity sha1-A9Btj2odho+WeJhcJq6QsbVpURw=

"@node-rs/crc32@^1.1.2":
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/@node-rs/crc32/download/@node-rs/crc32-1.4.3.tgz#702b1d9bc3f4a1f3209e469f4b221250d0f41a95"
  integrity sha1-cCsdm8P0ofMgnkafSyISUND0GpU=
  optionalDependencies:
    "@node-rs/crc32-android-arm-eabi" "1.4.3"
    "@node-rs/crc32-android-arm64" "1.4.3"
    "@node-rs/crc32-darwin-arm64" "1.4.3"
    "@node-rs/crc32-darwin-x64" "1.4.3"
    "@node-rs/crc32-freebsd-x64" "1.4.3"
    "@node-rs/crc32-linux-arm-gnueabihf" "1.4.3"
    "@node-rs/crc32-linux-arm64-gnu" "1.4.3"
    "@node-rs/crc32-linux-arm64-musl" "1.4.3"
    "@node-rs/crc32-linux-x64-gnu" "1.4.3"
    "@node-rs/crc32-linux-x64-musl" "1.4.3"
    "@node-rs/crc32-win32-arm64-msvc" "1.4.3"
    "@node-rs/crc32-win32-ia32-msvc" "1.4.3"
    "@node-rs/crc32-win32-x64-msvc" "1.4.3"

"@node-rs/helper@^1.2.0":
  version "1.3.3"
  resolved "http://registry.npm.qima-inc.com/@node-rs/helper/download/@node-rs/helper-1.3.3.tgz#2b1953846ddba31bfb7ecf4ba57e7b1359fcdedc"
  integrity sha1-KxlThG3boxv7fs9LpX57E1n83tw=
  dependencies:
    "@napi-rs/triples" "^1.1.0"

"@nodelib/fs.scandir@2.1.3":
  version "2.1.3"
  resolved "http://registry.npm.qima-inc.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.3.tgz#3a582bdb53804c6ba6d146579c46e52130cf4a3b"
  integrity sha1-Olgr21OATGum0UZXnEblITDPSjs=
  dependencies:
    "@nodelib/fs.stat" "2.0.3"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.3", "@nodelib/fs.stat@^2.0.2":
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.3.tgz#34dc5f4cabbc720f4e60f75a747e7ecd6c175bd3"
  integrity sha1-NNxfTKu8cg9OYPdadH5+zWwXW9M=

"@nodelib/fs.stat@^1.1.2":
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/@nodelib/fs.stat/download/@nodelib/fs.stat-1.1.3.tgz#2b5a3ab3f918cca48a8c754c08168e3f03eba61b"
  integrity sha1-K1o6s/kYzKSKjHVMCBaOPwPrphs=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.4"
  resolved "http://registry.npm.qima-inc.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.4.tgz#011b9202a70a6366e436ca5c065844528ab04976"
  integrity sha1-ARuSAqcKY2bkNspcBlhEUoqwSXY=
  dependencies:
    "@nodelib/fs.scandir" "2.1.3"
    fastq "^1.6.0"

"@samverschueren/stream-to-observable@^0.3.0":
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/@samverschueren/stream-to-observable/download/@samverschueren/stream-to-observable-0.3.0.tgz#ecdf48d532c58ea477acfcab80348424f8d0662f"
  integrity sha1-7N9I1TLFjqR3rPyrgDSEJPjQZi8=
  dependencies:
    any-observable "^0.3.0"

"@tql/utils@^1.1.2":
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/@tql/utils/download/@tql/utils-1.1.2.tgz#b81ba95391898eb4e7fde5347d0e614ce052c568"
  integrity sha1-uBupU5GJjrTn/eU0fQ5hTOBSxWg=
  dependencies:
    tslib "^2.4.0"

"@types/accepts@*":
  version "1.3.5"
  resolved "http://registry.npm.qima-inc.com/@types/accepts/download/@types/accepts-1.3.5.tgz#c34bec115cfc746e04fe5a059df4ce7e7b391575"
  integrity sha1-w0vsEVz8dG4E/loFnfTOfns5FXU=
  dependencies:
    "@types/node" "*"

"@types/body-parser@*":
  version "1.19.0"
  resolved "http://registry.npm.qima-inc.com/@types/body-parser/download/@types/body-parser-1.19.0.tgz#0685b3c47eb3006ffed117cdd55164b61f80538f"
  integrity sha1-BoWzxH6zAG/+0RfN1VFkth+AU48=
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/color-name@^1.1.1":
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/@types/color-name/download/@types/color-name-1.1.1.tgz#1c1261bbeaa10a8055bbc5d8ab84b7b2afc846a0"
  integrity sha1-HBJhu+qhCoBVu8XYq4S3sq/IRqA=

"@types/connect@*":
  version "3.4.33"
  resolved "http://registry.npm.qima-inc.com/@types/connect/download/@types/connect-3.4.33.tgz#31610c901eca573b8713c3330abc6e6b9f588546"
  integrity sha1-MWEMkB7KVzuHE8MzCrxua59YhUY=
  dependencies:
    "@types/node" "*"

"@types/content-disposition@*":
  version "0.5.3"
  resolved "http://registry.npm.qima-inc.com/@types/content-disposition/download/@types/content-disposition-0.5.3.tgz#0aa116701955c2faa0717fc69cd1596095e49d96"
  integrity sha1-CqEWcBlVwvqgcX/GnNFZYJXknZY=

"@types/cookies@*", "@types/cookies@^0.7.1":
  version "0.7.4"
  resolved "http://registry.npm.qima-inc.com/@types/cookies/download/@types/cookies-0.7.4.tgz#26dedf791701abc0e36b5b79a5722f40e455f87b"
  integrity sha1-Jt7feRcBq8Dja1t5pXIvQORV+Hs=
  dependencies:
    "@types/connect" "*"
    "@types/express" "*"
    "@types/keygrip" "*"
    "@types/node" "*"

"@types/debug@^4.1.4":
  version "4.1.5"
  resolved "http://registry.npm.qima-inc.com/@types/debug/download/@types/debug-4.1.5.tgz#b14efa8852b7768d898906613c23f688713e02cd"
  integrity sha1-sU76iFK3do2JiQZhPCP2iHE+As0=

"@types/eslint-visitor-keys@^1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@types/eslint-visitor-keys/download/@types/eslint-visitor-keys-1.0.0.tgz#1ee30d79544ca84d68d4b3cdb0af4f205663dd2d"
  integrity sha1-HuMNeVRMqE1o1LPNsK9PIFZj3S0=

"@types/events@*":
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/@types/events/download/@types/events-3.0.0.tgz#2862f3f58a9a7f7c3e78d79f130dd4d71c25c2a7"
  integrity sha1-KGLz9Yqaf3w+eNefEw3U1xwlwqc=

"@types/express-serve-static-core@*":
  version "4.17.4"
  resolved "http://registry.npm.qima-inc.com/@types/express-serve-static-core/download/@types/express-serve-static-core-4.17.4.tgz#157c79c2d28b632d6418497c57c93185e392e444"
  integrity sha1-FXx5wtKLYy1kGEl8V8kxheOS5EQ=
  dependencies:
    "@types/node" "*"
    "@types/range-parser" "*"

"@types/express@*":
  version "4.17.6"
  resolved "http://registry.npm.qima-inc.com/@types/express/download/@types/express-4.17.6.tgz#6bce49e49570507b86ea1b07b806f04697fac45e"
  integrity sha1-a85J5JVwUHuG6hsHuAbwRpf6xF4=
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "*"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/fs-extra@^5.1.0":
  version "5.1.0"
  resolved "http://registry.npm.qima-inc.com/@types/fs-extra/download/@types/fs-extra-5.1.0.tgz#2a325ef97901504a3828718c390d34b8426a10a1"
  integrity sha1-KjJe+XkBUEo4KHGMOQ00uEJqEKE=
  dependencies:
    "@types/node" "*"

"@types/glob@7.1.1":
  version "7.1.1"
  resolved "http://registry.npm.qima-inc.com/@types/glob/download/@types/glob-7.1.1.tgz#aa59a1c6e3fbc421e07ccd31a944c30eba521575"
  integrity sha1-qlmhxuP7xCHgfM0xqUTDDrpSFXU=
  dependencies:
    "@types/events" "*"
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/hapi__joi@*":
  version "16.0.12"
  resolved "http://registry.npm.qima-inc.com/@types/hapi__joi/download/@types/hapi__joi-16.0.12.tgz#fb9113f17cf5764d6b3586ae9817d1606cc7c90c"
  integrity sha1-+5ET8Xz1dk1rNYaumBfRYGzHyQw=

"@types/hapi__joi@^15.0.3":
  version "15.0.4"
  resolved "http://registry.npm.qima-inc.com/@types/hapi__joi/download/@types/hapi__joi-15.0.4.tgz#49e2e1e6da15ade0fdd6db4daf94aecb07bb391b"
  integrity sha1-SeLh5toVreD91ttNr5Suywe7ORs=
  dependencies:
    "@types/hapi__joi" "*"

"@types/http-assert@*":
  version "1.5.1"
  resolved "http://registry.npm.qima-inc.com/@types/http-assert/download/@types/http-assert-1.5.1.tgz#d775e93630c2469c2f980fc27e3143240335db3b"
  integrity sha1-13XpNjDCRpwvmA/CfjFDJAM12zs=

"@types/ioredis@^4.0.10":
  version "4.14.9"
  resolved "http://registry.npm.qima-inc.com/@types/ioredis/download/@types/ioredis-4.14.9.tgz#774387d44d3ad60e1b849044b2b28b96e5813866"
  integrity sha1-d0OH1E061g4bhJBEsrKLluWBOGY=
  dependencies:
    "@types/node" "*"

"@types/ip@^1.1.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@types/ip/download/@types/ip-1.1.0.tgz#aec4f5bfd49e4a4c53b590d88c36eb078827a7c0"
  integrity sha1-rsT1v9SeSkxTtZDYjDbrB4gnp8A=
  dependencies:
    "@types/node" "*"

"@types/json-schema@^7.0.3":
  version "7.0.4"
  resolved "http://registry.npm.qima-inc.com/@types/json-schema/download/@types/json-schema-7.0.4.tgz#38fd73ddfd9b55abb1e1b2ed578cb55bd7b7d339"
  integrity sha1-OP1z3f2bVaux4bLtV4y1W9e30zk=

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "http://registry.npm.qima-inc.com/@types/json5/download/@types/json5-0.0.29.tgz#ee28707ae94e11d2b827bcbe5270bcea7f3e71ee"
  integrity sha1-7ihweulOEdK4J7y+UnC86n8+ce4=

"@types/keygrip@*":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@types/keygrip/download/@types/keygrip-1.0.2.tgz#513abfd256d7ad0bf1ee1873606317b33b1b2a72"
  integrity sha1-UTq/0lbXrQvx7hhzYGMXszsbKnI=

"@types/koa-bodyparser@^4.2.2":
  version "4.3.0"
  resolved "http://registry.npm.qima-inc.com/@types/koa-bodyparser/download/@types/koa-bodyparser-4.3.0.tgz#54ecd662c45f3a4fa9de849528de5fc8ab269ba5"
  integrity sha1-VOzWYsRfOk+p3oSVKN5fyKsmm6U=
  dependencies:
    "@types/koa" "*"

"@types/koa-compose@*":
  version "3.2.5"
  resolved "http://registry.npm.qima-inc.com/@types/koa-compose/download/@types/koa-compose-3.2.5.tgz#85eb2e80ac50be95f37ccf8c407c09bbe3468e9d"
  integrity sha1-hesugKxQvpXzfM+MQHwJu+NGjp0=
  dependencies:
    "@types/koa" "*"

"@types/koa-router@7.0.40":
  version "7.0.40"
  resolved "http://registry.npm.qima-inc.com/@types/koa-router/download/@types/koa-router-7.0.40.tgz#9654dbc43375a0380c44c49c4504b4dbfc3e4e6a"
  integrity sha1-llTbxDN1oDgMRMScRQS02/w+Tmo=
  dependencies:
    "@types/koa" "*"

"@types/koa-router@^7.0.40":
  version "7.4.0"
  resolved "http://registry.npm.qima-inc.com/@types/koa-router/download/@types/koa-router-7.4.0.tgz#4a5f48ca4432281f7429faedd9510c20945939dc"
  integrity sha1-Sl9IykQyKB90Kfrt2VEMIJRZOdw=
  dependencies:
    "@types/koa" "*"

"@types/koa@*", "@types/koa@^2.0.46":
  version "2.11.3"
  resolved "http://registry.npm.qima-inc.com/@types/koa/download/@types/koa-2.11.3.tgz#540ece376581b12beadf9a417dd1731bc31c16ce"
  integrity sha1-VA7ON2WBsSvq35pBfdFzG8McFs4=
  dependencies:
    "@types/accepts" "*"
    "@types/content-disposition" "*"
    "@types/cookies" "*"
    "@types/http-assert" "*"
    "@types/keygrip" "*"
    "@types/koa-compose" "*"
    "@types/node" "*"

"@types/koa@2.0.48":
  version "2.0.48"
  resolved "http://registry.npm.qima-inc.com/@types/koa/download/@types/koa-2.0.48.tgz#29162783029d3e5df8b58c55f6bf0d35f78fc39f"
  integrity sha1-KRYngwKdPl34tYxV9r8NNfePw58=
  dependencies:
    "@types/accepts" "*"
    "@types/cookies" "*"
    "@types/http-assert" "*"
    "@types/keygrip" "*"
    "@types/koa-compose" "*"
    "@types/node" "*"

"@types/lodash@4.14.122":
  version "4.14.122"
  resolved "http://registry.npm.qima-inc.com/@types/lodash/download/@types/lodash-4.14.122.tgz#3e31394c38cf1e5949fb54c1192cbc406f152c6c"
  integrity sha1-PjE5TDjPHllJ+1TBGSy8QG8VLGw=

"@types/lodash@^4.14.123":
  version "4.14.149"
  resolved "http://registry.npm.qima-inc.com/@types/lodash/download/@types/lodash-4.14.149.tgz#1342d63d948c6062838fbf961012f74d4e638440"
  integrity sha1-E0LWPZSMYGKDj7+WEBL3TU5jhEA=

"@types/microtime@^2.1.0":
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/@types/microtime/download/@types/microtime-2.1.0.tgz#adbd99f501a85c88695eb1efd3515810f2563932"
  integrity sha1-rb2Z9QGoXIhpXrHv01FYEPJWOTI=

"@types/mime-types@^2.1.0":
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/@types/mime-types/download/@types/mime-types-2.1.1.tgz#d9ba43490fa3a3df958759adf69396c3532cf2c1"
  integrity sha1-2bpDSQ+jo9+Vh1mt9pOWw1Ms8sE=

"@types/mime@*":
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/@types/mime/download/@types/mime-2.0.1.tgz#dc488842312a7f075149312905b5e3c0b054c79d"
  integrity sha1-3EiIQjEqfwdRSTEpBbXjwLBUx50=

"@types/minimatch@*":
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/@types/minimatch/download/@types/minimatch-3.0.3.tgz#3dca0e3f33b200fc7d1139c0cd96c1268cadfd9d"
  integrity sha1-PcoOPzOyAPx9ETnAzZbBJoyt/Z0=

"@types/node-schedule@1.3.0":
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/@types/node-schedule/download/@types/node-schedule-1.3.0.tgz#100f69078e74d736d59433fc4634ff49d0a9142d"
  integrity sha1-EA9pB4501zbVlDP8RjT/SdCpFC0=
  dependencies:
    "@types/node" "*"

"@types/node@*":
  version "13.11.1"
  resolved "http://registry.npm.qima-inc.com/@types/node/download/@types/node-13.11.1.tgz#49a2a83df9d26daacead30d0ccc8762b128d53c7"
  integrity sha1-SaKoPfnSbarOrTDQzMh2KxKNU8c=

"@types/node@11.9.5":
  version "11.9.5"
  resolved "http://registry.npm.qima-inc.com/@types/node/download/@types/node-11.9.5.tgz#011eece9d3f839a806b63973e228f85967b79ed3"
  integrity sha1-AR7s6dP4OagGtjlz4ij4WWe3ntM=

"@types/nunjucks@^3.0.0":
  version "3.1.3"
  resolved "http://registry.npm.qima-inc.com/@types/nunjucks/download/@types/nunjucks-3.1.3.tgz#55fa2bf6fd34641545a6686217324fde66d31164"
  integrity sha1-Vfor9v00ZBVFpmhiFzJP3mbTEWQ=

"@types/qs@*":
  version "6.9.1"
  resolved "http://registry.npm.qima-inc.com/@types/qs/download/@types/qs-6.9.1.tgz#937fab3194766256ee09fcd40b781740758617e7"
  integrity sha1-k3+rMZR2YlbuCfzUC3gXQHWGF+c=

"@types/range-parser@*":
  version "1.2.3"
  resolved "http://registry.npm.qima-inc.com/@types/range-parser/download/@types/range-parser-1.2.3.tgz#7ee330ba7caafb98090bece86a5ee44115904c2c"
  integrity sha1-fuMwunyq+5gJC+zoal7kQRWQTCw=

"@types/serve-static@*":
  version "1.13.3"
  resolved "http://registry.npm.qima-inc.com/@types/serve-static/download/@types/serve-static-1.13.3.tgz#eb7e1c41c4468272557e897e9171ded5e2ded9d1"
  integrity sha1-634cQcRGgnJVfol+kXHe1eLe2dE=
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/mime" "*"

"@types/url-parse@^1.4.3":
  version "1.4.8"
  resolved "http://registry.npm.qima-inc.com/@types/url-parse/download/@types/url-parse-1.4.8.tgz#c3825047efbca1295b7f1646f38203d9145130d6"
  integrity sha1-w4JQR++8oSlbfxZG84ID2RRRMNY=

"@types/uuid@^8.3.4":
  version "8.3.4"
  resolved "http://registry.npm.qima-inc.com/@types/uuid/download/@types/uuid-8.3.4.tgz#bd86a43617df0594787d38b735f55c805becf1bc"
  integrity sha1-vYakNhffBZR4fTi3NfVcgFvs8bw=

"@typescript-eslint/eslint-plugin@^2.5.0":
  version "2.28.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-2.28.0.tgz#4431bc6d3af41903e5255770703d4e55a0ccbdec"
  integrity sha1-RDG8bTr0GQPlJVdwcD1OVaDMvew=
  dependencies:
    "@typescript-eslint/experimental-utils" "2.28.0"
    functional-red-black-tree "^1.0.1"
    regexpp "^3.0.0"
    tsutils "^3.17.1"

"@typescript-eslint/experimental-utils@2.28.0":
  version "2.28.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/experimental-utils/download/@typescript-eslint/experimental-utils-2.28.0.tgz#1fd0961cd8ef6522687b4c562647da6e71f8833d"
  integrity sha1-H9CWHNjvZSJoe0xWJkfabnH4gz0=
  dependencies:
    "@types/json-schema" "^7.0.3"
    "@typescript-eslint/typescript-estree" "2.28.0"
    eslint-scope "^5.0.0"
    eslint-utils "^2.0.0"

"@typescript-eslint/parser@^2.5.0":
  version "2.28.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/parser/download/@typescript-eslint/parser-2.28.0.tgz#bb761286efd2b0714761cab9d0ee5847cf080385"
  integrity sha1-u3YShu/SsHFHYcq50O5YR88IA4U=
  dependencies:
    "@types/eslint-visitor-keys" "^1.0.0"
    "@typescript-eslint/experimental-utils" "2.28.0"
    "@typescript-eslint/typescript-estree" "2.28.0"
    eslint-visitor-keys "^1.1.0"

"@typescript-eslint/typescript-estree@2.28.0":
  version "2.28.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-2.28.0.tgz#d34949099ff81092c36dc275b6a1ea580729ba00"
  integrity sha1-00lJCZ/4EJLDbcJ1tqHqWAcpugA=
  dependencies:
    debug "^4.1.1"
    eslint-visitor-keys "^1.1.0"
    glob "^7.1.6"
    is-glob "^4.0.1"
    lodash "^4.17.15"
    semver "^6.3.0"
    tsutils "^3.17.1"

"@youzan/apollox@0.1.0":
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/apollox/download/@youzan/apollox-0.1.0.tgz#a257e3e975396f36c1d5535df60617f8c8e64e19"
  integrity sha1-olfj6XU5bzbB1VNd9gYX+MjmThk=
  dependencies:
    debug "4.1.1"
    ip "1.1.5"
    lightning-request "0.2.0"
    properties-parser "0.3.1"

"@youzan/apollox@0.3.4":
  version "0.3.4"
  resolved "http://registry.npm.qima-inc.com/@youzan/apollox/download/@youzan/apollox-0.3.4.tgz#e051071686555bcda0e10dbcb1b5d23fd6d2434d"
  integrity sha1-4FEHFoZVW82g4Q28sbXSP9bSQ00=
  dependencies:
    debug "4.1.1"
    ip "1.1.5"
    lightning-request "0.2.1"
    properties-parser "0.3.1"

"@youzan/marconi-lark-sdk@^1.0.3":
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/@youzan/marconi-lark-sdk/download/@youzan/marconi-lark-sdk-1.0.3.tgz#1c597d1df5e049230076b12ad72224682f42bfd6"
  integrity sha1-HFl9HfXgSSMAdrEq1yIkaC9Cv9Y=
  dependencies:
    "@tql/utils" "^1.1.2"
    "@youzan/utils" "^3.0.20"
    axios "^1.3.4"
    tslib "^2.5.0"

"@youzan/node-qiniu@1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/node-qiniu/download/@youzan/node-qiniu-1.0.2.tgz#5bbb2f752a613cf036a540ee2dbf1b9ad1f7862f"
  integrity sha1-W7svdSphPPA2pUDuLb8bmtH3hi8=
  dependencies:
    agentkeepalive "3.3.0"
    crc32 "0.2.2"
    encodeurl "^1.0.1"
    formstream "1.1.0"
    mime "2.3.1"
    tunnel-agent "0.6.0"
    urllib "2.22.0"

"@youzan/nsq-client@1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/nsq-client/download/@youzan/nsq-client-1.0.0.tgz#0c7bc1006be7bbbe85c423c1555bee2568928d95"
  integrity sha1-DHvBAGvnu76FxCPBVVvuJWiSjZU=
  dependencies:
    axios "^0.19.2"
    bignumber.js "^8.0.2"
    debug "^4.1.1"
    lodash "^4.17.15"
    node-int64 "~0.4.0"
    node-state "~1.4.4"
    p-event "^4.2.0"
  optionalDependencies:
    snappystream "^1.4.0"

"@youzan/runtime@0.6.4":
  version "0.6.4"
  resolved "http://registry.npm.qima-inc.com/@youzan/runtime/download/@youzan/runtime-0.6.4.tgz#35ce194ffa5f89ea590f9d8c7c746e2864fcde21"
  integrity sha1-Nc4ZT/pfiepZD52MfHRuKGT83iE=
  dependencies:
    "@youzan/apollox" "0.3.4"
    "@youzan/youzan-skynet-logger" "0.0.2"
    dnscache "1.0.2"
    graceful-error "1.1.1"
    lightning-request "0.2.0"
    lru-cache "5.1.1"
    tslib "1.10.0"

"@youzan/skynet-log@0.0.6":
  version "0.0.6"
  resolved "http://registry.npm.qima-inc.com/@youzan/skynet-log/download/@youzan/skynet-log-0.0.6.tgz#de5b98fc6fd443152b91b34bb3951cb7e9888bd9"
  integrity sha1-3luY/G/UQxUrkbNLs5Uct+mIi9k=
  dependencies:
    appdata-path "^1.0.0"
    axios "^0.19.0"
    colors-cli "^1.0.21"
    date-fns "^1.30.1"
    minimist "^1.2.0"
    ora "^3.0.0"

"@youzan/uniform-resource@2.0.1":
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/uniform-resource/download/@youzan/uniform-resource-2.0.1.tgz#f86f1ef7a2a9bd21db500580e287bde7eb81a2c7"
  integrity sha1-+G8e96KpvSHbUAWA4oe95+uBosc=
  dependencies:
    "@youzan/apollox" "0.1.0"

"@youzan/utils@2.3.11":
  version "2.3.11"
  resolved "http://registry.npm.qima-inc.com/@youzan/utils/download/@youzan/utils-2.3.11.tgz#20a174b01bc1d8e18e7a5b0a0c33a693215b369a"
  integrity sha1-IKF0sBvB2OGOelsKDDOmkyFbNpo=
  dependencies:
    big.js "^5.2.2"
    compare-versions "^3.4.0"
    exif-js "^2.3.0"
    fecha "^3.0.2"
    lodash "^4.17.11"
    query-string "5"
    raf "^3.4.1"
    tslib "^1.9.3"
    url-parse "^1.4.4"
    zan-jquery "^1.0.2"

"@youzan/utils@^3.0.20":
  version "3.0.20"
  resolved "http://registry.npm.qima-inc.com/@youzan/utils/download/@youzan/utils-3.0.20.tgz#e8ad5f5678ff750dbded8a8a176116edfb9ee403"
  integrity sha1-6K1fVnj/dQ297YqKF2EW7fue5AM=
  dependencies:
    "@types/url-parse" "^1.4.3"
    big.js "^6.0.2"
    exif-js "^2.3.0"
    fecha "^4.2.0"
    query-string "^6.13.7"
    raf "^3.4.1"
    tslib "^2.0.3"
    url-parse "^1.4.7"

"@youzan/youzan-framework@3.4.2":
  version "3.4.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-framework/download/@youzan/youzan-framework-3.4.2.tgz#4a536ac34554d4aaf5387961c4f8c10eb0d70816"
  integrity sha1-SlNqw0VU1Kr1OHlhxPjBDrDXCBY=
  dependencies:
    "@hapi/joi" "15.1.0"
    "@types/cookies" "^0.7.1"
    "@types/debug" "^4.1.4"
    "@types/hapi__joi" "^15.0.3"
    "@types/ioredis" "^4.0.10"
    "@types/ip" "^1.1.0"
    "@types/koa-bodyparser" "^4.2.2"
    "@types/koa-router" "^7.0.40"
    "@types/microtime" "^2.1.0"
    "@types/nunjucks" "^3.0.0"
    "@youzan/node-qiniu" "1.0.2"
    "@youzan/runtime" "0.6.4"
    astroboy "2.2.1"
    chalk "2.4.1"
    debug "3.1.0"
    ioredis "4.3.0"
    ip "1.1.5"
    jsonp-body "1.0.0"
    lightning-request-net "0.2.3"
    lodash "4.17.10"
    microtime "3.0.0"
    nunjucks "3.1.6"
    tslib "^1.10.0"
    utility-types "^3.7.0"
    zan-ajax "2.1.0"

"@youzan/youzan-skynet-logger@0.0.2":
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-skynet-logger/download/@youzan/youzan-skynet-logger-0.0.2.tgz#1cd3f86aaf236038066b973333b1999d30a52e4e"
  integrity sha1-HNP4aq8jYDgGa5czM7GZnTClLk4=
  dependencies:
    debug "4.1.1"
    ip "1.1.5"

a-sync-waterfall@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/a-sync-waterfall/download/a-sync-waterfall-1.0.1.tgz#75b6b6aa72598b497a125e7a2770f14f4c8a1fa7"
  integrity sha1-dba2qnJZi0l6El56J3DxT0yKH6c=

abbrev@1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/abbrev/download/abbrev-1.1.1.tgz#f8f2c887ad10bf67f634f005b6987fed3179aac8"
  integrity sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=

accepts@^1.2.2:
  version "1.3.7"
  resolved "http://registry.npm.qima-inc.com/accepts/download/accepts-1.3.7.tgz#531bc726517a3b2b41f850021c6cc15eaab507cd"
  integrity sha1-UxvHJlF6OytB+FACHGzBXqq1B80=
  dependencies:
    mime-types "~2.1.24"
    negotiator "0.6.2"

acorn-jsx@^5.2.0:
  version "5.2.0"
  resolved "http://registry.npm.qima-inc.com/acorn-jsx/download/acorn-jsx-5.2.0.tgz#4c66069173d6fdd68ed85239fc256226182b2ebe"
  integrity sha1-TGYGkXPW/daO2FI5/CViJhgrLr4=

acorn@^7.1.1:
  version "7.1.1"
  resolved "http://registry.npm.qima-inc.com/acorn/download/acorn-7.1.1.tgz#e35668de0b402f359de515c5482a1ab9f89a69bf"
  integrity sha1-41Zo3gtALzWd5RXFSCoaufiaab8=

address@>=0.0.1:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/address/download/address-1.1.2.tgz#bf1116c9c758c51b7a933d296b72c221ed9428b6"
  integrity sha1-vxEWycdYxRt6kz0pa3LCIe2UKLY=

agentkeepalive@3.3.0:
  version "3.3.0"
  resolved "http://registry.npm.qima-inc.com/agentkeepalive/download/agentkeepalive-3.3.0.tgz#6d5de5829afd3be2712201a39275fd11c651857c"
  integrity sha1-bV3lgpr9O+JxIgGjknX9EcZRhXw=
  dependencies:
    humanize-ms "^1.2.1"

ajv@^6.10.0, ajv@^6.10.2:
  version "6.12.0"
  resolved "http://registry.npm.qima-inc.com/ajv/download/ajv-6.12.0.tgz#06d60b96d87b8454a5adaba86e7854da629db4b7"
  integrity sha1-BtYLlth7hFSlrauobnhU2mKdtLc=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ansi-align@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/ansi-align/download/ansi-align-2.0.0.tgz#c36aeccba563b89ceb556f3690f0b1d9e3547f7f"
  integrity sha1-w2rsy6VjuJzrVW82kPCx2eNUf38=
  dependencies:
    string-width "^2.0.0"

ansi-escapes@^3.0.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/ansi-escapes/download/ansi-escapes-3.2.0.tgz#8780b98ff9dbf5638152d1f1fe5c1d7b4442976b"
  integrity sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=

ansi-escapes@^4.2.1, ansi-escapes@^4.3.0:
  version "4.3.1"
  resolved "http://registry.npm.qima-inc.com/ansi-escapes/download/ansi-escapes-4.3.1.tgz#a5c47cc43181f1f38ffd7076837700d395522a61"
  integrity sha1-pcR8xDGB8fOP/XB2g3cA05VSKmE=
  dependencies:
    type-fest "^0.11.0"

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/ansi-regex/download/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/ansi-regex/download/ansi-regex-3.0.0.tgz#ed0317c322064f79466c02966bddb605ab37d998"
  integrity sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=

ansi-regex@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/ansi-regex/download/ansi-regex-4.1.0.tgz#8b9f8f08cf1acb843756a839ca8c7e3168c51997"
  integrity sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc=

ansi-regex@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/ansi-regex/download/ansi-regex-5.0.0.tgz#388539f55179bf39339c81af30a654d69f87cb75"
  integrity sha1-OIU59VF5vzkznIGvMKZU1p+Hy3U=

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/ansi-regex/download/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/ansi-styles/download/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"
  integrity sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=

ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "http://registry.npm.qima-inc.com/ansi-styles/download/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.2.1"
  resolved "http://registry.npm.qima-inc.com/ansi-styles/download/ansi-styles-4.2.1.tgz#90ae75c424d008d2624c5bf29ead3177ebfcf359"
  integrity sha1-kK51xCTQCNJiTFvynq0xd+v881k=
  dependencies:
    "@types/color-name" "^1.1.1"
    color-convert "^2.0.1"

ansi-term@>=0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/ansi-term/download/ansi-term-0.0.2.tgz#fd753efa4beada0eac99981bc52a3f6ff019deb7"
  integrity sha1-/XU++kvq2g6smZgbxSo/b/AZ3rc=
  dependencies:
    x256 ">=0.0.1"

ansicolors@~0.3.2:
  version "0.3.2"
  resolved "http://registry.npm.qima-inc.com/ansicolors/download/ansicolors-0.3.2.tgz#665597de86a9ffe3aa9bfbe6cae5c6ea426b4979"
  integrity sha1-ZlWX3oap/+Oqm/vmyuXG6kJrSXk=

any-observable@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/any-observable/download/any-observable-0.3.0.tgz#af933475e5806a67d0d7df090dd5e8bef65d119b"
  integrity sha1-r5M0deWAamfQ198JDdXovvZdEZs=

any-promise@^1.0.0, any-promise@^1.1.0, any-promise@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/any-promise/download/any-promise-1.3.0.tgz#abc6afeedcea52e809cdc0376aed3ce39635d17f"
  integrity sha1-q8av7tzqUugJzcA3au0845Y10X8=

anymatch@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/anymatch/download/anymatch-2.0.0.tgz#bcb24b4f37934d9aa7ac17b4adaf89e7c76ef2eb"
  integrity sha1-vLJLTzeTTZqnrBe0ra+J58du8us=
  dependencies:
    micromatch "^3.1.4"
    normalize-path "^2.1.1"

app-root-path@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/app-root-path/download/app-root-path-3.0.0.tgz#210b6f43873227e18a4b810a032283311555d5ad"
  integrity sha1-IQtvQ4cyJ+GKS4EKAyKDMRVV1a0=

appdata-path@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/appdata-path/download/appdata-path-1.0.0.tgz#c4022d0b6727d1ddc1dd7ecec143d4352f3eefad"
  integrity sha1-xAItC2cn0d3B3X7OwUPUNS8+760=

aproba@^1.0.3:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/aproba/download/aproba-1.2.0.tgz#6802e6264efd18c790a1b0d517f0f2627bf2c94a"
  integrity sha1-aALmJk79GMeQobDVF/DyYnvyyUo=

are-we-there-yet@~1.1.2:
  version "1.1.7"
  resolved "http://registry.npm.qima-inc.com/are-we-there-yet/download/are-we-there-yet-1.1.7.tgz#b15474a932adab4ff8a50d9adfa7e4e926f21146"
  integrity sha1-sVR0qTKtq0/4pQ2a36fk6SbyEUY=
  dependencies:
    delegates "^1.0.0"
    readable-stream "^2.0.6"

argparse@^1.0.7:
  version "1.0.10"
  resolved "http://registry.npm.qima-inc.com/argparse/download/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/arr-diff/download/arr-diff-4.0.0.tgz#d6461074febfec71e7e15235761a329a5dc7c520"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/arr-flatten/download/arr-flatten-1.1.0.tgz#36048bbff4e7b47e136644316c99669ea5ae91f1"
  integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=

arr-union@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/arr-union/download/arr-union-3.1.0.tgz#e39b09aea9def866a8f206e288af63919bae39c4"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-union@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/array-union/download/array-union-1.0.2.tgz#9a34410e4f4e3da23dea375be5be70f24778ec39"
  integrity sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=
  dependencies:
    array-uniq "^1.0.1"

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/array-uniq/download/array-uniq-1.0.3.tgz#af6ac877a25cc7f74e058894753858dfdb24fdb6"
  integrity sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "http://registry.npm.qima-inc.com/array-unique/download/array-unique-0.3.2.tgz#a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

arrify@^1.0.0, arrify@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/arrify/download/arrify-1.0.1.tgz#898508da2226f380df904728456849c1501a4b0d"
  integrity sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=

asap@^2.0.3, asap@^2.0.6:
  version "2.0.6"
  resolved "http://registry.npm.qima-inc.com/asap/download/asap-2.0.6.tgz#e50347611d7e690943208bbdafebcbc2fb866d46"
  integrity sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/assign-symbols/download/assign-symbols-1.0.0.tgz#59667f41fadd4f20ccbc2bb96b8d4f7f78ec0367"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

astral-regex@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/astral-regex/download/astral-regex-1.0.0.tgz#6c8c3fb827dd43ee3918f27b82782ab7658a6fd9"
  integrity sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=

astroboy-cli@0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/astroboy-cli/download/astroboy-cli-0.1.1.tgz#858ad2ab770c0c43d1f0881e150f6c5612da3259"
  integrity sha1-hYrSq3cMDEPR8IgeFQ9sVhLaMlk=
  dependencies:
    blessed "^0.1.81"
    blessed-contrib "^4.8.5"
    boxen "^1.3.0"
    chalk "^2.3.2"
    commander "^2.15.1"
    fs-extra "^5.0.0"
    nodemon "^1.17.3"
    opn "^5.3.0"
    ora "^2.0.0"
    shelljs "^0.8.2"
    ts-node "^7.0.0"
    tsconfig-paths "^3.4.2"
    typescript "^2.9.2"

astroboy@2.2.1:
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/astroboy/download/astroboy-2.2.1.tgz#07f3849350f9e362aedb099df5d15165bf9c3c29"
  integrity sha1-B/OEk1D542Ku2wmd9dFRZb+cPCk=
  dependencies:
    "@types/fs-extra" "^5.1.0"
    "@types/koa" "^2.0.46"
    "@types/lodash" "^4.14.123"
    chalk "2.4.0"
    complete-assign "0.0.2"
    fast-glob "2.2.6"
    fs-extra "5.0.0"
    koa "2.5.0"
    koa-body "2.5.0"
    koa-bodyparser "4.2.1"
    koa-compose "4.0.0"
    koa-router "7.4.0"
    koa-static "4.0.2"
    lodash "4.17.11"
    methods "1.1.2"
    path-matching "0.0.2"
    path-to-regexp "2.2.1"
    tslib "1.9.3"
    xss "0.3.7"

async-each@^1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/async-each/download/async-each-1.0.3.tgz#b727dbf87d7651602f06f4d4ac387f47d91b0cbf"
  integrity sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8=

async@^2.6.2:
  version "2.6.4"
  resolved "http://registry.npm.qima-inc.com/async/download/async-2.6.4.tgz#706b7ff6084664cd7eae713f6f965433b5504221"
  integrity sha1-cGt/9ghGZM1+rnE/b5ZUM7VQQiE=
  dependencies:
    lodash "^4.17.14"

async@^3.2.0:
  version "3.2.4"
  resolved "http://registry.npm.qima-inc.com/async/download/async-3.2.4.tgz#2d22e00f8cddeb5fde5dd33522b56d1cf569a81c"
  integrity sha1-LSLgD4zd61/eXdM1IrVtHPVpqBw=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://registry.npm.qima-inc.com/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

atob@^2.1.2:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/atob/download/atob-2.1.2.tgz#6d9517eb9e030d2436666651e86bd9f6f13533c9"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

axios@0.18.0:
  version "0.18.0"
  resolved "http://registry.npm.qima-inc.com/axios/download/axios-0.18.0.tgz#32d53e4851efdc0a11993b6cd000789d70c05102"
  integrity sha1-MtU+SFHv3AoRmTts0AB4nXDAUQI=
  dependencies:
    follow-redirects "^1.3.0"
    is-buffer "^1.1.5"

axios@^0.19.0, axios@^0.19.2:
  version "0.19.2"
  resolved "http://registry.npm.qima-inc.com/axios/download/axios-0.19.2.tgz#3ea36c5d8818d0d5f8a8a97a6d36b86cdc00cb27"
  integrity sha1-PqNsXYgY0NX4qKl6bTa4bNwAyyc=
  dependencies:
    follow-redirects "1.5.10"

axios@^1.3.4:
  version "1.3.6"
  resolved "http://registry.npm.qima-inc.com/axios/download/axios-1.3.6.tgz#1ace9a9fb994314b5f6327960918406fa92c6646"
  integrity sha1-Gs6an7mUMUtfYyeWCRhAb6ksZkY=
  dependencies:
    follow-redirects "^1.15.0"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

babel-code-frame@^6.22.0, babel-code-frame@^6.26.0:
  version "6.26.0"
  resolved "http://registry.npm.qima-inc.com/babel-code-frame/download/babel-code-frame-6.26.0.tgz#63fd43f7dc1e3bb7ce35947db8fe369a3f58c74b"
  integrity sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=
  dependencies:
    chalk "^1.1.3"
    esutils "^2.0.2"
    js-tokens "^3.0.2"

babel-eslint@^7.2.1:
  version "7.2.3"
  resolved "http://registry.npm.qima-inc.com/babel-eslint/download/babel-eslint-7.2.3.tgz#b2fe2d80126470f5c19442dc757253a897710827"
  integrity sha1-sv4tgBJkcPXBlELcdXJTqJdxCCc=
  dependencies:
    babel-code-frame "^6.22.0"
    babel-traverse "^6.23.1"
    babel-types "^6.23.0"
    babylon "^6.17.0"

babel-messages@^6.23.0:
  version "6.23.0"
  resolved "http://registry.npm.qima-inc.com/babel-messages/download/babel-messages-6.23.0.tgz#f3cdf4703858035b2a2951c6ec5edf6c62f2630e"
  integrity sha1-8830cDhYA1sqKVHG7F7fbGLyYw4=
  dependencies:
    babel-runtime "^6.22.0"

babel-runtime@^6.22.0, babel-runtime@^6.26.0:
  version "6.26.0"
  resolved "http://registry.npm.qima-inc.com/babel-runtime/download/babel-runtime-6.26.0.tgz#965c7058668e82b55d7bfe04ff2337bc8b5647fe"
  integrity sha1-llxwWGaOgrVde/4E/yM3vItWR/4=
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

babel-traverse@^6.23.1:
  version "6.26.0"
  resolved "http://registry.npm.qima-inc.com/babel-traverse/download/babel-traverse-6.26.0.tgz#46a9cbd7edcc62c8e5c064e2d2d8d0f4035766ee"
  integrity sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4=
  dependencies:
    babel-code-frame "^6.26.0"
    babel-messages "^6.23.0"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    debug "^2.6.8"
    globals "^9.18.0"
    invariant "^2.2.2"
    lodash "^4.17.4"

babel-types@^6.23.0, babel-types@^6.26.0:
  version "6.26.0"
  resolved "http://registry.npm.qima-inc.com/babel-types/download/babel-types-6.26.0.tgz#a3b073f94ab49eb6fa55cd65227a334380632497"
  integrity sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc=
  dependencies:
    babel-runtime "^6.26.0"
    esutils "^2.0.2"
    lodash "^4.17.4"
    to-fast-properties "^1.0.3"

babylon@^6.17.0, babylon@^6.18.0:
  version "6.18.0"
  resolved "http://registry.npm.qima-inc.com/babylon/download/babylon-6.18.0.tgz#af2f3b88fa6f5c1e4c634d1a0f8eac4f55b395e3"
  integrity sha1-ry87iPpvXB5MY00aD46sT1WzleM=

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/balanced-match/download/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"
  integrity sha1-ibTRmasr7kneFk6gK4nORi1xt2c=

base64-js@^1.0.2:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/base64-js/download/base64-js-1.3.1.tgz#58ece8cb75dd07e71ed08c736abc5fac4dbf8df1"
  integrity sha1-WOzoy3XdB+ce0IxzarxfrE2/jfE=

base@^0.11.1:
  version "0.11.2"
  resolved "http://registry.npm.qima-inc.com/base/download/base-0.11.2.tgz#7bde5ced145b6d551a90db87f83c558b4eb48a8f"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

big.js@^5.2.2:
  version "5.2.2"
  resolved "http://registry.npm.qima-inc.com/big.js/download/big.js-5.2.2.tgz#65f0af382f578bcdc742bd9c281e9cb2d7768328"
  integrity sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=

big.js@^6.0.2:
  version "6.2.1"
  resolved "http://registry.npm.qima-inc.com/big.js/download/big.js-6.2.1.tgz#7205ce763efb17c2e41f26f121c420c6a7c2744f"
  integrity sha1-cgXOdj77F8LkHybxIcQgxqfCdE8=

bignumber.js@^8.0.2:
  version "8.1.1"
  resolved "http://registry.npm.qima-inc.com/bignumber.js/download/bignumber.js-8.1.1.tgz#4b072ae5aea9c20f6730e4e5d529df1271c4d885"
  integrity sha1-Swcq5a6pwg9nMOTl1SnfEnHE2IU=

binary-extensions@^1.0.0:
  version "1.13.1"
  resolved "http://registry.npm.qima-inc.com/binary-extensions/download/binary-extensions-1.13.1.tgz#598afe54755b2868a5330d2aff9d4ebb53209b65"
  integrity sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=

binary@~0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/binary/download/binary-0.3.0.tgz#9f60553bc5ce8c3386f3b553cff47462adecaa79"
  integrity sha1-n2BVO8XOjDOG87VTz/R0Yq3sqnk=
  dependencies:
    buffers "~0.1.1"
    chainsaw "~0.1.0"

bindings@^1.3.0, bindings@^1.3.1, bindings@^1.5.0:
  version "1.5.0"
  resolved "http://registry.npm.qima-inc.com/bindings/download/bindings-1.5.0.tgz#10353c9e945334bc0511a6d90b38fbc7c9c504df"
  integrity sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=
  dependencies:
    file-uri-to-path "1.0.0"

bl@^1.0.0:
  version "1.2.3"
  resolved "http://registry.npm.qima-inc.com/bl/download/bl-1.2.3.tgz#1e8dd80142eac80d7158c9dccc047fb620e035e7"
  integrity sha1-Ho3YAULqyA1xWMnczAR/tiDgNec=
  dependencies:
    readable-stream "^2.3.5"
    safe-buffer "^5.1.1"

bl@^2.2.0:
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/bl/download/bl-2.2.1.tgz#8c11a7b730655c5d56898cdc871224f40fd901d5"
  integrity sha1-jBGntzBlXF1WiYzchxIk9A/ZAdU=
  dependencies:
    readable-stream "^2.3.5"
    safe-buffer "^5.1.1"

blessed-contrib@^4.8.5:
  version "4.8.19"
  resolved "http://registry.npm.qima-inc.com/blessed-contrib/download/blessed-contrib-4.8.19.tgz#95029ad574bfb901366b1b3a58da21c0e4804920"
  integrity sha1-lQKa1XS/uQE2axs6WNohwOSASSA=
  dependencies:
    ansi-term ">=0.0.2"
    chalk "^1.1.0"
    drawille-canvas-blessed-contrib ">=0.1.3"
    lodash "~>=4.17.11"
    map-canvas ">=0.1.5"
    marked "^0.7.0"
    marked-terminal "^4.0.0"
    memory-streams "^0.1.0"
    memorystream "^0.3.1"
    picture-tuber "^1.0.1"
    sparkline "^0.1.1"
    strip-ansi "^3.0.0"
    term-canvas "0.0.5"
    x256 ">=0.0.1"

blessed@^0.1.81:
  version "0.1.81"
  resolved "http://registry.npm.qima-inc.com/blessed/download/blessed-0.1.81.tgz#f962d687ec2c369570ae71af843256e6d0ca1129"
  integrity sha1-+WLWh+wsNpVwrnGvhDJW5tDKESk=

bluebird@^3.5.0:
  version "3.7.2"
  resolved "http://registry.npm.qima-inc.com/bluebird/download/bluebird-3.7.2.tgz#9f229c15be272454ffa973ace0dbee79a1b0c36f"
  integrity sha1-nyKcFb4nJFT/qXOs4NvueaGww28=

boxen@^1.2.1, boxen@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/boxen/download/boxen-1.3.0.tgz#55c6c39a8ba58d9c61ad22cd877532deb665a20b"
  integrity sha1-VcbDmouljZxhrSLNh3Uy3rZlogs=
  dependencies:
    ansi-align "^2.0.0"
    camelcase "^4.0.0"
    chalk "^2.0.1"
    cli-boxes "^1.0.0"
    string-width "^2.0.0"
    term-size "^1.2.0"
    widest-line "^2.0.0"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://registry.npm.qima-inc.com/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^2.3.1, braces@^2.3.2:
  version "2.3.2"
  resolved "http://registry.npm.qima-inc.com/braces/download/braces-2.3.2.tgz#5979fd3f14cd531565e5fa2df1abfff1dfaee729"
  integrity sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@^3.0.1:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/braces/download/braces-3.0.2.tgz#3454e1a462ee8d599e236df336cd9ea4f8afe107"
  integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
  dependencies:
    fill-range "^7.0.1"

bresenham@0.0.3:
  version "0.0.3"
  resolved "http://registry.npm.qima-inc.com/bresenham/download/bresenham-0.0.3.tgz#abdab9e5b194e27c757cd314d8444314f299877a"
  integrity sha1-q9q55bGU4nx1fNMU2ERDFPKZh3o=

buffer-alloc-unsafe@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/buffer-alloc-unsafe/download/buffer-alloc-unsafe-1.1.0.tgz#bd7dc26ae2972d0eda253be061dba992349c19f0"
  integrity sha1-vX3CauKXLQ7aJTvgYdupkjScGfA=

buffer-alloc@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/buffer-alloc/download/buffer-alloc-1.2.0.tgz#890dd90d923a873e08e10e5fd51a57e5b7cce0ec"
  integrity sha1-iQ3ZDZI6hz4I4Q5f1RpX5bfM4Ow=
  dependencies:
    buffer-alloc-unsafe "^1.1.0"
    buffer-fill "^1.0.0"

buffer-crc32@~0.2.5:
  version "0.2.13"
  resolved "http://registry.npm.qima-inc.com/buffer-crc32/download/buffer-crc32-0.2.13.tgz#0d333e3f00eac50aa1454abd30ef8c2a5d9a7242"
  integrity sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=

buffer-fill@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/buffer-fill/download/buffer-fill-1.0.0.tgz#f8f78b76789888ef39f205cd637f68e702122b2c"
  integrity sha1-+PeLdniYiO858gXNY39o5wISKyw=

buffer-from@^1.0.0, buffer-from@^1.1.0:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/buffer-from/download/buffer-from-1.1.1.tgz#32713bc028f75c02fdb710d7c7bcec1f2c6070ef"
  integrity sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8=

buffer@^5.1.0:
  version "5.6.0"
  resolved "http://registry.npm.qima-inc.com/buffer/download/buffer-5.6.0.tgz#a31749dc7d81d84db08abf937b6b8c4033f62786"
  integrity sha1-oxdJ3H2B2E2wir+Te2uMQDP2J4Y=
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"

buffermaker@~1.2.0:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/buffermaker/download/buffermaker-1.2.1.tgz#0631f92b891a84b750f1036491ac857c734429f4"
  integrity sha1-BjH5K4kahLdQ8QNkkayFfHNEKfQ=
  dependencies:
    long "1.1.2"

buffers@~0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/buffers/download/buffers-0.1.1.tgz#b24579c3bed4d6d396aeee6d9a8ae7f5482ab7bb"
  integrity sha1-skV5w77U1tOWru5tmorn9Ugqt7s=

bytes@3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/bytes/download/bytes-3.1.0.tgz#f6cf7933a360e0588fa9fde85651cdc7f805d1f6"
  integrity sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY=

cache-base@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/cache-base/download/cache-base-1.0.1.tgz#0a7f46416831c8b662ee36fe4e7c59d76f666ab2"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

call-bind@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/call-bind/download/call-bind-1.0.2.tgz#b1d4e89e688119c3c9a903ad30abb2f6a919be3c"
  integrity sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

call-me-maybe@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/call-me-maybe/download/call-me-maybe-1.0.1.tgz#26d208ea89e37b5cbde60250a15f031c16a4d66b"
  integrity sha1-JtII6onje1y95gJQoV8DHBak1ms=

caller-callsite@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/caller-callsite/download/caller-callsite-2.0.0.tgz#847e0fce0a223750a9a027c54b33731ad3154134"
  integrity sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=
  dependencies:
    callsites "^2.0.0"

caller-path@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/caller-path/download/caller-path-2.0.0.tgz#468f83044e369ab2010fac5f06ceee15bb2cb1f4"
  integrity sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=
  dependencies:
    caller-callsite "^2.0.0"

callsites@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/callsites/download/callsites-2.0.0.tgz#06eb84f00eea413da86affefacbffb36093b3c50"
  integrity sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=

callsites@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/callsites/download/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camelcase@^2.0.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/camelcase/download/camelcase-2.1.1.tgz#7c1d16d679a1bbe59ca02cacecfb011e201f5a1f"
  integrity sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8=

camelcase@^4.0.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/camelcase/download/camelcase-4.1.0.tgz#d545635be1e33c542649c69173e5de6acfae34dd"
  integrity sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0=

camelcase@^5.0.0:
  version "5.3.1"
  resolved "http://registry.npm.qima-inc.com/camelcase/download/camelcase-5.3.1.tgz#e3c9b31569e106811df242f715725a1f4c494320"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

capture-stack-trace@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/capture-stack-trace/download/capture-stack-trace-1.0.1.tgz#a6c0bbe1f38f3aa0b92238ecb6ff42c344d4135d"
  integrity sha1-psC74fOPOqC5Ijjstv9Cw0TUE10=

cardinal@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/cardinal/download/cardinal-2.1.1.tgz#7cc1055d822d212954d07b085dea251cc7bc5505"
  integrity sha1-fMEFXYItISlU0HsIXeolHMe8VQU=
  dependencies:
    ansicolors "~0.3.2"
    redeyed "~2.1.0"

chainsaw@~0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/chainsaw/download/chainsaw-0.1.0.tgz#5eab50b28afe58074d0d58291388828b5e5fbc98"
  integrity sha1-XqtQsor+WAdNDVgpE4iCi15fvJg=
  dependencies:
    traverse ">=0.3.0 <0.4"

chalk@2.4.0:
  version "2.4.0"
  resolved "http://registry.npm.qima-inc.com/chalk/download/chalk-2.4.0.tgz#a060a297a6b57e15b61ca63ce84995daa0fe6e52"
  integrity sha1-oGCil6a1fhW2HKY86EmV2qD+blI=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@2.4.1:
  version "2.4.1"
  resolved "http://registry.npm.qima-inc.com/chalk/download/chalk-2.4.1.tgz#18c49ab16a037b6eb0152cc83e3471338215b66e"
  integrity sha1-GMSasWoDe26wFSzIPjRxM4IVtm4=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^1.0.0, chalk@^1.1.0, chalk@^1.1.1, chalk@^1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/chalk/download/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  integrity sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.0, chalk@^2.0.1, chalk@^2.1.0, chalk@^2.3.1, chalk@^2.3.2, chalk@^2.4.1, chalk@^2.4.2:
  version "2.4.2"
  resolved "http://registry.npm.qima-inc.com/chalk/download/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/chalk/download/chalk-3.0.0.tgz#3f73c2bf526591f574cc492c51e2456349f844e4"
  integrity sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chardet@^0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.qima-inc.com/chardet/download/chardet-0.7.0.tgz#90094849f0937f2eedc2425d0d28a9e5f0cbad9e"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

charm@~0.1.0:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/charm/download/charm-0.1.2.tgz#06c21eed1a1b06aeb67553cdc53e23274bac2296"
  integrity sha1-BsIe7RobBq62dVPNxT4jJ0usIpY=

chokidar@^2.0.0, chokidar@^2.1.8:
  version "2.1.8"
  resolved "http://registry.npm.qima-inc.com/chokidar/download/chokidar-2.1.8.tgz#804b3a7b6a99358c3c5c61e71d8728f041cff917"
  integrity sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc=
  dependencies:
    anymatch "^2.0.0"
    async-each "^1.0.1"
    braces "^2.3.2"
    glob-parent "^3.1.0"
    inherits "^2.0.3"
    is-binary-path "^1.0.0"
    is-glob "^4.0.0"
    normalize-path "^3.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.2.1"
    upath "^1.1.1"
  optionalDependencies:
    fsevents "^1.2.7"

chownr@^1.0.1:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/chownr/download/chownr-1.1.4.tgz#6fc9d7b42d32a583596337666e7d08084da2cc6b"
  integrity sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=

ci-info@^1.5.0:
  version "1.6.0"
  resolved "http://registry.npm.qima-inc.com/ci-info/download/ci-info-1.6.0.tgz#2ca20dbb9ceb32d4524a683303313f0304b1e497"
  integrity sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc=

ci-info@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/ci-info/download/ci-info-2.0.0.tgz#67a9e964be31a51e15e5010d58e6f12834002f46"
  integrity sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y=

class-utils@^0.3.5:
  version "0.3.6"
  resolved "http://registry.npm.qima-inc.com/class-utils/download/class-utils-0.3.6.tgz#f93369ae8b9a7ce02fd41faad0ca83033190c463"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

cli-boxes@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/cli-boxes/download/cli-boxes-1.0.0.tgz#4fa917c3e59c94a004cd61f8ee509da651687143"
  integrity sha1-T6kXw+WclKAEzWH47lCdplFocUM=

cli-cursor@^2.0.0, cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/cli-cursor/download/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/cli-cursor/download/cli-cursor-3.1.0.tgz#264305a7ae490d1d03bf0c9ba7c925d1753af307"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-highlight@^2.0.0:
  version "2.1.4"
  resolved "http://registry.npm.qima-inc.com/cli-highlight/download/cli-highlight-2.1.4.tgz#098cb642cf17f42adc1c1145e07f960ec4d7522b"
  integrity sha1-CYy2Qs8X9CrcHBFF4H+WDsTXUis=
  dependencies:
    chalk "^3.0.0"
    highlight.js "^9.6.0"
    mz "^2.4.0"
    parse5 "^5.1.1"
    parse5-htmlparser2-tree-adapter "^5.1.1"
    yargs "^15.0.0"

cli-spinners@^1.1.0:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/cli-spinners/download/cli-spinners-1.3.1.tgz#002c1990912d0d59580c93bd36c056de99e4259a"
  integrity sha1-ACwZkJEtDVlYDJO9NsBW3pnkJZo=

cli-spinners@^2.0.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/cli-spinners/download/cli-spinners-2.3.0.tgz#0632239a4b5aa4c958610142c34bb7a651fc8df5"
  integrity sha1-BjIjmktapMlYYQFCw0u3plH8jfU=

cli-table@^0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/cli-table/download/cli-table-0.3.1.tgz#f53b05266a8b1a0b934b3d0821e6e2dc5914ae23"
  integrity sha1-9TsFJmqLGguTSz0IIebi3FkUriM=
  dependencies:
    colors "1.0.3"

cli-truncate@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/cli-truncate/download/cli-truncate-0.2.1.tgz#9f15cfbb0705005369216c626ac7d05ab90dd574"
  integrity sha1-nxXPuwcFAFNpIWxiasfQWrkN1XQ=
  dependencies:
    slice-ansi "0.0.4"
    string-width "^1.0.1"

cli-width@^2.0.0:
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/cli-width/download/cli-width-2.2.1.tgz#b0433d0b4e9c847ef18868a4ef16fd5fc8271c48"
  integrity sha1-sEM9C06chH7xiGik7xb9X8gnHEg=

cliui@^3.0.3:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/cliui/download/cliui-3.2.0.tgz#120601537a916d29940f934da3b48d585a39213d"
  integrity sha1-EgYBU3qRbSmUD5NNo7SNWFo5IT0=
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wrap-ansi "^2.0.0"

cliui@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/cliui/download/cliui-5.0.0.tgz#deefcfdb2e800784aa34f46fa08e06851c7bbbc5"
  integrity sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=
  dependencies:
    string-width "^3.1.0"
    strip-ansi "^5.2.0"
    wrap-ansi "^5.1.0"

cliui@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/cliui/download/cliui-6.0.0.tgz#511d702c0c4e41ca156d7d0e96021f23e13225b1"
  integrity sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^6.2.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/clone/download/clone-1.0.4.tgz#da309cc263df15994c688ca902179ca3c7cd7c7e"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

cls-bluebird@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/cls-bluebird/download/cls-bluebird-2.1.0.tgz#37ef1e080a8ffb55c2f4164f536f1919e7968aee"
  integrity sha1-N+8eCAqP+1XC9BZPU28ZGeeWiu4=
  dependencies:
    is-bluebird "^1.0.2"
    shimmer "^1.1.0"

cluster-key-slot@^1.0.6:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/cluster-key-slot/download/cluster-key-slot-1.1.0.tgz#30474b2a981fb12172695833052bc0d01336d10d"
  integrity sha1-MEdLKpgfsSFyaVgzBSvA0BM20Q0=

co-body@^5.1.1:
  version "5.2.0"
  resolved "http://registry.npm.qima-inc.com/co-body/download/co-body-5.2.0.tgz#5a0a658c46029131e0e3a306f67647302f71c124"
  integrity sha1-WgpljEYCkTHg46MG9nZHMC9xwSQ=
  dependencies:
    inflation "^2.0.0"
    qs "^6.4.0"
    raw-body "^2.2.0"
    type-is "^1.6.14"

co-body@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/co-body/download/co-body-6.0.0.tgz#965b9337d7f5655480787471f4237664820827e3"
  integrity sha1-lluTN9f1ZVSAeHRx9CN2ZIIIJ+M=
  dependencies:
    inflation "^2.0.0"
    qs "^6.5.2"
    raw-body "^2.3.3"
    type-is "^1.6.16"

co@^4.6.0:
  version "4.6.0"
  resolved "http://registry.npm.qima-inc.com/co/download/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/code-point-at/download/code-point-at-1.1.0.tgz#0d070b4d043a5bea33a2f1a40e2edb3d9a4ccf77"
  integrity sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/collection-visit/download/collection-visit-1.0.0.tgz#4bc0373c164bc3291b4d368c829cf1a80a59dca0"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.9.0:
  version "1.9.3"
  resolved "http://registry.npm.qima-inc.com/color-convert/download/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@~1.1.4:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

colors-cli@^1.0.21:
  version "1.0.27"
  resolved "http://registry.npm.qima-inc.com/colors-cli/download/colors-cli-1.0.27.tgz#1a33f6cfc013cdb55e50bac0bff1a8f5f0866cf8"
  integrity sha1-GjP2z8ATzbVeULrAv/Go9fCGbPg=

colors@1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/colors/download/colors-1.0.3.tgz#0433f44d809680fdeb60ed260f1b0c262e82a40b"
  integrity sha1-BDP0TYCWgP3rYO0mDxsMJi6CpAs=

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "http://registry.npm.qima-inc.com/combined-stream/download/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@^2.14.1, commander@^2.15.1, commander@^2.9.0:
  version "2.20.3"
  resolved "http://registry.npm.qima-inc.com/commander/download/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

compare-versions@^3.4.0:
  version "3.6.0"
  resolved "http://registry.npm.qima-inc.com/compare-versions/download/compare-versions-3.6.0.tgz#1a5689913685e5a87637b8d3ffca75514ec41d62"
  integrity sha1-GlaJkTaF5ah2N7jT/8p1UU7EHWI=

complete-assign@0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/complete-assign/download/complete-assign-0.0.2.tgz#e35d14be0e9f087f050b2b72c8e87cd7f91691e5"
  integrity sha1-410Uvg6fCH8FCytyyOh81/kWkeU=

component-emitter@^1.2.1:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/component-emitter/download/component-emitter-1.3.0.tgz#16e4070fba8ae29b679f2215853ee181ab2eabc0"
  integrity sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.qima-inc.com/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

configstore@^3.0.0:
  version "3.1.2"
  resolved "http://registry.npm.qima-inc.com/configstore/download/configstore-3.1.2.tgz#c6f25defaeef26df12dd33414b001fe81a543f8f"
  integrity sha1-xvJd767vJt8S3TNBSwAf6BpUP48=
  dependencies:
    dot-prop "^4.1.0"
    graceful-fs "^4.1.2"
    make-dir "^1.0.0"
    unique-string "^1.0.0"
    write-file-atomic "^2.0.0"
    xdg-basedir "^3.0.0"

console-control-strings@^1.0.0, console-control-strings@~1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/console-control-strings/download/console-control-strings-1.1.0.tgz#3d7cf4464db6446ea644bf4b39507f9851008e8e"
  integrity sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=

content-disposition@~0.5.0:
  version "0.5.3"
  resolved "http://registry.npm.qima-inc.com/content-disposition/download/content-disposition-0.5.3.tgz#e130caf7e7279087c5616c2007d0485698984fbd"
  integrity sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70=
  dependencies:
    safe-buffer "5.1.2"

content-type@^1.0.0, content-type@^1.0.2:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/content-type/download/content-type-1.0.4.tgz#e138cc75e040c727b1966fe5e5f8c9aee256fe3b"
  integrity sha1-4TjMdeBAxyexlm/l5fjJruJW/js=

cookies@~0.7.0:
  version "0.7.3"
  resolved "http://registry.npm.qima-inc.com/cookies/download/cookies-0.7.3.tgz#7912ce21fbf2e8c2da70cf1c3f351aecf59dadfa"
  integrity sha1-eRLOIfvy6MLacM8cPzUa7PWdrfo=
  dependencies:
    depd "~1.1.2"
    keygrip "~1.0.3"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/copy-descriptor/download/copy-descriptor-0.1.1.tgz#676f6eb3c39997c2ee1ac3a924fd6124748f578d"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

copy-to@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/copy-to/download/copy-to-2.0.1.tgz#2680fbb8068a48d08656b6098092bdafc906f4a5"
  integrity sha1-JoD7uAaKSNCGVrYJgJK9r8kG9KU=

core-js-pure@^3.0.0:
  version "3.6.5"
  resolved "http://registry.npm.qima-inc.com/core-js-pure/download/core-js-pure-3.6.5.tgz#c79e75f5e38dbc85a662d91eea52b8256d53b813"
  integrity sha1-x5519eONvIWmYtke6lK4JW1TuBM=

core-js@^2.4.0:
  version "2.6.11"
  resolved "http://registry.npm.qima-inc.com/core-js/download/core-js-2.6.11.tgz#38831469f9922bded8ee21c9dc46985e0399308c"
  integrity sha1-OIMUafmSK97Y7iHJ3EaYXgOZMIw=

core-util-is@~1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/core-util-is/download/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

cosmiconfig@^5.0.7, cosmiconfig@^5.2.0:
  version "5.2.1"
  resolved "http://registry.npm.qima-inc.com/cosmiconfig/download/cosmiconfig-5.2.1.tgz#040f726809c591e77a17c0a3626ca45b4f168b1a"
  integrity sha1-BA9yaAnFked6F8CjYmykW08Wixo=
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.13.1"
    parse-json "^4.0.0"

crc32@0.2.2:
  version "0.2.2"
  resolved "http://registry.npm.qima-inc.com/crc32/download/crc32-0.2.2.tgz#7ad220d6ffdcd119f9fc127a7772cacea390a4ba"
  integrity sha1-etIg1v/c0Rn5/BJ6d3LKzqOQpLo=

create-error-class@^3.0.0:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/create-error-class/download/create-error-class-3.0.2.tgz#06be7abef947a3f14a30fd610671d401bca8b7b6"
  integrity sha1-Br56vvlHo/FKMP1hBnHUAbyot7Y=
  dependencies:
    capture-stack-trace "^1.0.0"

cron-parser@^2.7.3:
  version "2.13.0"
  resolved "http://registry.npm.qima-inc.com/cron-parser/download/cron-parser-2.13.0.tgz#6f930bb6f2931790d2a9eec83b3ec276e27a6725"
  integrity sha1-b5MLtvKTF5DSqe7IOz7CduJ6ZyU=
  dependencies:
    is-nan "^1.2.1"
    moment-timezone "^0.5.25"

cross-spawn@^5.0.1:
  version "5.1.0"
  resolved "http://registry.npm.qima-inc.com/cross-spawn/download/cross-spawn-5.1.0.tgz#e8bd0efee58fcff6f8f94510a0a554bbfa235449"
  integrity sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^6.0.0, cross-spawn@^6.0.5:
  version "6.0.5"
  resolved "http://registry.npm.qima-inc.com/cross-spawn/download/cross-spawn-6.0.5.tgz#4a5ec7c64dfae22c3a14124dbacdee846d80cbc4"
  integrity sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

crypto-random-string@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/crypto-random-string/download/crypto-random-string-1.0.0.tgz#a230f64f568310e1498009940790ec99545bca7e"
  integrity sha1-ojD2T1aDEOFJgAmUB5DsmVRbyn4=

cssfilter@0.0.10:
  version "0.0.10"
  resolved "http://registry.npm.qima-inc.com/cssfilter/download/cssfilter-0.0.10.tgz#c6d2672632a2e5c83e013e6864a42ce8defd20ae"
  integrity sha1-xtJnJjKi5cg+AT5oZKQs6N79IK4=

date-fns@^1.27.2, date-fns@^1.29.0, date-fns@^1.30.1:
  version "1.30.1"
  resolved "http://registry.npm.qima-inc.com/date-fns/download/date-fns-1.30.1.tgz#2e71bf0b119153dbb4cc4e88d9ea5acfb50dc05c"
  integrity sha1-LnG/CxGRU9u0zE6I2epaz7UNwFw=

debug@*, debug@4.1.1, debug@^4.0.1, debug@^4.1.1:
  version "4.1.1"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-4.1.1.tgz#3b72260255109c6b589cee050f1d516139664791"
  integrity sha1-O3ImAlUQnGtYnO4FDx1RYTlmR5E=
  dependencies:
    ms "^2.1.1"

debug@3.1.0, debug@=3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-3.1.0.tgz#5bb5a0672628b64149566ba16819e61518c67261"
  integrity sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=
  dependencies:
    ms "2.0.0"

debug@^2.1.3, debug@^2.2.0, debug@^2.3.3, debug@^2.6.0, debug@^2.6.3, debug@^2.6.8:
  version "2.6.9"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^3.0.0, debug@^3.1.0, debug@^3.2.6:
  version "3.2.6"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-3.2.6.tgz#e83d17de16d8a7efb7717edbe5fb10135eee629b"
  integrity sha1-6D0X3hbYp++3cX7b5fsQE17uYps=
  dependencies:
    ms "^2.1.1"

decamelize@^1.1.1, decamelize@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/decamelize/download/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decamelize@^3.2.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/decamelize/download/decamelize-3.2.0.tgz#84b8e8f4f8c579f938e35e2cc7024907e0090851"
  integrity sha1-hLjo9PjFefk4414sxwJJB+AJCFE=
  dependencies:
    xregexp "^4.2.4"

decode-uri-component@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.qima-inc.com/decode-uri-component/download/decode-uri-component-0.2.0.tgz#eb3913333458775cb84cd1a1fae062106bb87545"
  integrity sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=

decompress-response@^3.3.0:
  version "3.3.0"
  resolved "http://registry.npm.qima-inc.com/decompress-response/download/decompress-response-3.3.0.tgz#80a4dd323748384bfa248083622aedec982adff3"
  integrity sha1-gKTdMjdIOEv6JICDYirt7Jgq3/M=
  dependencies:
    mimic-response "^1.0.0"

dedent@^0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.qima-inc.com/dedent/download/dedent-0.7.0.tgz#2495ddbaf6eb874abb0e1be9df22d2e5a544326c"
  integrity sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=

deep-equal@~1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/deep-equal/download/deep-equal-1.0.1.tgz#f5d260292b660e084eff4cdbc9f08ad3247448b5"
  integrity sha1-9dJgKStmDghO/0zbyfCK0yR0SLU=

deep-extend@^0.6.0:
  version "0.6.0"
  resolved "http://registry.npm.qima-inc.com/deep-extend/download/deep-extend-0.6.0.tgz#c4fa7c95404a17a9c3e8ca7e1537312b736330ac"
  integrity sha1-xPp8lUBKF6nD6Mp+FTcxK3NjMKw=

deep-is@~0.1.3:
  version "0.1.3"
  resolved "http://registry.npm.qima-inc.com/deep-is/download/deep-is-0.1.3.tgz#b369d6fb5dbc13eecf524f91b070feedc357cf34"
  integrity sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=

default-user-agent@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/default-user-agent/download/default-user-agent-1.0.0.tgz#16c46efdcaba3edc45f24f2bd4868b01b7c2adc6"
  integrity sha1-FsRu/cq6PtxF8k8r1IaLAbfCrcY=
  dependencies:
    os-name "~1.0.3"

defaults@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/defaults/download/defaults-1.0.3.tgz#c656051e9817d9ff08ed881477f3fe4019f3ef7d"
  integrity sha1-xlYFHpgX2f8I7YgUd/P+QBnz730=
  dependencies:
    clone "^1.0.2"

define-properties@^1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/define-properties/download/define-properties-1.1.3.tgz#cf88da6cbee26fe6db7094f61d870cbd84cee9f1"
  integrity sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=
  dependencies:
    object-keys "^1.0.12"

define-property@^0.2.5:
  version "0.2.5"
  resolved "http://registry.npm.qima-inc.com/define-property/download/define-property-0.2.5.tgz#c35b1ef918ec3c990f9a5bc57be04aacec5c8116"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/define-property/download/define-property-1.0.0.tgz#769ebaaf3f4a63aad3af9e8d304c9bbe79bfb0e6"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/define-property/download/define-property-2.0.2.tgz#d459689e8d654ba77e02a817f8710d702cb16e9d"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

del@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/del/download/del-3.0.0.tgz#53ecf699ffcbcb39637691ab13baf160819766e5"
  integrity sha1-U+z2mf/LyzljdpGrE7rxYIGXZuU=
  dependencies:
    globby "^6.1.0"
    is-path-cwd "^1.0.0"
    is-path-in-cwd "^1.0.0"
    p-map "^1.1.1"
    pify "^3.0.0"
    rimraf "^2.2.8"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

delegates@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/delegates/download/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a"
  integrity sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=

denque@^1.1.0, denque@^1.4.1:
  version "1.4.1"
  resolved "http://registry.npm.qima-inc.com/denque/download/denque-1.4.1.tgz#6744ff7641c148c3f8a69c307e51235c1f4a37cf"
  integrity sha1-Z0T/dkHBSMP4ppwwflEjXB9KN88=

denque@^1.3.0:
  version "1.5.1"
  resolved "http://registry.npm.qima-inc.com/denque/download/denque-1.5.1.tgz#07f670e29c9a78f8faecb2566a1e2c11929c5cbf"
  integrity sha1-B/Zw4pyaePj67LJWah4sEZKcXL8=

depd@^1.1.0, depd@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/depd/download/depd-1.1.2.tgz#9bcd52e14c097763e749b274c4346ed2e560b5a9"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

destroy@^1.0.3, destroy@^1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/destroy/download/destroy-1.0.4.tgz#978857442c44749e4206613e37946205826abd80"
  integrity sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=

detect-libc@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/detect-libc/download/detect-libc-1.0.3.tgz#fa137c4bd698edf55cd5cd02ac559f91a4c4ba9b"
  integrity sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=

diff@^3.1.0:
  version "3.5.0"
  resolved "http://registry.npm.qima-inc.com/diff/download/diff-3.5.0.tgz#800c0dd1e0a8bfbc95835c202ad220fe317e5a12"
  integrity sha1-gAwN0eCov7yVg1wgKtIg/jF+WhI=

digest-header@^0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.qima-inc.com/digest-header/download/digest-header-0.0.1.tgz#11ccf6deec5766ac379744d901c12cba49514be6"
  integrity sha1-Ecz23uxXZqw3l0TZAcEsuklRS+Y=
  dependencies:
    utility "0.1.11"

dnscache@1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/dnscache/download/dnscache-1.0.2.tgz#fd3c24d66c141625f594c77be7a8dafee2a66c8a"
  integrity sha1-/Twk1mwUFiX1lMd756ja/uKmbIo=
  dependencies:
    asap "^2.0.6"
    lodash.clone "^4.5.0"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/doctrine/download/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dot-prop@^4.1.0:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/dot-prop/download/dot-prop-4.2.0.tgz#1f19e0c2e1aa0e32797c49799f2837ac6af69c57"
  integrity sha1-HxngwuGqDjJ5fEl5nyg3rGr2nFc=
  dependencies:
    is-obj "^1.0.0"

dotenv@^6.2.0:
  version "6.2.0"
  resolved "http://registry.npm.qima-inc.com/dotenv/download/dotenv-6.2.0.tgz#941c0410535d942c8becf28d3f357dbd9d476064"
  integrity sha1-lBwEEFNdlCyL7PKNPzV9vZ1HYGQ=

dottie@^2.0.0:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/dottie/download/dottie-2.0.2.tgz#cc91c0726ce3a054ebf11c55fbc92a7f266dd154"
  integrity sha1-zJHAcmzjoFTr8RxV+8kqfyZt0VQ=

drawille-blessed-contrib@>=0.0.1:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/drawille-blessed-contrib/download/drawille-blessed-contrib-1.0.0.tgz#15c27934f57a0056ad13596e1561637bc941f0b7"
  integrity sha1-FcJ5NPV6AFatE1luFWFje8lB8Lc=

drawille-canvas-blessed-contrib@>=0.0.1, drawille-canvas-blessed-contrib@>=0.1.3:
  version "0.1.3"
  resolved "http://registry.npm.qima-inc.com/drawille-canvas-blessed-contrib/download/drawille-canvas-blessed-contrib-0.1.3.tgz#212f078a722bfd2ecc267ea86ab6dddc1081fd48"
  integrity sha1-IS8HinIr/S7MJn6oarbd3BCB/Ug=
  dependencies:
    ansi-term ">=0.0.2"
    bresenham "0.0.3"
    drawille-blessed-contrib ">=0.0.1"
    gl-matrix "^2.1.0"
    x256 ">=0.0.1"

duplexer3@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/duplexer3/download/duplexer3-0.1.4.tgz#ee01dd1cac0ed3cbc7fdbea37dc0a8f1ce002ce2"
  integrity sha1-7gHdHKwO08vH/b6jfcCo8c4ALOI=

ee-first@1.1.1, ee-first@~1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/ee-first/download/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

elegant-spinner@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/elegant-spinner/download/elegant-spinner-1.0.1.tgz#db043521c95d7e303fd8f345bedc3349cfb0729e"
  integrity sha1-2wQ1IcldfjA/2PNFvtwzSc+wcp4=

emoji-regex@^7.0.1:
  version "7.0.3"
  resolved "http://registry.npm.qima-inc.com/emoji-regex/download/emoji-regex-7.0.3.tgz#933a04052860c85e83c122479c4748a8e4c72156"
  integrity sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://registry.npm.qima-inc.com/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

encodeurl@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/encodeurl/download/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

end-of-stream@^1.0.0, end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "http://registry.npm.qima-inc.com/end-of-stream/download/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://registry.npm.qima-inc.com/error-ex/download/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

error-inject@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/error-inject/download/error-inject-1.0.0.tgz#e2b3d91b54aed672f309d950d154850fa11d4f37"
  integrity sha1-4rPZG1Su1nLzCdlQ0VSFD6EdTzc=

escape-html@~1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/escape-html/download/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.4, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

eslint-config-google@^0.9.1:
  version "0.9.1"
  resolved "http://registry.npm.qima-inc.com/eslint-config-google/download/eslint-config-google-0.9.1.tgz#83353c3dba05f72bb123169a4094f4ff120391eb"
  integrity sha1-gzU8PboF9yuxIxaaQJT0/xIDkes=

eslint-config-prettier@^2.10.0:
  version "2.10.0"
  resolved "http://registry.npm.qima-inc.com/eslint-config-prettier/download/eslint-config-prettier-2.10.0.tgz#ec07bc1d01f87d09f61d3840d112dc8a9791e30b"
  integrity sha1-7Ae8HQH4fQn2HThA0RLcipeR4ws=
  dependencies:
    get-stdin "^5.0.1"

eslint-plugin-lean-imports@^0.3.3:
  version "0.3.3"
  resolved "http://registry.npm.qima-inc.com/eslint-plugin-lean-imports/download/eslint-plugin-lean-imports-0.3.3.tgz#acb835d23801d92d2ce119b752b97c953f96d865"
  integrity sha1-rLg10jgB2S0s4Rm3Url8lT+W2GU=

eslint-plugin-prettier@^2.6.2:
  version "2.7.0"
  resolved "http://registry.npm.qima-inc.com/eslint-plugin-prettier/download/eslint-plugin-prettier-2.7.0.tgz#b4312dcf2c1d965379d7f9d5b5f8aaadc6a45904"
  integrity sha1-tDEtzywdllN51/nVtfiqrcakWQQ=
  dependencies:
    fast-diff "^1.1.1"
    jest-docblock "^21.0.0"

eslint-scope@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/eslint-scope/download/eslint-scope-5.0.0.tgz#e87c8887c73e8d1ec84f1ca591645c358bfc8fb9"
  integrity sha1-6HyIh8c+jR7ITxylkWRcNYv8j7k=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-utils@^1.4.3:
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/eslint-utils/download/eslint-utils-1.4.3.tgz#74fec7c54d0776b6f67e0251040b5806564e981f"
  integrity sha1-dP7HxU0Hdrb2fgJRBAtYBlZOmB8=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-utils@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/eslint-utils/download/eslint-utils-2.0.0.tgz#7be1cc70f27a72a76cd14aa698bcabed6890e1cd"
  integrity sha1-e+HMcPJ6cqds0UqmmLyr7WiQ4c0=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-visitor-keys@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/eslint-visitor-keys/download/eslint-visitor-keys-1.1.0.tgz#e2a82cea84ff246ad6fb57f9bde5b46621459ec2"
  integrity sha1-4qgs6oT/JGrW+1f5veW0ZiFFnsI=

eslint@^6.5.1:
  version "6.8.0"
  resolved "http://registry.npm.qima-inc.com/eslint/download/eslint-6.8.0.tgz#62262d6729739f9275723824302fb227c8c93ffb"
  integrity sha1-YiYtZylzn5J1cjgkMC+yJ8jJP/s=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    ajv "^6.10.0"
    chalk "^2.1.0"
    cross-spawn "^6.0.5"
    debug "^4.0.1"
    doctrine "^3.0.0"
    eslint-scope "^5.0.0"
    eslint-utils "^1.4.3"
    eslint-visitor-keys "^1.1.0"
    espree "^6.1.2"
    esquery "^1.0.1"
    esutils "^2.0.2"
    file-entry-cache "^5.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^5.0.0"
    globals "^12.1.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    inquirer "^7.0.0"
    is-glob "^4.0.0"
    js-yaml "^3.13.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.3.0"
    lodash "^4.17.14"
    minimatch "^3.0.4"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    optionator "^0.8.3"
    progress "^2.0.0"
    regexpp "^2.0.1"
    semver "^6.1.2"
    strip-ansi "^5.2.0"
    strip-json-comments "^3.0.1"
    table "^5.2.3"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^6.1.2:
  version "6.2.1"
  resolved "http://registry.npm.qima-inc.com/espree/download/espree-6.2.1.tgz#77fc72e1fd744a2052c20f38a5b575832e82734a"
  integrity sha1-d/xy4f10SiBSwg84pbV1gy6Cc0o=
  dependencies:
    acorn "^7.1.1"
    acorn-jsx "^5.2.0"
    eslint-visitor-keys "^1.1.0"

esprima@^4.0.0, esprima@~4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/esprima/download/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.0.1:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/esquery/download/esquery-1.3.0.tgz#e5e29a6f66a837840d34f68cb9ce355260d1128b"
  integrity sha1-5eKab2aoN4QNNPaMuc41UmDREos=
  dependencies:
    estraverse "^5.0.0"

esrecurse@^4.1.0:
  version "4.2.1"
  resolved "http://registry.npm.qima-inc.com/esrecurse/download/esrecurse-4.2.1.tgz#007a3b9fdbc2b3bb87e4879ea19c92fdbd3942cf"
  integrity sha1-AHo7n9vCs7uH5IeeoZyS/b05Qs8=
  dependencies:
    estraverse "^4.1.0"

estraverse@^4.1.0, estraverse@^4.1.1:
  version "4.3.0"
  resolved "http://registry.npm.qima-inc.com/estraverse/download/estraverse-4.3.0.tgz#398ad3f3c5a24948be7725e83d11a7de28cdbd1d"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/estraverse/download/estraverse-5.0.0.tgz#ac81750b482c11cca26e4b07e83ed8f75fbcdc22"
  integrity sha1-rIF1C0gsEcyibksH6D7Y91+83CI=

esutils@^2.0.2:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/esutils/download/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

event-stream@~0.9.8:
  version "0.9.8"
  resolved "http://registry.npm.qima-inc.com/event-stream/download/event-stream-0.9.8.tgz#5da9cf3c7900975989db5a68c28e5b3c98ebe03a"
  integrity sha1-XanPPHkAl1mJ21powo5bPJjr4Do=
  dependencies:
    optimist "0.2"

execa@^0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.qima-inc.com/execa/download/execa-0.7.0.tgz#944becd34cc41ee32a63a9faf27ad5a65fc59777"
  integrity sha1-lEvs00zEHuMqY6n68nrVpl/Fl3c=
  dependencies:
    cross-spawn "^5.0.1"
    get-stream "^3.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/execa/download/execa-1.0.0.tgz#c6236a5bb4df6d6f15e88e7f017798216749ddd8"
  integrity sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

exif-js@^2.3.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/exif-js/download/exif-js-2.3.0.tgz#9d10819bf571f873813e7640241255ab9ce1a814"
  integrity sha1-nRCBm/Vx+HOBPnZAJBJVq5zhqBQ=

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "http://registry.npm.qima-inc.com/expand-brackets/download/expand-brackets-2.1.4.tgz#b77735e315ce30f6b6eff0f83b04151a22449622"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

expand-template@^2.0.3:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/expand-template/download/expand-template-2.0.3.tgz#6e14b3fcee0f3a6340ecb57d2e8918692052a47c"
  integrity sha1-bhSz/O4POmNA7LV9LokYaSBSpHw=

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/extend-shallow/download/extend-shallow-2.0.1.tgz#51af7d614ad9a9f610ea1bafbb989d6b1c56890f"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/extend-shallow/download/extend-shallow-3.0.2.tgz#26a71aaf073b39fb2127172746131c2704028db8"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

external-editor@^3.0.3:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/external-editor/download/external-editor-3.1.0.tgz#cb03f740befae03ea4d283caed2741a83f335495"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extglob@^2.0.4:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/extglob/download/extglob-2.0.4.tgz#ad00fe4dc612a9232e8718711dc5cb5ab0285543"
  integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

fast-crc32c@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/fast-crc32c/download/fast-crc32c-2.0.0.tgz#1f7365ec5b47ec23bdfe15c99d13288c9285c6cb"
  integrity sha1-H3Nl7FtH7CO9/hXJnRMojJKFxss=
  optionalDependencies:
    sse4_crc32 "^6.0.1"

fast-deep-equal@^3.1.1:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/fast-deep-equal/download/fast-deep-equal-3.1.1.tgz#545145077c501491e33b15ec408c294376e94ae4"
  integrity sha1-VFFFB3xQFJHjOxXsQIwpQ3bpSuQ=

fast-diff@^1.1.1:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/fast-diff/download/fast-diff-1.2.0.tgz#73ee11982d86caaf7959828d519cfe927fac5f03"
  integrity sha1-c+4RmC2Gyq95WYKNUZz+kn+sXwM=

fast-glob@2.2.6:
  version "2.2.6"
  resolved "http://registry.npm.qima-inc.com/fast-glob/download/fast-glob-2.2.6.tgz#a5d5b697ec8deda468d85a74035290a025a95295"
  integrity sha1-pdW2l+yN7aRo2Fp0A1KQoCWpUpU=
  dependencies:
    "@mrmlnc/readdir-enhanced" "^2.2.1"
    "@nodelib/fs.stat" "^1.1.2"
    glob-parent "^3.1.0"
    is-glob "^4.0.0"
    merge2 "^1.2.3"
    micromatch "^3.1.10"

fast-glob@3.2.2:
  version "3.2.2"
  resolved "http://registry.npm.qima-inc.com/fast-glob/download/fast-glob-3.2.2.tgz#ade1a9d91148965d4bf7c51f72e1ca662d32e63d"
  integrity sha1-reGp2RFIll1L98UfcuHKZi0y5j0=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.0"
    merge2 "^1.3.0"
    micromatch "^4.0.2"
    picomatch "^2.2.1"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@~2.0.6:
  version "2.0.6"
  resolved "http://registry.npm.qima-inc.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastq@^1.6.0:
  version "1.7.0"
  resolved "http://registry.npm.qima-inc.com/fastq/download/fastq-1.7.0.tgz#fcd79a08c5bd7ec5b55cd3f5c4720db551929801"
  integrity sha1-/NeaCMW9fsW1XNP1xHINtVGSmAE=
  dependencies:
    reusify "^1.0.4"

fecha@^3.0.2:
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/fecha/download/fecha-3.0.3.tgz#fabbd416497649a42c24d34bfa726b579203a1e2"
  integrity sha1-+rvUFkl2SaQsJNNL+nJrV5IDoeI=

fecha@^4.2.0:
  version "4.2.3"
  resolved "http://registry.npm.qima-inc.com/fecha/download/fecha-4.2.3.tgz#4d9ccdbc61e8629b259fdca67e65891448d569fd"
  integrity sha1-TZzNvGHoYpsln9ymfmWJFEjVaf0=

figlet@^1.1.1:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/figlet/download/figlet-1.4.0.tgz#21c5878b3752a932ebdb8be400e2d10bbcddfd60"
  integrity sha1-IcWHizdSqTLr24vkAOLRC7zd/WA=

figures@^1.7.0:
  version "1.7.0"
  resolved "http://registry.npm.qima-inc.com/figures/download/figures-1.7.0.tgz#cbe1e3affcf1cd44b80cadfed28dc793a9701d2e"
  integrity sha1-y+Hjr/zxzUS4DK3+0o3Hk6lwHS4=
  dependencies:
    escape-string-regexp "^1.0.5"
    object-assign "^4.1.0"

figures@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/figures/download/figures-2.0.0.tgz#3ab1a2d2a62c8bfb431a0c94cb797a2fce27c962"
  integrity sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=
  dependencies:
    escape-string-regexp "^1.0.5"

figures@^3.0.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/figures/download/figures-3.2.0.tgz#625c18bd293c604dc4a8ddb2febf0c88341746af"
  integrity sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/file-entry-cache/download/file-entry-cache-5.0.1.tgz#ca0f6efa6dd3d561333fb14515065c2fafdf439c"
  integrity sha1-yg9u+m3T1WEzP7FFFQZcL6/fQ5w=
  dependencies:
    flat-cache "^2.0.1"

file-uri-to-path@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz#553a7b8446ff6f684359c445f1e37a05dacc33dd"
  integrity sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=

fill-range@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/fill-range/download/fill-range-4.0.0.tgz#d544811d428f98eb06a63dc402d2403c328c38f7"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "http://registry.npm.qima-inc.com/fill-range/download/fill-range-7.0.1.tgz#1919a6a7c75fe38b2c7c77e5198535da9acdda40"
  integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
  dependencies:
    to-regex-range "^5.0.1"

filter-obj@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/filter-obj/download/filter-obj-1.1.0.tgz#9b311112bc6c6127a16e016c6c5d7f19e0805c5b"
  integrity sha1-mzERErxsYSehbgFsbF1/GeCAXFs=

find-up@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/find-up/download/find-up-3.0.0.tgz#49169f1d7993430646da61ecc5ae355c21c97b73"
  integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
  dependencies:
    locate-path "^3.0.0"

find-up@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/find-up/download/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

flat-cache@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/flat-cache/download/flat-cache-2.0.1.tgz#5d296d6f04bda44a4630a301413bdbc2ec085ec0"
  integrity sha1-XSltbwS9pEpGMKMBQTvbwuwIXsA=
  dependencies:
    flatted "^2.0.0"
    rimraf "2.6.3"
    write "1.0.3"

flatted@^2.0.0:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/flatted/download/flatted-2.0.2.tgz#4575b21e2bcee7434aa9be662f4b7b5f9c2b5138"
  integrity sha1-RXWyHivO50NKqb5mL0t7X5wrUTg=

flexbuffer@0.0.6:
  version "0.0.6"
  resolved "http://registry.npm.qima-inc.com/flexbuffer/download/flexbuffer-0.0.6.tgz#039fdf23f8823e440c38f3277e6fef1174215b30"
  integrity sha1-A5/fI/iCPkQMOPMnfm/vEXQhWzA=

fn-name@~2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/fn-name/download/fn-name-2.0.1.tgz#5214d7537a4d06a4a301c0cc262feb84188002e7"
  integrity sha1-UhTXU3pNBqSjAcDMJi/rhBiAAuc=

follow-redirects@1.5.10:
  version "1.5.10"
  resolved "http://registry.npm.qima-inc.com/follow-redirects/download/follow-redirects-1.5.10.tgz#7b7a9f9aea2fdff36786a94ff643ed07f4ff5e2a"
  integrity sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio=
  dependencies:
    debug "=3.1.0"

follow-redirects@^1.15.0:
  version "1.15.2"
  resolved "http://registry.npm.qima-inc.com/follow-redirects/download/follow-redirects-1.15.2.tgz#b460864144ba63f2681096f274c4e57026da2c13"
  integrity sha1-tGCGQUS6Y/JoEJbydMTlcCbaLBM=

follow-redirects@^1.3.0:
  version "1.11.0"
  resolved "http://registry.npm.qima-inc.com/follow-redirects/download/follow-redirects-1.11.0.tgz#afa14f08ba12a52963140fe43212658897bc0ecb"
  integrity sha1-r6FPCLoSpSljFA/kMhJliJe8Dss=
  dependencies:
    debug "^3.0.0"

for-in@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/for-in/download/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

form-data@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/form-data/download/form-data-4.0.0.tgz#93919daeaf361ee529584b9b31664dc12c9fa452"
  integrity sha1-k5Gdrq82HuUpWEubMWZNwSyfpFI=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

formidable@^1.1.1:
  version "1.2.2"
  resolved "http://registry.npm.qima-inc.com/formidable/download/formidable-1.2.2.tgz#bf69aea2972982675f00865342b982986f6b8dd9"
  integrity sha1-v2muopcpgmdfAIZTQrmCmG9rjdk=

formstream@1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/formstream/download/formstream-1.1.0.tgz#51f3970f26136eb0ad44304de4cebb50207b4479"
  integrity sha1-UfOXDyYTbrCtRDBN5M67UCB7RHk=
  dependencies:
    destroy "^1.0.4"
    mime "^1.3.4"
    pause-stream "~0.0.11"

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/fragment-cache/download/fragment-cache-0.2.1.tgz#4290fad27f13e89be7f33799c6bc5a0abfff0d19"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fresh@^0.5.2:
  version "0.5.2"
  resolved "http://registry.npm.qima-inc.com/fresh/download/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

fs-constants@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/fs-constants/download/fs-constants-1.0.0.tgz#6be0de9be998ce16af8afc24497b9ee9b7ccd9ad"
  integrity sha1-a+Dem+mYzhavivwkSXue6bfM2a0=

fs-extra@5.0.0, fs-extra@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/fs-extra/download/fs-extra-5.0.0.tgz#414d0110cdd06705734d055652c5411260c31abd"
  integrity sha1-QU0BEM3QZwVzTQVWUsVBEmDDGr0=
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^1.2.7:
  version "1.2.12"
  resolved "http://registry.npm.qima-inc.com/fsevents/download/fsevents-1.2.12.tgz#db7e0d8ec3b0b45724fd4d83d43554a8f1f0de5c"
  integrity sha1-234NjsOwtFck/U2D1DVUqPHw3lw=
  dependencies:
    bindings "^1.5.0"
    nan "^2.12.1"

function-bind@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/function-bind/download/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"
  integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz#1b0ab3bd553b2a0d6399d29c0e3ea0b252078327"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

g-status@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/g-status/download/g-status-2.0.2.tgz#270fd32119e8fc9496f066fe5fe88e0a6bc78b97"
  integrity sha1-Jw/TIRno/JSW8Gb+X+iOCmvHi5c=
  dependencies:
    arrify "^1.0.1"
    matcher "^1.0.0"
    simple-git "^1.85.0"

gauge@~2.7.3:
  version "2.7.4"
  resolved "http://registry.npm.qima-inc.com/gauge/download/gauge-2.7.4.tgz#2c03405c7538c39d7eb37b317022e325fb018bf7"
  integrity sha1-LANAXHU4w51+s3sxcCLjJfsBi/c=
  dependencies:
    aproba "^1.0.3"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.0"
    object-assign "^4.1.0"
    signal-exit "^3.0.0"
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wide-align "^1.1.0"

generate-function@^2.3.1:
  version "2.3.1"
  resolved "http://registry.npm.qima-inc.com/generate-function/download/generate-function-2.3.1.tgz#f069617690c10c868e73b8465746764f97c3479f"
  integrity sha1-8GlhdpDBDIaOc7hGV0Z2T5fDR58=
  dependencies:
    is-property "^1.0.2"

get-caller-file@^2.0.1:
  version "2.0.5"
  resolved "http://registry.npm.qima-inc.com/get-caller-file/download/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-intrinsic@^1.0.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/get-intrinsic/download/get-intrinsic-1.1.2.tgz#336975123e05ad0b7ba41f152ee4aadbea6cf598"
  integrity sha1-M2l1Ej4FrQt7pB8VLuSq2+ps9Zg=
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.3"

get-own-enumerable-property-symbols@^3.0.0:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/get-own-enumerable-property-symbols/download/get-own-enumerable-property-symbols-3.0.2.tgz#b5fde77f22cbe35f390b4e089922c50bce6ef664"
  integrity sha1-tf3nfyLL4185C04ImSLFC85u9mQ=

get-port@^3.1.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/get-port/download/get-port-3.2.0.tgz#dd7ce7de187c06c8bf353796ac71e099f0980ebc"
  integrity sha1-3Xzn3hh8Bsi/NTeWrHHgmfCYDrw=

get-stdin@^5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/get-stdin/download/get-stdin-5.0.1.tgz#122e161591e21ff4c52530305693f20e6393a398"
  integrity sha1-Ei4WFZHiH/TFJTAwVpPyDmOTo5g=

get-stdin@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/get-stdin/download/get-stdin-6.0.0.tgz#9e09bf712b360ab9225e812048f71fde9c89657b"
  integrity sha1-ngm/cSs2CrkiXoEgSPcf3pyJZXs=

get-stream@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/get-stream/download/get-stream-3.0.0.tgz#8e943d1358dc37555054ecbe2edb05aa174ede14"
  integrity sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=

get-stream@^4.0.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/get-stream/download/get-stream-4.1.0.tgz#c1b255575f3dc21d59bfc79cd3d2b46b1c3a54b5"
  integrity sha1-wbJVV189wh1Zv8ec09K0axw6VLU=
  dependencies:
    pump "^3.0.0"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "http://registry.npm.qima-inc.com/get-value/download/get-value-2.0.6.tgz#dc15ca1c672387ca76bd37ac0a395ba2042a2c28"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

github-from-package@0.0.0:
  version "0.0.0"
  resolved "http://registry.npm.qima-inc.com/github-from-package/download/github-from-package-0.0.0.tgz#97fb5d96bfde8973313f20e8288ef9a167fa64ce"
  integrity sha1-l/tdlr/eiXMxPyDoKI75oWf6ZM4=

gl-matrix@^2.1.0:
  version "2.8.1"
  resolved "http://registry.npm.qima-inc.com/gl-matrix/download/gl-matrix-2.8.1.tgz#1c7873448eac61d2cd25803a074e837bd42581a3"
  integrity sha1-HHhzRI6sYdLNJYA6B06De9QlgaM=

glob-parent@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/glob-parent/download/glob-parent-3.1.0.tgz#9e6af6299d8d3bd2bd40430832bd113df906c5ae"
  integrity sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=
  dependencies:
    is-glob "^3.1.0"
    path-dirname "^1.0.0"

glob-parent@^5.0.0, glob-parent@^5.1.0:
  version "5.1.1"
  resolved "http://registry.npm.qima-inc.com/glob-parent/download/glob-parent-5.1.1.tgz#b6c1ef417c4e5663ea498f1c45afac6916bbc229"
  integrity sha1-tsHvQXxOVmPqSY8cRa+saRa7wik=
  dependencies:
    is-glob "^4.0.1"

glob-to-regexp@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/glob-to-regexp/download/glob-to-regexp-0.3.0.tgz#8c5a1494d2066c570cc3bfe4496175acc4d502ab"
  integrity sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs=

glob@7.1.6, glob@^7.0.0, glob@^7.0.3, glob@^7.1.2, glob@^7.1.3, glob@^7.1.6:
  version "7.1.6"
  resolved "http://registry.npm.qima-inc.com/glob/download/glob-7.1.6.tgz#141f33b81a7c2492e125594307480c46679278a6"
  integrity sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global-dirs@^0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/global-dirs/download/global-dirs-0.1.1.tgz#b319c0dd4607f353f3be9cca4c72fc148c49f445"
  integrity sha1-sxnA3UYH81PzvpzKTHL8FIxJ9EU=
  dependencies:
    ini "^1.3.4"

globals@^12.1.0:
  version "12.4.0"
  resolved "http://registry.npm.qima-inc.com/globals/download/globals-12.4.0.tgz#a18813576a41b00a24a97e7f815918c2e19925f8"
  integrity sha1-oYgTV2pBsAokqX5/gVkYwuGZJfg=
  dependencies:
    type-fest "^0.8.1"

globals@^9.18.0:
  version "9.18.0"
  resolved "http://registry.npm.qima-inc.com/globals/download/globals-9.18.0.tgz#aa3896b3e69b487f17e31ed2143d69a8e30c2d8a"
  integrity sha1-qjiWs+abSH8X4x7SFD1pqOMMLYo=

globby@^6.1.0:
  version "6.1.0"
  resolved "http://registry.npm.qima-inc.com/globby/download/globby-6.1.0.tgz#f5a6d70e8395e21c858fb0489d64df02424d506c"
  integrity sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=
  dependencies:
    array-union "^1.0.1"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

got@^6.7.1:
  version "6.7.1"
  resolved "http://registry.npm.qima-inc.com/got/download/got-6.7.1.tgz#240cd05785a9a18e561dc1b44b41c763ef1e8db0"
  integrity sha1-JAzQV4WpoY5WHcG0S0HHY+8ejbA=
  dependencies:
    create-error-class "^3.0.0"
    duplexer3 "^0.1.4"
    get-stream "^3.0.0"
    is-redirect "^1.0.0"
    is-retry-allowed "^1.0.0"
    is-stream "^1.0.0"
    lowercase-keys "^1.0.0"
    safe-buffer "^5.0.1"
    timed-out "^4.0.0"
    unzip-response "^2.0.1"
    url-parse-lax "^1.0.0"

graceful-error@1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/graceful-error/download/graceful-error-1.1.1.tgz#edddc1953bd934e6382783ce392dca282149ed29"
  integrity sha1-7d3BlTvZNOY4J4POOS3KKCFJ7Sk=

graceful-fs@^4.1.11, graceful-fs@^4.1.2, graceful-fs@^4.1.6:
  version "4.2.3"
  resolved "http://registry.npm.qima-inc.com/graceful-fs/download/graceful-fs-4.2.3.tgz#4a12ff1b60376ef09862c2093edd908328be8423"
  integrity sha1-ShL/G2A3bvCYYsIJPt2Qgyi+hCM=

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/has-ansi/download/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  integrity sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=
  dependencies:
    ansi-regex "^2.0.0"

has-flag@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/has-flag/download/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-symbols@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/has-symbols/download/has-symbols-1.0.3.tgz#bb7b2c4349251dce87b125f7bdf874aa7c8b39f8"
  integrity sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg=

has-unicode@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/has-unicode/download/has-unicode-2.0.1.tgz#e0e6fe6a28cf51138855e086d1691e771de2a8b9"
  integrity sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=

has-value@^0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/has-value/download/has-value-0.3.1.tgz#7b1f58bada62ca827ec0a2078025654845995e1f"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/has-value/download/has-value-1.0.0.tgz#18b281da585b1c5c51def24c930ed29a0be6b177"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/has-values/download/has-values-0.1.4.tgz#6d61de95d91dfca9b9a02089ad384bff8f62b771"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/has-values/download/has-values-1.0.0.tgz#95b0b63fec2146619a6fe57fe75628d5a39efe4f"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/has/download/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
  integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
  dependencies:
    function-bind "^1.1.1"

here@0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/here/download/here-0.0.2.tgz#69c1af3f02121f3d8788e02e84dc8b3905d71195"
  integrity sha1-acGvPwISHz2HiOAuhNyLOQXXEZU=

highlight.js@^9.6.0:
  version "9.18.1"
  resolved "http://registry.npm.qima-inc.com/highlight.js/download/highlight.js-9.18.1.tgz#ed21aa001fe6252bb10a3d76d47573c6539fe13c"
  integrity sha1-7SGqAB/mJSuxCj121HVzxlOf4Tw=

hosted-git-info@^2.1.4:
  version "2.8.8"
  resolved "http://registry.npm.qima-inc.com/hosted-git-info/download/hosted-git-info-2.8.8.tgz#7539bd4bc1e0e0a895815a2e0262420b12858488"
  integrity sha1-dTm9S8Hg4KiVgVouAmJCCxKFhIg=

http-assert@^1.1.0:
  version "1.4.1"
  resolved "http://registry.npm.qima-inc.com/http-assert/download/http-assert-1.4.1.tgz#c5f725d677aa7e873ef736199b89686cceb37878"
  integrity sha1-xfcl1neqfoc+9zYZm4lobM6zeHg=
  dependencies:
    deep-equal "~1.0.1"
    http-errors "~1.7.2"

http-errors@1.7.3, http-errors@^1.2.8, http-errors@^1.3.1, http-errors@^1.6.1, http-errors@~1.7.2:
  version "1.7.3"
  resolved "http://registry.npm.qima-inc.com/http-errors/download/http-errors-1.7.3.tgz#6c619e4f9c60308c38519498c14fbb10aacebb06"
  integrity sha1-bGGeT5xgMIw4UZSYwU+7EKrOuwY=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "http://registry.npm.qima-inc.com/http-errors/download/http-errors-1.6.3.tgz#8b55680bb4be283a0b5bf4ea2e38580be1d9320d"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

humanize-ms@^1.2.0, humanize-ms@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/humanize-ms/download/humanize-ms-1.2.1.tgz#c46e3159a293f6b896da29316d8b6fe8bb79bbed"
  integrity sha1-xG4xWaKT9riW2ikxbYtv6Lt5u+0=
  dependencies:
    ms "^2.0.0"

husky@^1.3.1:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/husky/download/husky-1.3.1.tgz#26823e399300388ca2afff11cfa8a86b0033fae0"
  integrity sha1-JoI+OZMAOIyir/8Rz6ioawAz+uA=
  dependencies:
    cosmiconfig "^5.0.7"
    execa "^1.0.0"
    find-up "^3.0.0"
    get-stdin "^6.0.0"
    is-ci "^2.0.0"
    pkg-dir "^3.0.0"
    please-upgrade-node "^3.1.1"
    read-pkg "^4.0.1"
    run-node "^1.0.0"
    slash "^2.0.0"

iconv-lite@0.4.24, iconv-lite@^0.4.15, iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "http://registry.npm.qima-inc.com/iconv-lite/download/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.5.0:
  version "0.5.2"
  resolved "http://registry.npm.qima-inc.com/iconv-lite/download/iconv-lite-0.5.2.tgz#af6d628dccfb463b7364d97f715e4b74b8c8c2b8"
  integrity sha1-r21ijcz7RjtzZNl/cV5LdLjIwrg=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

ieee754@^1.1.4:
  version "1.1.13"
  resolved "http://registry.npm.qima-inc.com/ieee754/download/ieee754-1.1.13.tgz#ec168558e95aa181fd87d37f55c32bbcb6708b84"
  integrity sha1-7BaFWOlaoYH9h9N/VcMrvLZwi4Q=

ignore-by-default@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/ignore-by-default/download/ignore-by-default-1.0.1.tgz#48ca6d72f6c6a3af00a9ad4ae6876be3889e2b09"
  integrity sha1-SMptcvbGo68Aqa1K5odr44ieKwk=

ignore@^4.0.6:
  version "4.0.6"
  resolved "http://registry.npm.qima-inc.com/ignore/download/ignore-4.0.6.tgz#750e3db5862087b4737ebac8207ffd1ef27b25fc"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

import-fresh@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/import-fresh/download/import-fresh-2.0.0.tgz#d81355c15612d386c61f9ddd3922d4304822a546"
  integrity sha1-2BNVwVYS04bGH53dOSLUMEgipUY=
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

import-fresh@^3.0.0:
  version "3.2.1"
  resolved "http://registry.npm.qima-inc.com/import-fresh/download/import-fresh-3.2.1.tgz#633ff618506e793af5ac91bf48b72677e15cbe66"
  integrity sha1-Yz/2GFBueTr1rJG/SLcmd+FcvmY=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-lazy@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/import-lazy/download/import-lazy-2.1.0.tgz#05698e3d45c88e8d7e9d92cb0584e77f096f3e43"
  integrity sha1-BWmOPUXIjo1+nZLLBYTnfwlvPkM=

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^3.0.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/indent-string/download/indent-string-3.2.0.tgz#4a5fd6d27cc332f37e5419a504dbb837105c9289"
  integrity sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok=

inflation@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/inflation/download/inflation-2.0.0.tgz#8b417e47c28f925a45133d914ca1fd389107f30f"
  integrity sha1-i0F+R8KPklpFEz2RTKH9OJEH8w8=

inflection@1.12.0:
  version "1.12.0"
  resolved "http://registry.npm.qima-inc.com/inflection/download/inflection-1.12.0.tgz#a200935656d6f5f6bc4dc7502e1aecb703228416"
  integrity sha1-ogCTVlbW9fa8TcdQLhrstwMihBY=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://registry.npm.qima-inc.com/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.3:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/inherits/download/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

ini@^1.3.4, ini@~1.3.0:
  version "1.3.5"
  resolved "http://registry.npm.qima-inc.com/ini/download/ini-1.3.5.tgz#eee25f56db1c9ec6085e0c22778083f596abf927"
  integrity sha1-7uJfVtscnsYIXgwid4CD9Zar+Sc=

inquirer@^7.0.0:
  version "7.1.0"
  resolved "http://registry.npm.qima-inc.com/inquirer/download/inquirer-7.1.0.tgz#1298a01859883e17c7264b82870ae1034f92dd29"
  integrity sha1-EpigGFmIPhfHJkuChwrhA0+S3Sk=
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^3.0.0"
    cli-cursor "^3.1.0"
    cli-width "^2.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.15"
    mute-stream "0.0.8"
    run-async "^2.4.0"
    rxjs "^6.5.3"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"

int24@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.qima-inc.com/int24/download/int24-0.0.1.tgz#96cff1430a46557352edb406cd87b3082b369d84"
  integrity sha1-ls/xQwpGVXNS7bQGzYezCCs2nYQ=

interpret@^1.0.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/interpret/download/interpret-1.2.0.tgz#d5061a6224be58e8083985f5014d844359576296"
  integrity sha1-1QYaYiS+WOgIOYX1AU2EQ1lXYpY=

invariant@^2.2.2:
  version "2.2.4"
  resolved "http://registry.npm.qima-inc.com/invariant/download/invariant-2.2.4.tgz#610f3c92c9359ce1db616e538008d23ff35158e6"
  integrity sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=
  dependencies:
    loose-envify "^1.0.0"

invert-kv@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/invert-kv/download/invert-kv-1.0.0.tgz#104a8e4aaca6d3d8cd157a8ef8bfab2d7a3ffdb6"
  integrity sha1-EEqOSqym09jNFXqO+L+rLXo//bY=

ioredis@4.3.0:
  version "4.3.0"
  resolved "http://registry.npm.qima-inc.com/ioredis/download/ioredis-4.3.0.tgz#a92850dd8794eaee4f38a265c830ca823a09d345"
  integrity sha1-qShQ3YeU6u5POKJlyDDKgjoJ00U=
  dependencies:
    cluster-key-slot "^1.0.6"
    debug "^3.1.0"
    denque "^1.1.0"
    flexbuffer "0.0.6"
    lodash.defaults "^4.2.0"
    lodash.flatten "^4.4.0"
    redis-commands "1.4.0"
    redis-errors "^1.2.0"
    redis-parser "^3.0.0"
    standard-as-callback "^1.0.0"

ip@1.1.5:
  version "1.1.5"
  resolved "http://registry.npm.qima-inc.com/ip/download/ip-1.1.5.tgz#bdded70114290828c0a039e72ef25f5aaec4354a"
  integrity sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo=

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "http://registry.npm.qima-inc.com/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz#a9e12cb3ae8d876727eeef3843f8a0897b5c98d6"
  integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz#169c2f6d3df1f992618072365c9b0ea1f6878656"
  integrity sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=
  dependencies:
    kind-of "^6.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-binary-path/download/is-binary-path-1.0.1.tgz#75f16642b480f187a711c814161fd3a4a7655898"
  integrity sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=
  dependencies:
    binary-extensions "^1.0.0"

is-bluebird@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/is-bluebird/download/is-bluebird-1.0.2.tgz#096439060f4aa411abee19143a84d6a55346d6e2"
  integrity sha1-CWQ5Bg9KpBGr7hkUOoTWpVNG1uI=

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "http://registry.npm.qima-inc.com/is-buffer/download/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-ci@^1.0.10:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/is-ci/download/is-ci-1.2.1.tgz#e3779c8ee17fccf428488f6e281187f2e632841c"
  integrity sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw=
  dependencies:
    ci-info "^1.5.0"

is-ci@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/is-ci/download/is-ci-2.0.0.tgz#6bc6334181810e04b5c22b3d589fdca55026404c"
  integrity sha1-a8YzQYGBDgS1wis9WJ/cpVAmQEw=
  dependencies:
    ci-info "^2.0.0"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz#0b5ee648388e2c860282e793f1856fec3f301b56"
  integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz#d84876321d0e7add03990406abbbbd36ba9268c7"
  integrity sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=
  dependencies:
    kind-of "^6.0.0"

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "http://registry.npm.qima-inc.com/is-descriptor/download/is-descriptor-0.1.6.tgz#366d8240dde487ca51823b1ab9f07a10a78251ca"
  integrity sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/is-descriptor/download/is-descriptor-1.0.2.tgz#3b159746a66604b04f8c81524ba365c5f14d86ec"
  integrity sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-directory@^0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/is-directory/download/is-directory-0.3.1.tgz#61339b6f2475fc772fd9c9d83f5c8575dc154ae1"
  integrity sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/is-extendable/download/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-extendable/download/is-extendable-1.0.1.tgz#a7470f9e426733d81bd81e1155264e3a3507cab4"
  integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^2.1.0, is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz#ef9e31386f031a7f0d643af82fde50c457ef00cb"
  integrity sha1-754xOG8DGn8NZDr4L95QxFfvAMs=
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-generator-function@^1.0.3:
  version "1.0.7"
  resolved "http://registry.npm.qima-inc.com/is-generator-function/download/is-generator-function-1.0.7.tgz#d2132e529bb0000a7f80794d4bdf5cd5e5813522"
  integrity sha1-0hMuUpuwAAp/gHlNS99c1eWBNSI=

is-glob@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/is-glob/download/is-glob-3.1.0.tgz#7ba5ae24217804ac70707b96922567486cc3e84a"
  integrity sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=
  dependencies:
    is-extglob "^2.1.0"

is-glob@^4.0.0, is-glob@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/is-glob/download/is-glob-4.0.1.tgz#7567dbe9f2f5e2467bc77ab83c4a29482407a5dc"
  integrity sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw=
  dependencies:
    is-extglob "^2.1.1"

is-installed-globally@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/is-installed-globally/download/is-installed-globally-0.1.0.tgz#0dfd98f5a9111716dd535dda6492f67bf3d25a80"
  integrity sha1-Df2Y9akRFxbdU13aZJL2e/PSWoA=
  dependencies:
    global-dirs "^0.1.0"
    is-path-inside "^1.0.0"

is-nan@^1.2.1:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/is-nan/download/is-nan-1.3.0.tgz#85d1f5482f7051c2019f5673ccebdb06f3b0db03"
  integrity sha1-hdH1SC9wUcIBn1ZzzOvbBvOw2wM=
  dependencies:
    define-properties "^1.1.3"

is-npm@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-npm/download/is-npm-1.0.0.tgz#f2fb63a65e4905b406c86072765a1a4dc793b9f4"
  integrity sha1-8vtjpl5JBbQGyGBydloaTceTufQ=

is-number@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/is-number/download/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://registry.npm.qima-inc.com/is-number/download/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-obj@^1.0.0, is-obj@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-obj/download/is-obj-1.0.1.tgz#3e4729ac1f5fde025cd7d83a896dab9f4f67db0f"
  integrity sha1-PkcprB9f3gJc19g6iW2rn09n2w8=

is-observable@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/is-observable/download/is-observable-1.1.0.tgz#b3e986c8f44de950867cab5403f5a3465005975e"
  integrity sha1-s+mGyPRN6VCGfKtUA/WjRlAFl14=
  dependencies:
    symbol-observable "^1.1.0"

is-path-cwd@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-path-cwd/download/is-path-cwd-1.0.0.tgz#d225ec23132e89edd38fda767472e62e65f1106d"
  integrity sha1-0iXsIxMuie3Tj9p2dHLmLmXxEG0=

is-path-in-cwd@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-path-in-cwd/download/is-path-in-cwd-1.0.1.tgz#5ac48b345ef675339bd6c7a48a912110b241cf52"
  integrity sha1-WsSLNF72dTOb1sekipEhELJBz1I=
  dependencies:
    is-path-inside "^1.0.0"

is-path-inside@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-path-inside/download/is-path-inside-1.0.1.tgz#8ef5b7de50437a3fdca6b4e865ef7aa55cb48036"
  integrity sha1-jvW33lBDej/cprToZe96pVy0gDY=
  dependencies:
    path-is-inside "^1.0.1"

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/is-plain-object/download/is-plain-object-2.0.4.tgz#2c163b3fafb1b606d9d17928f05c2a1c38e07677"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-promise@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/is-promise/download/is-promise-2.1.0.tgz#79a2a9ece7f096e80f36d2b2f3bc16c1ff4bf3fa"
  integrity sha1-eaKp7OfwlugPNtKy87wWwf9L8/o=

is-property@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/is-property/download/is-property-1.0.2.tgz#57fe1c4e48474edd65b09911f26b1cd4095dda84"
  integrity sha1-V/4cTkhHTt1lsJkR8msc1Ald2oQ=

is-redirect@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-redirect/download/is-redirect-1.0.0.tgz#1d03dded53bd8db0f30c26e4f95d36fc7c87dc24"
  integrity sha1-HQPd7VO9jbDzDCbk+V02/HyH3CQ=

is-regexp@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-regexp/download/is-regexp-1.0.0.tgz#fd2d883545c46bac5a633e7b9a09e87fa2cb5069"
  integrity sha1-/S2INUXEa6xaYz57mgnof6LLUGk=

is-retry-allowed@^1.0.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/is-retry-allowed/download/is-retry-allowed-1.2.0.tgz#d778488bd0a4666a3be8a1482b9f2baafedea8b4"
  integrity sha1-13hIi9CkZmo76KFIK58rqv7eqLQ=

is-stream@^1.0.0, is-stream@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/is-stream/download/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-windows@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/is-windows/download/is-windows-1.0.2.tgz#d1850eb9791ecd18e6182ce12a30f396634bb19d"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

is-wsl@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/is-wsl/download/is-wsl-1.1.0.tgz#1f16e4aa22b04d1336b66188a66af3c600c3a66d"
  integrity sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=

isarray@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.qima-inc.com/isarray/download/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isarray@1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/isobject/download/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/isobject/download/isobject-3.0.1.tgz#4e431e92b11a9731636aa1f9c8d1ccbcfdab78df"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

jest-docblock@^21.0.0:
  version "21.2.0"
  resolved "http://registry.npm.qima-inc.com/jest-docblock/download/jest-docblock-21.2.0.tgz#51529c3b30d5fd159da60c27ceedc195faf8d414"
  integrity sha1-UVKcOzDV/RWdpgwnzu3Blfr41BQ=

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/js-tokens/download/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-tokens@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/js-tokens/download/js-tokens-3.0.2.tgz#9866df395102130e38f7f996bceb65443209c25b"
  integrity sha1-mGbfOVECEw449/mWvOtlRDIJwls=

js-yaml@^3.13.1:
  version "3.13.1"
  resolved "http://registry.npm.qima-inc.com/js-yaml/download/js-yaml-3.13.1.tgz#aff151b30bfdfa8e49e05da22e7415e9dfa37847"
  integrity sha1-r/FRswv9+o5J4F2iLnQV6d+jeEc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

json-parse-better-errors@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://registry.npm.qima-inc.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json5@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/json5/download/json5-1.0.1.tgz#779fb0018604fa854eacbf6252180d83543e3dbe"
  integrity sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4=
  dependencies:
    minimist "^1.2.0"

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/jsonfile/download/jsonfile-4.0.0.tgz#8771aae0799b64076b76640fca058f9c10e33ecb"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonp-body@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/jsonp-body/download/jsonp-body-1.0.0.tgz#e610fb6fcea79cf0cc9f27baa7b56377d4b0bb36"
  integrity sha1-5hD7b86nnPDMnye6p7Vjd9SwuzY=

kafka-node@5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/kafka-node/download/kafka-node-5.0.0.tgz#4b6f65cc1d77ebe565859dfb8f9575ed15d543c0"
  integrity sha1-S29lzB136+VlhZ37j5V17RXVQ8A=
  dependencies:
    async "^2.6.2"
    binary "~0.3.0"
    bl "^2.2.0"
    buffer-crc32 "~0.2.5"
    buffermaker "~1.2.0"
    debug "^2.1.3"
    denque "^1.3.0"
    lodash "^4.17.4"
    minimatch "^3.0.2"
    nested-error-stacks "^2.0.0"
    optional "^0.1.3"
    retry "^0.10.1"
    uuid "^3.0.0"
  optionalDependencies:
    snappy "^6.0.1"

keygrip@~1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/keygrip/download/keygrip-1.0.3.tgz#399d709f0aed2bab0a059e0cdd3a5023a053e1dc"
  integrity sha1-OZ1wnwrtK6sKBZ4M3TpQI6BT4dw=

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "http://registry.npm.qima-inc.com/kind-of/download/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/kind-of/download/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"
  resolved "http://registry.npm.qima-inc.com/kind-of/download/kind-of-5.1.0.tgz#729c91e2d857b7a419a1f9aa65685c4c33f5845d"
  integrity sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=

kind-of@^6.0.0, kind-of@^6.0.2:
  version "6.0.3"
  resolved "http://registry.npm.qima-inc.com/kind-of/download/kind-of-6.0.3.tgz#07c05034a6c349fa06e24fa35aa76db4580ce4dd"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

koa-body@2.5.0:
  version "2.5.0"
  resolved "http://registry.npm.qima-inc.com/koa-body/download/koa-body-2.5.0.tgz#84e8fcd8d5229a8cc1cb98a926e939069e716915"
  integrity sha1-hOj82NUimozBy5ipJuk5Bp5xaRU=
  dependencies:
    co-body "^5.1.1"
    formidable "^1.1.1"

koa-bodyparser@4.2.1:
  version "4.2.1"
  resolved "http://registry.npm.qima-inc.com/koa-bodyparser/download/koa-bodyparser-4.2.1.tgz#4d7dacb5e6db1106649b595d9e5ccb158b6f3b29"
  integrity sha1-TX2stebbEQZkm1ldnlzLFYtvOyk=
  dependencies:
    co-body "^6.0.0"
    copy-to "^2.0.1"

koa-compose@4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/koa-compose/download/koa-compose-4.0.0.tgz#2800a513d9c361ef0d63852b038e4f6f2d5a773c"
  integrity sha1-KAClE9nDYe8NY4UrA45Pby1adzw=

koa-compose@^3.0.0:
  version "3.2.1"
  resolved "http://registry.npm.qima-inc.com/koa-compose/download/koa-compose-3.2.1.tgz#a85ccb40b7d986d8e5a345b3a1ace8eabcf54de7"
  integrity sha1-qFzLQLfZhtjlo0Wzoazo6rz1Tec=
  dependencies:
    any-promise "^1.1.0"

koa-compose@^4.0.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/koa-compose/download/koa-compose-4.1.0.tgz#507306b9371901db41121c812e923d0d67d3e877"
  integrity sha1-UHMGuTcZAdtBEhyBLpI9DWfT6Hc=

koa-convert@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/koa-convert/download/koa-convert-1.2.0.tgz#da40875df49de0539098d1700b50820cebcd21d0"
  integrity sha1-2kCHXfSd4FOQmNFwC1CCDOvNIdA=
  dependencies:
    co "^4.6.0"
    koa-compose "^3.0.0"

koa-is-json@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/koa-is-json/download/koa-is-json-1.0.0.tgz#273c07edcdcb8df6a2c1ab7d59ee76491451ec14"
  integrity sha1-JzwH7c3Ljfaiwat9We52SRRR7BQ=

koa-router@7.4.0:
  version "7.4.0"
  resolved "http://registry.npm.qima-inc.com/koa-router/download/koa-router-7.4.0.tgz#aee1f7adc02d5cb31d7d67465c9eacc825e8c5e0"
  integrity sha1-ruH3rcAtXLMdfWdGXJ6syCXoxeA=
  dependencies:
    debug "^3.1.0"
    http-errors "^1.3.1"
    koa-compose "^3.0.0"
    methods "^1.0.1"
    path-to-regexp "^1.1.1"
    urijs "^1.19.0"

koa-send@^4.1.0:
  version "4.1.3"
  resolved "http://registry.npm.qima-inc.com/koa-send/download/koa-send-4.1.3.tgz#0822207bbf5253a414c8f1765ebc29fa41353cb6"
  integrity sha1-CCIge79SU6QUyPF2Xrwp+kE1PLY=
  dependencies:
    debug "^2.6.3"
    http-errors "^1.6.1"
    mz "^2.6.0"
    resolve-path "^1.4.0"

koa-static@4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/koa-static/download/koa-static-4.0.2.tgz#6cda92d88d771dcaad9f0d825cd94a631c861a1a"
  integrity sha1-bNqS2I13Hcqtnw2CXNlKYxyGGho=
  dependencies:
    debug "^2.6.8"
    koa-send "^4.1.0"

koa@2.5.0:
  version "2.5.0"
  resolved "http://registry.npm.qima-inc.com/koa/download/koa-2.5.0.tgz#b0fbe1e195e43b27588a04fd0be0ddaeca2c154c"
  integrity sha1-sPvh4ZXkOydYigT9C+DdrsosFUw=
  dependencies:
    accepts "^1.2.2"
    content-disposition "~0.5.0"
    content-type "^1.0.0"
    cookies "~0.7.0"
    debug "*"
    delegates "^1.0.0"
    depd "^1.1.0"
    destroy "^1.0.3"
    error-inject "~1.0.0"
    escape-html "~1.0.1"
    fresh "^0.5.2"
    http-assert "^1.1.0"
    http-errors "^1.2.8"
    is-generator-function "^1.0.3"
    koa-compose "^4.0.0"
    koa-convert "^1.2.0"
    koa-is-json "^1.0.0"
    mime-types "^2.0.7"
    on-finished "^2.1.0"
    only "0.0.2"
    parseurl "^1.3.0"
    statuses "^1.2.0"
    type-is "^1.5.5"
    vary "^1.0.0"

latest-version@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/latest-version/download/latest-version-3.1.0.tgz#a205383fea322b33b5ae3b18abee0dc2f356ee15"
  integrity sha1-ogU4P+oyKzO1rjsYq+4NwvNW7hU=
  dependencies:
    package-json "^4.0.0"

lcid@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/lcid/download/lcid-1.0.0.tgz#308accafa0bc483a3867b4b6f2b9506251d1b835"
  integrity sha1-MIrMr6C8SDo4Z7S28rlQYlHRuDU=
  dependencies:
    invert-kv "^1.0.0"

levn@^0.3.0, levn@~0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/levn/download/levn-0.3.0.tgz#3b09924edf9f083c0490fdd4c0bc4421e04764ee"
  integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

lightning-request-net@0.2.3:
  version "0.2.3"
  resolved "http://registry.npm.qima-inc.com/lightning-request-net/download/lightning-request-net-0.2.3.tgz#010178271aa8ef58276814a294f12c8c99cf946b"
  integrity sha1-AQF4Jxqo71gnaBSilPEsjJnPlGs=
  dependencies:
    debug "4.1.1"
    zan-json-parse "1.0.2"

lightning-request@0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.qima-inc.com/lightning-request/download/lightning-request-0.2.0.tgz#6418f915c70e52a362d2b5246745c1df098953ad"
  integrity sha1-ZBj5FccOUqNi0rUkZ0XB3wmJU60=

lightning-request@0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/lightning-request/download/lightning-request-0.2.1.tgz#2cc2d7760cdc8fe4137287da553f978b60836b46"
  integrity sha1-LMLXdgzcj+QTcofaVT+Xi2CDa0Y=
  dependencies:
    sync-rpc "^1.3.6"

lint-staged@^8.1.0:
  version "8.2.1"
  resolved "http://registry.npm.qima-inc.com/lint-staged/download/lint-staged-8.2.1.tgz#752fcf222d9d28f323a3b80f1e668f3654ff221f"
  integrity sha1-dS/PIi2dKPMjo7gPHmaPNlT/Ih8=
  dependencies:
    chalk "^2.3.1"
    commander "^2.14.1"
    cosmiconfig "^5.2.0"
    debug "^3.1.0"
    dedent "^0.7.0"
    del "^3.0.0"
    execa "^1.0.0"
    g-status "^2.0.2"
    is-glob "^4.0.0"
    is-windows "^1.0.2"
    listr "^0.14.2"
    listr-update-renderer "^0.5.0"
    lodash "^4.17.11"
    log-symbols "^2.2.0"
    micromatch "^3.1.8"
    npm-which "^3.0.1"
    p-map "^1.1.1"
    path-is-inside "^1.0.2"
    pify "^3.0.0"
    please-upgrade-node "^3.0.2"
    staged-git-files "1.1.2"
    string-argv "^0.0.2"
    stringify-object "^3.2.2"
    yup "^0.27.0"

listr-silent-renderer@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/listr-silent-renderer/download/listr-silent-renderer-1.1.1.tgz#924b5a3757153770bf1a8e3fbf74b8bbf3f9242e"
  integrity sha1-kktaN1cVN3C/Go4/v3S4u/P5JC4=

listr-update-renderer@^0.5.0:
  version "0.5.0"
  resolved "http://registry.npm.qima-inc.com/listr-update-renderer/download/listr-update-renderer-0.5.0.tgz#4ea8368548a7b8aecb7e06d8c95cb45ae2ede6a2"
  integrity sha1-Tqg2hUinuK7LfgbYyVy0WuLt5qI=
  dependencies:
    chalk "^1.1.3"
    cli-truncate "^0.2.1"
    elegant-spinner "^1.0.1"
    figures "^1.7.0"
    indent-string "^3.0.0"
    log-symbols "^1.0.2"
    log-update "^2.3.0"
    strip-ansi "^3.0.1"

listr-verbose-renderer@^0.5.0:
  version "0.5.0"
  resolved "http://registry.npm.qima-inc.com/listr-verbose-renderer/download/listr-verbose-renderer-0.5.0.tgz#f1132167535ea4c1261102b9f28dac7cba1e03db"
  integrity sha1-8RMhZ1NepMEmEQK58o2sfLoeA9s=
  dependencies:
    chalk "^2.4.1"
    cli-cursor "^2.1.0"
    date-fns "^1.27.2"
    figures "^2.0.0"

listr@^0.14.2:
  version "0.14.3"
  resolved "http://registry.npm.qima-inc.com/listr/download/listr-0.14.3.tgz#2fea909604e434be464c50bddba0d496928fa586"
  integrity sha1-L+qQlgTkNL5GTFC926DUlpKPpYY=
  dependencies:
    "@samverschueren/stream-to-observable" "^0.3.0"
    is-observable "^1.1.0"
    is-promise "^2.1.0"
    is-stream "^1.1.0"
    listr-silent-renderer "^1.1.1"
    listr-update-renderer "^0.5.0"
    listr-verbose-renderer "^0.5.0"
    p-map "^2.0.0"
    rxjs "^6.3.3"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/locate-path/download/locate-path-3.0.0.tgz#dbec3b3ab759758071b58fe59fc41871af21400e"
  integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/locate-path/download/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

lodash.clone@^4.5.0:
  version "4.5.0"
  resolved "http://registry.npm.qima-inc.com/lodash.clone/download/lodash.clone-4.5.0.tgz#195870450f5a13192478df4bc3d23d2dea1907b6"
  integrity sha1-GVhwRQ9aExkkeN9Lw9I9LeoZB7Y=

lodash.defaults@^4.2.0:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/lodash.defaults/download/lodash.defaults-4.2.0.tgz#d09178716ffea4dde9e5fb7b37f6f0802274580c"
  integrity sha1-0JF4cW/+pN3p5ft7N/bwgCJ0WAw=

lodash.flatten@^4.4.0:
  version "4.4.0"
  resolved "http://registry.npm.qima-inc.com/lodash.flatten/download/lodash.flatten-4.4.0.tgz#f31c22225a9632d2bbf8e4addbef240aa765a61f"
  integrity sha1-8xwiIlqWMtK7+OSt2+8kCqdlph8=

lodash.toarray@^4.4.0:
  version "4.4.0"
  resolved "http://registry.npm.qima-inc.com/lodash.toarray/download/lodash.toarray-4.4.0.tgz#24c4bfcd6b2fba38bfd0594db1179d8e9b656561"
  integrity sha1-JMS/zWsvuji/0FlNsRedjptlZWE=

lodash@4.17.10:
  version "4.17.10"
  resolved "http://registry.npm.qima-inc.com/lodash/download/lodash-4.17.10.tgz#1b7793cf7259ea38fb3661d4d38b3260af8ae4e7"
  integrity sha1-G3eTz3JZ6jj7NmHU04syYK+K5Oc=

lodash@4.17.11:
  version "4.17.11"
  resolved "http://registry.npm.qima-inc.com/lodash/download/lodash-4.17.11.tgz#b39ea6229ef607ecd89e2c8df12536891cac9b8d"
  integrity sha1-s56mIp72B+zYniyN8SU2iRysm40=

lodash@^4.17.10:
  version "4.17.21"
  resolved "http://registry.npm.qima-inc.com/lodash/download/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

lodash@^4.17.11, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.4, lodash@~>=4.17.11:
  version "4.17.15"
  resolved "http://registry.npm.qima-inc.com/lodash/download/lodash-4.17.15.tgz#b447f6670a0455bbfeedd11392eff330ea097548"
  integrity sha1-tEf2ZwoEVbv+7dETku/zMOoJdUg=

log-symbols@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/log-symbols/download/log-symbols-1.0.2.tgz#376ff7b58ea3086a0f09facc74617eca501e1a18"
  integrity sha1-N2/3tY6jCGoPCfrMdGF+ylAeGhg=
  dependencies:
    chalk "^1.0.0"

log-symbols@^2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/log-symbols/download/log-symbols-2.2.0.tgz#5740e1c5d6f0dfda4ad9323b5332107ef6b4c40a"
  integrity sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=
  dependencies:
    chalk "^2.0.1"

log-update@^2.3.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/log-update/download/log-update-2.3.0.tgz#88328fd7d1ce7938b29283746f0b1bc126b24708"
  integrity sha1-iDKP19HOeTiykoN0bwsbwSayRwg=
  dependencies:
    ansi-escapes "^3.0.0"
    cli-cursor "^2.0.0"
    wrap-ansi "^3.0.1"

long-timeout@0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/long-timeout/download/long-timeout-0.1.1.tgz#9721d788b47e0bcb5a24c2e2bee1a0da55dab514"
  integrity sha1-lyHXiLR+C8taJMLivuGg2lXatRQ=

long@1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/long/download/long-1.1.2.tgz#eaef5951ca7551d96926b82da242db9d6b28fb53"
  integrity sha1-6u9ZUcp1UdlpJrgtokLbnWso+1M=

long@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/long/download/long-4.0.0.tgz#9a7b71cfb7d361a194ea555241c92f7468d5bf28"
  integrity sha1-mntxz7fTYaGU6lVSQckvdGjVvyg=

loose-envify@^1.0.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/loose-envify/download/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lowercase-keys@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/lowercase-keys/download/lowercase-keys-1.0.1.tgz#6f9e30b47084d971a7c820ff15a6c5167b74c26f"
  integrity sha1-b54wtHCE2XGnyCD/FabFFnt0wm8=

lru-cache@5.1.1, lru-cache@^5.1.1:
  version "5.1.1"
  resolved "http://registry.npm.qima-inc.com/lru-cache/download/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

lru-cache@^4.0.1, lru-cache@^4.1.3:
  version "4.1.5"
  resolved "http://registry.npm.qima-inc.com/lru-cache/download/lru-cache-4.1.5.tgz#8bbe50ea85bed59bc9e33dcab8235ee9bcf443cd"
  integrity sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

make-dir@^1.0.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/make-dir/download/make-dir-1.3.0.tgz#79c1033b80515bd6d24ec9933e860ca75ee27f0c"
  integrity sha1-ecEDO4BRW9bSTsmTPoYMp17ifww=
  dependencies:
    pify "^3.0.0"

make-error@^1.1.1:
  version "1.3.6"
  resolved "http://registry.npm.qima-inc.com/make-error/download/make-error-1.3.6.tgz#2eb2e37ea9b67c4891f684a1394799af484cf7a2"
  integrity sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=

map-cache@^0.2.2:
  version "0.2.2"
  resolved "http://registry.npm.qima-inc.com/map-cache/download/map-cache-0.2.2.tgz#c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-canvas@>=0.1.5:
  version "0.1.5"
  resolved "http://registry.npm.qima-inc.com/map-canvas/download/map-canvas-0.1.5.tgz#8be6bade0bf3e9f9a8b56e8836a1d1d133cab186"
  integrity sha1-i+a63gvz6fmotW6INqHR0TPKsYY=
  dependencies:
    drawille-canvas-blessed-contrib ">=0.0.1"
    xml2js "^0.4.5"

map-visit@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/map-visit/download/map-visit-1.0.0.tgz#ecdca8f13144e660f1b5bd41f12f3479d98dfb8f"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

marked-terminal@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/marked-terminal/download/marked-terminal-4.0.0.tgz#2c7aa2c0eec496f05cd61f768d80d35db0bf6a86"
  integrity sha1-LHqiwO7ElvBc1h92jYDTXbC/aoY=
  dependencies:
    ansi-escapes "^4.3.0"
    cardinal "^2.1.1"
    chalk "^3.0.0"
    cli-table "^0.3.1"
    node-emoji "^1.10.0"
    supports-hyperlinks "^2.0.0"

marked@^0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.qima-inc.com/marked/download/marked-0.7.0.tgz#b64201f051d271b1edc10a04d1ae9b74bb8e5c0e"
  integrity sha1-tkIB8FHScbHtwQoE0a6bdLuOXA4=

matcher@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/matcher/download/matcher-1.1.1.tgz#51d8301e138f840982b338b116bb0c09af62c1c2"
  integrity sha1-UdgwHhOPhAmCszixFrsMCa9iwcI=
  dependencies:
    escape-string-regexp "^1.0.4"

media-typer@0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/media-typer/download/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memory-streams@^0.1.0:
  version "0.1.3"
  resolved "http://registry.npm.qima-inc.com/memory-streams/download/memory-streams-0.1.3.tgz#d9b0017b4b87f1d92f55f2745c9caacb1dc93ceb"
  integrity sha1-2bABe0uH8dkvVfJ0XJyqyx3JPOs=
  dependencies:
    readable-stream "~1.0.2"

memorystream@^0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/memorystream/download/memorystream-0.3.1.tgz#86d7090b30ce455d63fbae12dda51a47ddcaf9b2"
  integrity sha1-htcJCzDORV1j+64S3aUaR93K+bI=

merge2@^1.2.3, merge2@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/merge2/download/merge2-1.3.0.tgz#5b366ee83b2f1582c48f87e47cf1a9352103ca81"
  integrity sha1-WzZu6DsvFYLEj4fkfPGpNSEDyoE=

methods@1.1.2, methods@^1.0.1:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/methods/download/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromatch@^3.1.10, micromatch@^3.1.4, micromatch@^3.1.8:
  version "3.1.10"
  resolved "http://registry.npm.qima-inc.com/micromatch/download/micromatch-3.1.10.tgz#70859bc95c9840952f359a068a3fc49f9ecfac23"
  integrity sha1-cIWbyVyYQJUvNZoGij/En57PrCM=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

micromatch@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/micromatch/download/micromatch-4.0.2.tgz#4fcb0999bf9fbc2fcbdd212f6d629b9a56c39259"
  integrity sha1-T8sJmb+fvC/L3SEvbWKbmlbDklk=
  dependencies:
    braces "^3.0.1"
    picomatch "^2.0.5"

microtime@3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/microtime/download/microtime-3.0.0.tgz#d140914bde88aa89b4f9fd2a18620b435af0f39b"
  integrity sha1-0UCRS96Iqom0+f0qGGILQ1rw85s=
  dependencies:
    node-addon-api "^1.2.0"
    node-gyp-build "^3.8.0"

mime-db@1.43.0:
  version "1.43.0"
  resolved "http://registry.npm.qima-inc.com/mime-db/download/mime-db-1.43.0.tgz#0a12e0502650e473d735535050e7c8f4eb4fae58"
  integrity sha1-ChLgUCZQ5HPXNVNQUOfI9OtPrlg=

mime-db@1.52.0:
  version "1.52.0"
  resolved "http://registry.npm.qima-inc.com/mime-db/download/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

mime-types@^2.0.7, mime-types@~2.1.24:
  version "2.1.26"
  resolved "http://registry.npm.qima-inc.com/mime-types/download/mime-types-2.1.26.tgz#9c921fc09b7e149a65dfdc0da4d20997200b0a06"
  integrity sha1-nJIfwJt+FJpl39wNpNIJlyALCgY=
  dependencies:
    mime-db "1.43.0"

mime-types@^2.1.12, mime-types@^2.1.27:
  version "2.1.35"
  resolved "http://registry.npm.qima-inc.com/mime-types/download/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

mime@2.3.1:
  version "2.3.1"
  resolved "http://registry.npm.qima-inc.com/mime/download/mime-2.3.1.tgz#b1621c54d63b97c47d3cfe7f7215f7d64517c369"
  integrity sha1-sWIcVNY7l8R9PP5/chX31kUXw2k=

mime@^1.3.4:
  version "1.6.0"
  resolved "http://registry.npm.qima-inc.com/mime/download/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/mimic-fn/download/mimic-fn-1.2.0.tgz#820c86a39334640e99516928bd03fca88057d022"
  integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/mimic-fn/download/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

mimic-response@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/mimic-response/download/mimic-response-1.0.1.tgz#4923538878eef42063cb8a3e3b0798781487ab1b"
  integrity sha1-SSNTiHju9CBjy4o+OweYeBSHqxs=

minimatch@^3.0.2:
  version "3.1.2"
  resolved "http://registry.npm.qima-inc.com/minimatch/download/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^3.0.4:
  version "3.0.4"
  resolved "http://registry.npm.qima-inc.com/minimatch/download/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  integrity sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=
  dependencies:
    brace-expansion "^1.1.7"

minimist@^1.1.0, minimist@^1.2.0, minimist@^1.2.5:
  version "1.2.5"
  resolved "http://registry.npm.qima-inc.com/minimist/download/minimist-1.2.5.tgz#67d66014b66a6a8aaa0c083c5fd58df4e4e97602"
  integrity sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "http://registry.npm.qima-inc.com/mixin-deep/download/mixin-deep-1.3.2.tgz#1120b43dc359a785dce65b55b82e257ccf479566"
  integrity sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mkdirp@^0.5.1:
  version "0.5.5"
  resolved "http://registry.npm.qima-inc.com/mkdirp/download/mkdirp-0.5.5.tgz#d91cefd62d1436ca0f41620e251288d420099def"
  integrity sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=
  dependencies:
    minimist "^1.2.5"

mkdirp@^1.0.3:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/mkdirp/download/mkdirp-1.0.4.tgz#3eb5ed62622756d79a5f0e2a221dfebad75c2f7e"
  integrity sha1-PrXtYmInVteaXw4qIh3+utdcL34=

moment-timezone@^0.5.21, moment-timezone@^0.5.25:
  version "0.5.28"
  resolved "http://registry.npm.qima-inc.com/moment-timezone/download/moment-timezone-0.5.28.tgz#f093d789d091ed7b055d82aa81a82467f72e4338"
  integrity sha1-8JPXidCR7XsFXYKqgagkZ/cuQzg=
  dependencies:
    moment ">= 2.9.0"

"moment@>= 2.9.0", moment@^2.24.0:
  version "2.24.0"
  resolved "http://registry.npm.qima-inc.com/moment/download/moment-2.24.0.tgz#0d055d53f5052aa653c9f6eb68bb5d12bf5c2b5b"
  integrity sha1-DQVdU/UFKqZTyfbraLtdEr9cK1s=

moment@^2.29.4:
  version "2.29.4"
  resolved "http://registry.npm.qima-inc.com/moment/download/moment-2.29.4.tgz#3dbe052889fe7c1b2ed966fcb3a77328964ef108"
  integrity sha1-Pb4FKIn+fBsu2Wb8s6dzKJZO8Qg=

ms@2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/ms/download/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@^2.0.0, ms@^2.1.1:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/ms/download/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

mute-stream@0.0.8:
  version "0.0.8"
  resolved "http://registry.npm.qima-inc.com/mute-stream/download/mute-stream-0.0.8.tgz#1630c42b2251ff81e2a283de96a5497ea92e5e0d"
  integrity sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=

mysql2@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/mysql2/download/mysql2-2.1.0.tgz#55ecfd4353114c148cc4c253192dbbfd000e6642"
  integrity sha1-Vez9Q1MRTBSMxMJTGS27/QAOZkI=
  dependencies:
    cardinal "^2.1.1"
    denque "^1.4.1"
    generate-function "^2.3.1"
    iconv-lite "^0.5.0"
    long "^4.0.0"
    lru-cache "^5.1.1"
    named-placeholders "^1.1.2"
    seq-queue "^0.0.5"
    sqlstring "^2.3.1"

mz@^2.4.0, mz@^2.6.0:
  version "2.7.0"
  resolved "http://registry.npm.qima-inc.com/mz/download/mz-2.7.0.tgz#95008057a56cafadc2bc63dde7f9ff6955948e32"
  integrity sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

named-placeholders@^1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/named-placeholders/download/named-placeholders-1.1.2.tgz#ceb1fbff50b6b33492b5cf214ccf5e39cef3d0e8"
  integrity sha1-zrH7/1C2szSStc8hTM9eOc7z0Og=
  dependencies:
    lru-cache "^4.1.3"

nan@^2.12.1:
  version "2.14.0"
  resolved "http://registry.npm.qima-inc.com/nan/download/nan-2.14.0.tgz#7818f722027b2459a86f0295d434d1fc2336c52c"
  integrity sha1-eBj3IgJ7JFmobwKV1DTR/CM2xSw=

nan@^2.14.1:
  version "2.16.0"
  resolved "http://registry.npm.qima-inc.com/nan/download/nan-2.16.0.tgz#664f43e45460fb98faf00edca0bb0d7b8dce7916"
  integrity sha1-Zk9D5FRg+5j68A7coLsNe43OeRY=

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "http://registry.npm.qima-inc.com/nanomatch/download/nanomatch-1.2.13.tgz#b87a8aa4fc0de8fe6be88895b38983ff265bd119"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

napi-build-utils@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/napi-build-utils/download/napi-build-utils-1.0.2.tgz#b1fddc0b2c46e380a0b7a76f984dd47c41a13806"
  integrity sha1-sf3cCyxG44Cgt6dvmE3UfEGhOAY=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

negotiator@0.6.2:
  version "0.6.2"
  resolved "http://registry.npm.qima-inc.com/negotiator/download/negotiator-0.6.2.tgz#feacf7ccf525a77ae9634436a64883ffeca346fb"
  integrity sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs=

nested-error-stacks@^2.0.0:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/nested-error-stacks/download/nested-error-stacks-2.1.1.tgz#26c8a3cee6cc05fbcf1e333cd2fc3e003326c0b5"
  integrity sha1-JsijzubMBfvPHjM80vw+ADMmwLU=

nice-try@^1.0.4:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/nice-try/download/nice-try-1.0.5.tgz#a3378a7696ce7d223e88fc9b764bd7ef1089e366"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

node-abi@^2.7.0:
  version "2.30.1"
  resolved "http://registry.npm.qima-inc.com/node-abi/download/node-abi-2.30.1.tgz#c437d4b1fe0e285aaf290d45b45d4d7afedac4cf"
  integrity sha1-xDfUsf4OKFqvKQ1FtF1Nev7axM8=
  dependencies:
    semver "^5.4.1"

node-addon-api@^1.2.0:
  version "1.7.1"
  resolved "http://registry.npm.qima-inc.com/node-addon-api/download/node-addon-api-1.7.1.tgz#cf813cd69bb8d9100f6bdca6755fc268f54ac492"
  integrity sha1-z4E81pu42RAPa9ymdV/CaPVKxJI=

node-addon-api@^1.3.0:
  version "1.7.2"
  resolved "http://registry.npm.qima-inc.com/node-addon-api/download/node-addon-api-1.7.2.tgz#3df30b95720b53c24e59948b49532b662444f54d"
  integrity sha1-PfMLlXILU8JOWZSLSVMrZiRE9U0=

node-emoji@^1.10.0:
  version "1.10.0"
  resolved "http://registry.npm.qima-inc.com/node-emoji/download/node-emoji-1.10.0.tgz#8886abd25d9c7bb61802a658523d1f8d2a89b2da"
  integrity sha1-iIar0l2ce7YYAqZYUj0fjSqJsto=
  dependencies:
    lodash.toarray "^4.4.0"

node-fetch@^2.6.1:
  version "2.6.7"
  resolved "http://registry.npm.qima-inc.com/node-fetch/download/node-fetch-2.6.7.tgz#24de9fba827e3b4ae44dc8b20256a379160052ad"
  integrity sha1-JN6fuoJ+O0rkTciyAlajeRYAUq0=
  dependencies:
    whatwg-url "^5.0.0"

node-gyp-build@^3.8.0:
  version "3.9.0"
  resolved "http://registry.npm.qima-inc.com/node-gyp-build/download/node-gyp-build-3.9.0.tgz#53a350187dd4d5276750da21605d1cb681d09e25"
  integrity sha1-U6NQGH3U1SdnUNohYF0ctoHQniU=

node-int64@~0.4.0:
  version "0.4.0"
  resolved "http://registry.npm.qima-inc.com/node-int64/download/node-int64-0.4.0.tgz#87a9065cdb355d3182d8f94ce11188b825c68a3b"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-schedule@1.3.2:
  version "1.3.2"
  resolved "http://registry.npm.qima-inc.com/node-schedule/download/node-schedule-1.3.2.tgz#d774b383e2a6f6ade59eecc62254aea07cd758cb"
  integrity sha1-13Szg+Km9q3lnuzGIlSuoHzXWMs=
  dependencies:
    cron-parser "^2.7.3"
    long-timeout "0.1.1"
    sorted-array-functions "^1.0.0"

node-state@~1.4.4:
  version "1.4.4"
  resolved "http://registry.npm.qima-inc.com/node-state/download/node-state-1.4.4.tgz#e5e62d86167e2da87238ac86fcf532dfe36cadb9"
  integrity sha1-5eYthhZ+LahyOKyG/PUy3+Nsrbk=

nodemon@^1.17.3:
  version "1.19.4"
  resolved "http://registry.npm.qima-inc.com/nodemon/download/nodemon-1.19.4.tgz#56db5c607408e0fdf8920d2b444819af1aae0971"
  integrity sha1-VttcYHQI4P34kg0rREgZrxquCXE=
  dependencies:
    chokidar "^2.1.8"
    debug "^3.2.6"
    ignore-by-default "^1.0.1"
    minimatch "^3.0.4"
    pstree.remy "^1.1.7"
    semver "^5.7.1"
    supports-color "^5.5.0"
    touch "^3.1.0"
    undefsafe "^2.0.2"
    update-notifier "^2.5.0"

noop-logger@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/noop-logger/download/noop-logger-0.1.1.tgz#94a2b1633c4f1317553007d8966fd0e841b6a4c2"
  integrity sha1-lKKxYzxPExdVMAfYlm/Q6EG2pMI=

nopt@~1.0.10:
  version "1.0.10"
  resolved "http://registry.npm.qima-inc.com/nopt/download/nopt-1.0.10.tgz#6ddd21bd2a31417b92727dd585f8a6f37608ebee"
  integrity sha1-bd0hvSoxQXuScn3Vhfim83YI6+4=
  dependencies:
    abbrev "1"

nopt@~2.1.2:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/nopt/download/nopt-2.1.2.tgz#6cccd977b80132a07731d6e8ce58c2c8303cf9af"
  integrity sha1-bMzZd7gBMqB3MdbozljCyDA8+a8=
  dependencies:
    abbrev "1"

normalize-package-data@^2.3.2:
  version "2.5.0"
  resolved "http://registry.npm.qima-inc.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz#e66db1838b200c1dfc233225d12cb36520e234a8"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/normalize-path/download/normalize-path-2.1.1.tgz#1ab28b556e198363a8c1a6f7e6fa20137fe6aed9"
  integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-path@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/normalize-path/download/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

npm-path@^2.0.2:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/npm-path/download/npm-path-2.0.4.tgz#c641347a5ff9d6a09e4d9bce5580c4f505278e64"
  integrity sha1-xkE0el/51qCeTZvOVYDE9QUnjmQ=
  dependencies:
    which "^1.2.10"

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/npm-run-path/download/npm-run-path-2.0.2.tgz#35a9232dfa35d7067b4cb2ddf2357b1871536c5f"
  integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
  dependencies:
    path-key "^2.0.0"

npm-which@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/npm-which/download/npm-which-3.0.1.tgz#9225f26ec3a285c209cae67c3b11a6b4ab7140aa"
  integrity sha1-kiXybsOihcIJyuZ8OxGmtKtxQKo=
  dependencies:
    commander "^2.9.0"
    npm-path "^2.0.2"
    which "^1.2.10"

npmlog@^4.0.1:
  version "4.1.2"
  resolved "http://registry.npm.qima-inc.com/npmlog/download/npmlog-4.1.2.tgz#08a7f2a8bf734604779a9efa4ad5cc717abb954b"
  integrity sha1-CKfyqL9zRgR3mp76StXMcXq7lUs=
  dependencies:
    are-we-there-yet "~1.1.2"
    console-control-strings "~1.1.0"
    gauge "~2.7.3"
    set-blocking "~2.0.0"

nsqjs@^0.13.0:
  version "0.13.0"
  resolved "http://registry.npm.qima-inc.com/nsqjs/download/nsqjs-0.13.0.tgz#b4aebee398bf9015f643d9633575768a18c45fbb"
  integrity sha1-tK6+45i/kBX2Q9ljNXV2ihjEX7s=
  dependencies:
    lodash "^4.17.15"
    node-fetch "^2.6.1"
    node-state "~1.4.4"
  optionalDependencies:
    snappystream "^2.0.0"

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/number-is-nan/download/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"
  integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=

nunjucks@3.1.6:
  version "3.1.6"
  resolved "http://registry.npm.qima-inc.com/nunjucks/download/nunjucks-3.1.6.tgz#6e3a3420c77ceae937ae323e9e2995383d6410fb"
  integrity sha1-bjo0IMd86uk3rjI+nimVOD1kEPs=
  dependencies:
    a-sync-waterfall "^1.0.0"
    asap "^2.0.3"
    postinstall-build "^5.0.1"
    yargs "^3.32.0"
  optionalDependencies:
    chokidar "^2.0.0"

object-assign@^4.0.1, object-assign@^4.1.0:
  version "4.1.1"
  resolved "http://registry.npm.qima-inc.com/object-assign/download/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/object-copy/download/object-copy-0.1.0.tgz#7e7d858b781bd7c991a41ba975ed3812754e998c"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-inspect@^1.9.0:
  version "1.12.2"
  resolved "http://registry.npm.qima-inc.com/object-inspect/download/object-inspect-1.12.2.tgz#c0641f26394532f28ab8d796ab954e43c009a8ea"
  integrity sha1-wGQfJjlFMvKKuNeWq5VOQ8AJqOo=

object-keys@^1.0.12:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/object-keys/download/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object-visit@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/object-visit/download/object-visit-1.0.1.tgz#f79c4493af0c5377b59fe39d395e41042dd045bb"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/object.pick/download/object.pick-1.3.0.tgz#87a10ac4c1694bd2e1cbf53591a66141fb5dd747"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

on-finished@^2.1.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/on-finished/download/on-finished-2.3.0.tgz#20f1336481b083cd75337992a16971aa2d906947"
  integrity sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=
  dependencies:
    ee-first "1.1.1"

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/onetime/download/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

onetime@^5.1.0:
  version "5.1.0"
  resolved "http://registry.npm.qima-inc.com/onetime/download/onetime-5.1.0.tgz#fff0f3c91617fe62bb50189636e99ac8a6df7be5"
  integrity sha1-//DzyRYX/mK7UBiWNumayKbfe+U=
  dependencies:
    mimic-fn "^2.1.0"

only@0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/only/download/only-0.0.2.tgz#2afde84d03e50b9a8edc444e30610a70295edfb4"
  integrity sha1-Kv3oTQPlC5qO3EROMGEKcCle37Q=

opn@^5.3.0:
  version "5.5.0"
  resolved "http://registry.npm.qima-inc.com/opn/download/opn-5.5.0.tgz#fc7164fab56d235904c51c3b27da6758ca3b9bfc"
  integrity sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w=
  dependencies:
    is-wsl "^1.1.0"

optimist@0.2:
  version "0.2.8"
  resolved "http://registry.npm.qima-inc.com/optimist/download/optimist-0.2.8.tgz#e981ab7e268b457948593b55674c099a815cac31"
  integrity sha1-6YGrfiaLRXlIWTtVZ0wJmoFcrDE=
  dependencies:
    wordwrap ">=0.0.1 <0.1.0"

optimist@~0.3.4:
  version "0.3.7"
  resolved "http://registry.npm.qima-inc.com/optimist/download/optimist-0.3.7.tgz#c90941ad59e4273328923074d2cf2e7cbc6ec0d9"
  integrity sha1-yQlBrVnkJzMokjB00s8ufLxuwNk=
  dependencies:
    wordwrap "~0.0.2"

optional@^0.1.3:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/optional/download/optional-0.1.4.tgz#cdb1a9bedc737d2025f690ceeb50e049444fd5b3"
  integrity sha1-zbGpvtxzfSAl9pDO61DgSURP1bM=

optionator@^0.8.3:
  version "0.8.3"
  resolved "http://registry.npm.qima-inc.com/optionator/download/optionator-0.8.3.tgz#84fa1d036fe9d3c7e21d99884b601167ec8fb495"
  integrity sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.6"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    word-wrap "~1.2.3"

ora@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/ora/download/ora-2.1.0.tgz#6caf2830eb924941861ec53a173799e008b51e5b"
  integrity sha1-bK8oMOuSSUGGHsU6FzeZ4Ai1Hls=
  dependencies:
    chalk "^2.3.1"
    cli-cursor "^2.1.0"
    cli-spinners "^1.1.0"
    log-symbols "^2.2.0"
    strip-ansi "^4.0.0"
    wcwidth "^1.0.1"

ora@^3.0.0:
  version "3.4.0"
  resolved "http://registry.npm.qima-inc.com/ora/download/ora-3.4.0.tgz#bf0752491059a3ef3ed4c85097531de9fdbcd318"
  integrity sha1-vwdSSRBZo+8+1MhQl1Md6f280xg=
  dependencies:
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-spinners "^2.0.0"
    log-symbols "^2.2.0"
    strip-ansi "^5.2.0"
    wcwidth "^1.0.1"

os-homedir@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/os-homedir/download/os-homedir-1.0.2.tgz#ffbc4988336e0e833de0c168c7ef152121aa7fb3"
  integrity sha1-/7xJiDNuDoM94MFox+8VISGqf7M=

os-locale@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/os-locale/download/os-locale-1.4.0.tgz#20f9f17ae29ed345e8bde583b13d2009803c14d9"
  integrity sha1-IPnxeuKe00XoveWDsT0gCYA8FNk=
  dependencies:
    lcid "^1.0.0"

os-name@~1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/os-name/download/os-name-1.0.3.tgz#1b379f64835af7c5a7f498b357cb95215c159edf"
  integrity sha1-GzefZINa98Wn9JizV8uVIVwVnt8=
  dependencies:
    osx-release "^1.0.0"
    win-release "^1.0.0"

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

osx-release@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/osx-release/download/osx-release-1.1.0.tgz#f217911a28136949af1bf9308b241e2737d3cd6c"
  integrity sha1-8heRGigTaUmvG/kwiyQeJzfTzWw=
  dependencies:
    minimist "^1.1.0"

p-event@^4.2.0:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/p-event/download/p-event-4.2.0.tgz#af4b049c8acd91ae81083ebd1e6f5cae2044c1b5"
  integrity sha1-r0sEnIrNka6BCD69Hm9criBEwbU=
  dependencies:
    p-timeout "^3.1.0"

p-finally@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/p-finally/download/p-finally-1.0.0.tgz#3fbcfb15b899a44123b34b6dcc18b724336a2cae"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-limit@^2.0.0, p-limit@^2.2.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/p-limit/download/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/p-locate/download/p-locate-3.0.0.tgz#322d69a05c0264b25997d9f40cd8a891ab0064a4"
  integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/p-locate/download/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-map@^1.1.1:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/p-map/download/p-map-1.2.0.tgz#e4e94f311eabbc8633a1e79908165fca26241b6b"
  integrity sha1-5OlPMR6rvIYzoeeZCBZfyiYkG2s=

p-map@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/p-map/download/p-map-2.1.0.tgz#310928feef9c9ecc65b68b17693018a665cea175"
  integrity sha1-MQko/u+cnsxltosXaTAYpmXOoXU=

p-timeout@^3.1.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/p-timeout/download/p-timeout-3.2.0.tgz#c7e17abc971d2a7962ef83626b35d635acf23dfe"
  integrity sha1-x+F6vJcdKnli74NiazXWNazyPf4=
  dependencies:
    p-finally "^1.0.0"

p-try@^2.0.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/p-try/download/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

package-json@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/package-json/download/package-json-4.0.1.tgz#8869a0401253661c4c4ca3da6c2121ed555f5eed"
  integrity sha1-iGmgQBJTZhxMTKPabCEh7VVfXu0=
  dependencies:
    got "^6.7.1"
    registry-auth-token "^3.0.1"
    registry-url "^3.0.3"
    semver "^5.1.0"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/parent-module/download/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parent-require@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/parent-require/download/parent-require-1.0.0.tgz#746a167638083a860b0eef6732cb27ed46c32977"
  integrity sha1-dGoWdjgIOoYLDu9nMssn7UbDKXc=

parse-json@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/parse-json/download/parse-json-4.0.0.tgz#be35f5425be1f7f6c747184f98a788cb99477ee0"
  integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse5-htmlparser2-tree-adapter@^5.1.1:
  version "5.1.1"
  resolved "http://registry.npm.qima-inc.com/parse5-htmlparser2-tree-adapter/download/parse5-htmlparser2-tree-adapter-5.1.1.tgz#e8c743d4e92194d5293ecde2b08be31e67461cbc"
  integrity sha1-6MdD1OkhlNUpPs3isIvjHmdGHLw=
  dependencies:
    parse5 "^5.1.1"

parse5@^5.1.1:
  version "5.1.1"
  resolved "http://registry.npm.qima-inc.com/parse5/download/parse5-5.1.1.tgz#f68e4e5ba1852ac2cadc00f4555fff6c2abb6178"
  integrity sha1-9o5OW6GFKsLK3AD0VV//bCq7YXg=

parseurl@^1.3.0:
  version "1.3.3"
  resolved "http://registry.npm.qima-inc.com/parseurl/download/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/pascalcase/download/pascalcase-0.1.1.tgz#b363e55e8006ca6fe21784d2db22bd15d7917f14"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-dirname@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/path-dirname/download/path-dirname-1.0.2.tgz#cc33d24d525e099a5388c0336c6e32b9160609e0"
  integrity sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=

path-exists@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/path-exists/download/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/path-exists/download/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@1.0.1, path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-is-inside@^1.0.1, path-is-inside@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/path-is-inside/download/path-is-inside-1.0.2.tgz#365417dede44430d1c11af61027facf074bdfc53"
  integrity sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/path-key/download/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-matching@0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/path-matching/download/path-matching-0.0.2.tgz#2d94c47b81b59e48cc41b3b0e3d588e4d342f3fc"
  integrity sha1-LZTEe4G1nkjMQbOw49WI5NNC8/w=
  dependencies:
    path-to-regexp "^2.2.0"

path-parse@^1.0.6:
  version "1.0.6"
  resolved "http://registry.npm.qima-inc.com/path-parse/download/path-parse-1.0.6.tgz#d62dbb5679405d72c4737ec58600e9ddcf06d24c"
  integrity sha1-1i27VnlAXXLEc37FhgDp3c8G0kw=

path-to-regexp@2.2.1:
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/path-to-regexp/download/path-to-regexp-2.2.1.tgz#90b617025a16381a879bc82a38d4e8bdeb2bcf45"
  integrity sha1-kLYXAloWOBqHm8gqONTovesrz0U=

path-to-regexp@^1.1.1:
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/path-to-regexp/download/path-to-regexp-1.9.0.tgz#5dc0753acbf8521ca2e0f137b4578b917b10cf24"
  integrity sha1-XcB1Osv4Uhyi4PE3tFeLkXsQzyQ=
  dependencies:
    isarray "0.0.1"

path-to-regexp@^2.2.0:
  version "2.4.0"
  resolved "http://registry.npm.qima-inc.com/path-to-regexp/download/path-to-regexp-2.4.0.tgz#35ce7f333d5616f1c1e1bfe266c3aba2e5b2e704"
  integrity sha1-Nc5/Mz1WFvHB4b/iZsOrouWy5wQ=

pause-stream@~0.0.11:
  version "0.0.11"
  resolved "http://registry.npm.qima-inc.com/pause-stream/download/pause-stream-0.0.11.tgz#fe5a34b0cbce12b5aa6a2b403ee2e73b602f1445"
  integrity sha1-/lo0sMvOErWqaitAPuLnO2AvFEU=
  dependencies:
    through "~2.3"

performance-now@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/performance-now/download/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picomatch@^2.0.5, picomatch@^2.2.1:
  version "2.2.2"
  resolved "http://registry.npm.qima-inc.com/picomatch/download/picomatch-2.2.2.tgz#21f333e9b6b8eaff02468f5146ea406d345f4dad"
  integrity sha1-IfMz6ba46v8CRo9RRupAbTRfTa0=

picture-tuber@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/picture-tuber/download/picture-tuber-1.0.2.tgz#2f6f024a882fbd28869d0b78a8d1ab45950e6cbf"
  integrity sha1-L28CSogvvSiGnQt4qNGrRZUObL8=
  dependencies:
    buffers "~0.1.1"
    charm "~0.1.0"
    event-stream "~0.9.8"
    optimist "~0.3.4"
    png-js "~0.1.0"
    x256 "~0.0.1"

pify@^2.0.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/pify/download/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/pify/download/pify-3.0.0.tgz#e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/pinkie-promise/download/pinkie-promise-2.0.1.tgz#2135d6dfa7a358c069ac9b178776288228450ffa"
  integrity sha1-ITXW36ejWMBprJsXh3YogihFD/o=
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/pinkie/download/pinkie-2.0.4.tgz#72556b80cfa0d48a974e80e77248e80ed4f7f870"
  integrity sha1-clVrgM+g1IqXToDnckjoDtT3+HA=

pkg-dir@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/pkg-dir/download/pkg-dir-3.0.0.tgz#2749020f239ed990881b1f71210d51eb6523bea3"
  integrity sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=
  dependencies:
    find-up "^3.0.0"

please-upgrade-node@^3.0.2, please-upgrade-node@^3.1.1:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/please-upgrade-node/download/please-upgrade-node-3.2.0.tgz#aeddd3f994c933e4ad98b99d9a556efa0e2fe942"
  integrity sha1-rt3T+ZTJM+StmLmdmlVu+g4v6UI=
  dependencies:
    semver-compare "^1.0.0"

png-js@~0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/png-js/download/png-js-0.1.1.tgz#1cc7c212303acabe74263ec3ac78009580242d93"
  integrity sha1-HMfCEjA6yr50Jj7DrHgAlYAkLZM=

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz#01eac0fe3b5af71a2a6c02feabb8c1fef7e00eab"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

postinstall-build@^5.0.1:
  version "5.0.3"
  resolved "http://registry.npm.qima-inc.com/postinstall-build/download/postinstall-build-5.0.3.tgz#238692f712a481d8f5bc8960e94786036241efc7"
  integrity sha1-I4aS9xKkgdj1vIlg6UeGA2JB78c=

prebuild-install@5.3.0:
  version "5.3.0"
  resolved "http://registry.npm.qima-inc.com/prebuild-install/download/prebuild-install-5.3.0.tgz#58b4d8344e03590990931ee088dd5401b03004c8"
  integrity sha1-WLTYNE4DWQmQkx7giN1UAbAwBMg=
  dependencies:
    detect-libc "^1.0.3"
    expand-template "^2.0.3"
    github-from-package "0.0.0"
    minimist "^1.2.0"
    mkdirp "^0.5.1"
    napi-build-utils "^1.0.1"
    node-abi "^2.7.0"
    noop-logger "^0.1.1"
    npmlog "^4.0.1"
    os-homedir "^1.0.1"
    pump "^2.0.1"
    rc "^1.2.7"
    simple-get "^2.7.0"
    tar-fs "^1.13.0"
    tunnel-agent "^0.6.0"
    which-pm-runs "^1.0.0"

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/prelude-ls/download/prelude-ls-1.1.2.tgz#21932a549f5e52ffd9a827f570e04be62a97da54"
  integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=

prepend-http@^1.0.1:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/prepend-http/download/prepend-http-1.0.4.tgz#d4f4562b0ce3696e41ac52d0e002e57a635dc6dc"
  integrity sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw=

prettier@^1.18.2:
  version "1.19.1"
  resolved "http://registry.npm.qima-inc.com/prettier/download/prettier-1.19.1.tgz#f7d7f5ff8a9cd872a7be4ca142095956a60797cb"
  integrity sha1-99f1/4qc2HKnvkyhQglZVqYHl8s=

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

progress@^2.0.0:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/progress/download/progress-2.0.3.tgz#7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

properties-parser@0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/properties-parser/download/properties-parser-0.3.1.tgz#1316e9539ffbfd93845e369b211022abd478771a"
  integrity sha1-ExbpU5/7/ZOEXjabIRAiq9R4dxo=
  dependencies:
    string.prototype.codepointat "^0.2.0"

property-expr@^1.5.0:
  version "1.5.1"
  resolved "http://registry.npm.qima-inc.com/property-expr/download/property-expr-1.5.1.tgz#22e8706894a0c8e28d58735804f6ba3a3673314f"
  integrity sha1-IuhwaJSgyOKNWHNYBPa6OjZzMU8=

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/proxy-from-env/download/proxy-from-env-1.1.0.tgz#e102f16ca355424865755d2c9e8ea4f24d58c3e2"
  integrity sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/pseudomap/download/pseudomap-1.0.2.tgz#f052a28da70e618917ef0a8ac34c1ae5a68286b3"
  integrity sha1-8FKijacOYYkX7wqKw0wa5aaChrM=

pstree.remy@^1.1.7:
  version "1.1.7"
  resolved "http://registry.npm.qima-inc.com/pstree.remy/download/pstree.remy-1.1.7.tgz#c76963a28047ed61542dc361aa26ee55a7fa15f3"
  integrity sha1-x2ljooBH7WFULcNhqibuVaf6FfM=

pump@^1.0.0:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/pump/download/pump-1.0.3.tgz#5dfe8311c33bbf6fc18261f9f34702c47c08a954"
  integrity sha1-Xf6DEcM7v2/BgmH580cCxHwIqVQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pump@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/pump/download/pump-2.0.1.tgz#12399add6e4cf7526d973cbc8b5ce2e2908b3909"
  integrity sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pump@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/pump/download/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@^2.1.0:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/punycode/download/punycode-2.1.1.tgz#b58b010ac40c22c5657616c8d2c2c02c7bf479ec"
  integrity sha1-tYsBCsQMIsVldhbI0sLALHv0eew=

qs@^6.11.0:
  version "6.11.0"
  resolved "http://registry.npm.qima-inc.com/qs/download/qs-6.11.0.tgz#fd0d963446f7a65e1367e01abd85429453f0c37a"
  integrity sha1-/Q2WNEb3pl4TZ+AavYVClFPww3o=
  dependencies:
    side-channel "^1.0.4"

qs@^6.4.0, qs@^6.5.2:
  version "6.9.3"
  resolved "http://registry.npm.qima-inc.com/qs/download/qs-6.9.3.tgz#bfadcd296c2d549f1dffa560619132c977f5008e"
  integrity sha1-v63NKWwtVJ8d/6VgYZEyyXf1AI4=

query-string@5:
  version "5.1.1"
  resolved "http://registry.npm.qima-inc.com/query-string/download/query-string-5.1.1.tgz#a78c012b71c17e05f2e3fa2319dd330682efb3cb"
  integrity sha1-p4wBK3HBfgXy4/ojGd0zBoLvs8s=
  dependencies:
    decode-uri-component "^0.2.0"
    object-assign "^4.1.0"
    strict-uri-encode "^1.0.0"

query-string@^6.13.7:
  version "6.14.1"
  resolved "http://registry.npm.qima-inc.com/query-string/download/query-string-6.14.1.tgz#7ac2dca46da7f309449ba0f86b1fd28255b0c86a"
  integrity sha1-esLcpG2n8wlEm6D4ax/SglWwyGo=
  dependencies:
    decode-uri-component "^0.2.0"
    filter-obj "^1.1.0"
    split-on-first "^1.0.0"
    strict-uri-encode "^2.0.0"

querystringify@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/querystringify/download/querystringify-2.1.1.tgz#60e5a5fd64a7f8bfa4d2ab2ed6fdf4c85bad154e"
  integrity sha1-YOWl/WSn+L+k0qsu1v30yFutFU4=

raf@^3.4.1:
  version "3.4.1"
  resolved "http://registry.npm.qima-inc.com/raf/download/raf-3.4.1.tgz#0742e99a4a6552f445d73e3ee0328af0ff1ede39"
  integrity sha1-B0LpmkplUvRF1z4+4DKK8P8e3jk=
  dependencies:
    performance-now "^2.1.0"

raw-body@^2.2.0, raw-body@^2.3.3:
  version "2.4.1"
  resolved "http://registry.npm.qima-inc.com/raw-body/download/raw-body-2.4.1.tgz#30ac82f98bb5ae8c152e67149dac8d55153b168c"
  integrity sha1-MKyC+Yu1rowVLmcUnayNVRU7Fow=
  dependencies:
    bytes "3.1.0"
    http-errors "1.7.3"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

rc@^1.0.1, rc@^1.1.6, rc@^1.2.7:
  version "1.2.8"
  resolved "http://registry.npm.qima-inc.com/rc/download/rc-1.2.8.tgz#cd924bf5200a075b83c188cd6b9e211b7fc0d3ed"
  integrity sha1-zZJL9SAKB1uDwYjNa54hG3/A0+0=
  dependencies:
    deep-extend "^0.6.0"
    ini "~1.3.0"
    minimist "^1.2.0"
    strip-json-comments "~2.0.1"

read-pkg@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/read-pkg/download/read-pkg-4.0.1.tgz#963625378f3e1c4d48c85872b5a6ec7d5d093237"
  integrity sha1-ljYlN48+HE1IyFhytabsfV0JMjc=
  dependencies:
    normalize-package-data "^2.3.2"
    parse-json "^4.0.0"
    pify "^3.0.0"

readable-stream@^2.0.2, readable-stream@^2.0.6, readable-stream@^2.3.0, readable-stream@^2.3.5:
  version "2.3.7"
  resolved "http://registry.npm.qima-inc.com/readable-stream/download/readable-stream-2.3.7.tgz#1eca1cf711aef814c04f62252a36a62f6cb23b57"
  integrity sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@~1.0.2:
  version "1.0.34"
  resolved "http://registry.npm.qima-inc.com/readable-stream/download/readable-stream-1.0.34.tgz#125820e34bc842d2f2aaafafe4c2916ee32c157c"
  integrity sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readdirp@^2.2.1:
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/readdirp/download/readdirp-2.2.1.tgz#0e87622a3325aa33e892285caf8b4e846529a525"
  integrity sha1-DodiKjMlqjPokihcr4tOhGUppSU=
  dependencies:
    graceful-fs "^4.1.11"
    micromatch "^3.1.10"
    readable-stream "^2.0.2"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "http://registry.npm.qima-inc.com/rechoir/download/rechoir-0.6.2.tgz#85204b54dba82d5742e28c96756ef43af50e3384"
  integrity sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=
  dependencies:
    resolve "^1.1.6"

redeyed@~2.1.0:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/redeyed/download/redeyed-2.1.1.tgz#8984b5815d99cb220469c99eeeffe38913e6cc0b"
  integrity sha1-iYS1gV2ZyyIEacme7v/jiRPmzAs=
  dependencies:
    esprima "~4.0.0"

redis-commands@1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/redis-commands/download/redis-commands-1.4.0.tgz#52f9cf99153efcce56a8f86af986bd04e988602f"
  integrity sha1-UvnPmRU+/M5WqPhq+Ya9BOmIYC8=

redis-errors@^1.0.0, redis-errors@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/redis-errors/download/redis-errors-1.2.0.tgz#eb62d2adb15e4eaf4610c04afe1529384250abad"
  integrity sha1-62LSrbFeTq9GEMBK/hUpOEJQq60=

redis-parser@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/redis-parser/download/redis-parser-3.0.0.tgz#b66d828cdcafe6b4b8a428a7def4c6bcac31c8b4"
  integrity sha1-tm2CjNyv5rS4pCin3vTGvKwxyLQ=
  dependencies:
    redis-errors "^1.0.0"

reflect-metadata@^0.1.13:
  version "0.1.13"
  resolved "http://registry.npm.qima-inc.com/reflect-metadata/download/reflect-metadata-0.1.13.tgz#67ae3ca57c972a2aa1642b10fe363fe32d49dc08"
  integrity sha1-Z648pXyXKiqhZCsQ/jY/4y1J3Ag=

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "http://registry.npm.qima-inc.com/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz#be05ad7f9bf7d22e056f9726cee5017fbf19e2e9"
  integrity sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk=

regenerator-runtime@^0.13.4:
  version "0.13.5"
  resolved "http://registry.npm.qima-inc.com/regenerator-runtime/download/regenerator-runtime-0.13.5.tgz#d878a1d094b4306d10b9096484b33ebd55e26697"
  integrity sha1-2Hih0JS0MG0QuQlkhLM+vVXiZpc=

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/regex-not/download/regex-not-1.0.2.tgz#1f4ece27e00b0b65e0247a6810e6a85d83a5752c"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexpp@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/regexpp/download/regexpp-2.0.1.tgz#8d19d31cf632482b589049f8281f93dbcba4d07f"
  integrity sha1-jRnTHPYySCtYkEn4KB+T28uk0H8=

regexpp@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/regexpp/download/regexpp-3.1.0.tgz#206d0ad0a5648cffbdb8ae46438f3dc51c9f78e2"
  integrity sha1-IG0K0KVkjP+9uK5GQ489xRyfeOI=

registry-auth-token@^3.0.1:
  version "3.4.0"
  resolved "http://registry.npm.qima-inc.com/registry-auth-token/download/registry-auth-token-3.4.0.tgz#d7446815433f5d5ed6431cd5dca21048f66b397e"
  integrity sha1-10RoFUM/XV7WQxzV3KIQSPZrOX4=
  dependencies:
    rc "^1.1.6"
    safe-buffer "^5.0.1"

registry-url@^3.0.3:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/registry-url/download/registry-url-3.1.0.tgz#3d4ef870f73dde1d77f0cf9a381432444e174942"
  integrity sha1-PU74cPc93h138M+aOBQyRE4XSUI=
  dependencies:
    rc "^1.0.1"

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz#c24bce2a283adad5bc3f58e0d48249b92379d8ef"
  integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=

repeat-element@^1.1.2:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/repeat-element/download/repeat-element-1.1.3.tgz#782e0d825c0c5a3bb39731f84efee6b742e6b1ce"
  integrity sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4=

repeat-string@^1.6.1:
  version "1.6.1"
  resolved "http://registry.npm.qima-inc.com/repeat-string/download/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

require-directory@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/require-directory/download/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/require-main-filename/download/require-main-filename-2.0.0.tgz#d0b329ecc7cc0f61649f62215be69af54aa8989b"
  integrity sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/requires-port/download/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/resolve-from/download/resolve-from-3.0.0.tgz#b22c7af7d9d6881bc8b6e653335eebcb0a188748"
  integrity sha1-six699nWiBvItuZTM17rywoYh0g=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/resolve-from/download/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-path@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/resolve-path/download/resolve-path-1.4.0.tgz#c4bda9f5efb2fce65247873ab36bb4d834fe16f7"
  integrity sha1-xL2p9e+y/OZSR4c6s2u02DT+Fvc=
  dependencies:
    http-errors "~1.6.2"
    path-is-absolute "1.0.1"

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/resolve-url/download/resolve-url-0.2.1.tgz#2c637fe77c893afd2a663fe21aa9080068e2052a"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve@^1.1.6, resolve@^1.10.0:
  version "1.15.1"
  resolved "http://registry.npm.qima-inc.com/resolve/download/resolve-1.15.1.tgz#27bdcdeffeaf2d6244b95bb0f9f4b4653451f3e8"
  integrity sha1-J73N7/6vLWJEuVuw+fS0ZTRR8+g=
  dependencies:
    path-parse "^1.0.6"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/restore-cursor/download/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/restore-cursor/download/restore-cursor-3.1.0.tgz#39f67c54b3a7a58cea5236d95cf0034239631f7e"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "http://registry.npm.qima-inc.com/ret/download/ret-0.1.15.tgz#b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

retry-as-promised@^3.2.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/retry-as-promised/download/retry-as-promised-3.2.0.tgz#769f63d536bec4783549db0777cb56dadd9d8543"
  integrity sha1-dp9j1Ta+xHg1SdsHd8tW2t2dhUM=
  dependencies:
    any-promise "^1.3.0"

retry@^0.10.1:
  version "0.10.1"
  resolved "http://registry.npm.qima-inc.com/retry/download/retry-0.10.1.tgz#e76388d217992c252750241d3d3956fed98d8ff4"
  integrity sha1-52OI0heZLCUnUCQdPTlW/tmNj/Q=

reusify@^1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/reusify/download/reusify-1.0.4.tgz#90da382b1e126efc02146e90845a88db12925d76"
  integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=

rimraf@2.6.3:
  version "2.6.3"
  resolved "http://registry.npm.qima-inc.com/rimraf/download/rimraf-2.6.3.tgz#b2d104fe0d8fb27cf9e0a1cda8262dd3833c6cab"
  integrity sha1-stEE/g2Psnz54KHNqCYt04M8bKs=
  dependencies:
    glob "^7.1.3"

rimraf@^2.2.8:
  version "2.7.1"
  resolved "http://registry.npm.qima-inc.com/rimraf/download/rimraf-2.7.1.tgz#35797f13a7fdadc566142c29d4f07ccad483e3ec"
  integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
  dependencies:
    glob "^7.1.3"

run-async@^2.4.0:
  version "2.4.0"
  resolved "http://registry.npm.qima-inc.com/run-async/download/run-async-2.4.0.tgz#e59054a5b86876cfae07f431d18cbaddc594f1e8"
  integrity sha1-5ZBUpbhods+uB/Qx0Yy63cWU8eg=
  dependencies:
    is-promise "^2.1.0"

run-node@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/run-node/download/run-node-1.0.0.tgz#46b50b946a2aa2d4947ae1d886e9856fd9cabe5e"
  integrity sha1-RrULlGoqotSUeuHYhumFb9nKvl4=

run-parallel@^1.1.9:
  version "1.1.9"
  resolved "http://registry.npm.qima-inc.com/run-parallel/download/run-parallel-1.1.9.tgz#c9dd3a7cf9f4b2c4b6244e173a6ed866e61dd679"
  integrity sha1-yd06fPn0ssS2JE4XOm7YZuYd1nk=

rxjs@^6.3.3, rxjs@^6.5.3:
  version "6.5.5"
  resolved "http://registry.npm.qima-inc.com/rxjs/download/rxjs-6.5.5.tgz#c5c884e3094c8cfee31bf27eb87e54ccfc87f9ec"
  integrity sha1-xciE4wlMjP7jG/J+uH5UzPyH+ew=
  dependencies:
    tslib "^1.9.0"

safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://registry.npm.qima-inc.com/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@^5.0.1:
  version "5.2.0"
  resolved "http://registry.npm.qima-inc.com/safe-buffer/download/safe-buffer-5.2.0.tgz#b74daec49b1148f88c64b68d49b1e815c1f2f519"
  integrity sha1-t02uxJsRSPiMZLaNSbHoFcHy9Rk=

safe-buffer@^5.1.1:
  version "5.2.1"
  resolved "http://registry.npm.qima-inc.com/safe-buffer/download/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/safe-regex/download/safe-regex-1.1.0.tgz#40a3669f3b077d1e943d44629e157dd48023bf2e"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3":
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sax@>=0.6.0:
  version "1.2.4"
  resolved "http://registry.npm.qima-inc.com/sax/download/sax-1.2.4.tgz#2816234e2378bddc4e5354fab5caa895df7100d9"
  integrity sha1-KBYjTiN4vdxOU1T6tcqold9xANk=

semver-compare@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/semver-compare/download/semver-compare-1.0.0.tgz#0dee216a1c941ab37e9efb1788f6afc5ff5537fc"
  integrity sha1-De4hahyUGrN+nvsXiPavxf9VN/w=

semver-diff@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/semver-diff/download/semver-diff-2.1.0.tgz#4bbb8437c8d37e4b0cf1a68fd726ec6d645d6d36"
  integrity sha1-S7uEN8jTfksM8aaP1ybsbWRdbTY=
  dependencies:
    semver "^5.0.3"

"semver@2 || 3 || 4 || 5", semver@^5.0.1, semver@^5.0.3, semver@^5.1.0, semver@^5.4.1, semver@^5.5.0, semver@^5.7.1:
  version "5.7.1"
  resolved "http://registry.npm.qima-inc.com/semver/download/semver-5.7.1.tgz#a954f931aeba508d307bbf069eff0c01c96116f7"
  integrity sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=

semver@^6.1.2, semver@^6.3.0:
  version "6.3.0"
  resolved "http://registry.npm.qima-inc.com/semver/download/semver-6.3.0.tgz#ee0a64c8af5e8ceea67687b133761e1becbd1d3d"
  integrity sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=

seq-queue@^0.0.5:
  version "0.0.5"
  resolved "http://registry.npm.qima-inc.com/seq-queue/download/seq-queue-0.0.5.tgz#d56812e1c017a6e4e7c3e3a37a1da6d78dd3c93e"
  integrity sha1-1WgS4cAXpuTnw+Ojeh2m143TyT4=

sequelize-pool@^2.3.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/sequelize-pool/download/sequelize-pool-2.3.0.tgz#64f1fe8744228172c474f530604b6133be64993d"
  integrity sha1-ZPH+h0QigXLEdPUwYEthM75kmT0=

sequelize@5.21.5:
  version "5.21.5"
  resolved "http://registry.npm.qima-inc.com/sequelize/download/sequelize-5.21.5.tgz#44056f3ab8862ccbfeebd5e03ce041c570477ea2"
  integrity sha1-RAVvOriGLMv+69XgPOBBxXBHfqI=
  dependencies:
    bluebird "^3.5.0"
    cls-bluebird "^2.1.0"
    debug "^4.1.1"
    dottie "^2.0.0"
    inflection "1.12.0"
    lodash "^4.17.15"
    moment "^2.24.0"
    moment-timezone "^0.5.21"
    retry-as-promised "^3.2.0"
    semver "^6.3.0"
    sequelize-pool "^2.3.0"
    toposort-class "^1.0.1"
    uuid "^3.3.3"
    validator "^10.11.0"
    wkx "^0.4.8"

set-blocking@^2.0.0, set-blocking@~2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/set-blocking/download/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/set-value/download/set-value-2.0.1.tgz#a18d40530e6f07de4228c7defe4227af8cad005b"
  integrity sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/setprototypeof/download/setprototypeof-1.1.0.tgz#d0bd85536887b6fe7c0d818cb962d9d91c54e656"
  integrity sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=

setprototypeof@1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/setprototypeof/download/setprototypeof-1.1.1.tgz#7e95acb24aa92f5885e0abef5ba131330d4ae683"
  integrity sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM=

sha.js@^2.4.11:
  version "2.4.11"
  resolved "http://registry.npm.qima-inc.com/sha.js/download/sha.js-2.4.11.tgz#37a5cf0b81ecbc6943de109ba2960d1b26584ae7"
  integrity sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/shebang-command/download/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/shebang-regex/download/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shelljs@^0.8.2:
  version "0.8.3"
  resolved "http://registry.npm.qima-inc.com/shelljs/download/shelljs-0.8.3.tgz#a7f3319520ebf09ee81275b2368adb286659b097"
  integrity sha1-p/MxlSDr8J7oEnWyNorbKGZZsJc=
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

shimmer@^1.1.0:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/shimmer/download/shimmer-1.2.1.tgz#610859f7de327b587efebf501fb43117f9aff337"
  integrity sha1-YQhZ994ye1h+/r9QH7QxF/mv8zc=

side-channel@^1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/side-channel/download/side-channel-1.0.4.tgz#efce5c8fdc104ee751b25c58d4290011fa5ea2cf"
  integrity sha1-785cj9wQTudRslxY1CkAEfpeos8=
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/signal-exit/download/signal-exit-3.0.3.tgz#a1410c2edd8f077b08b4e253c8eacfcaf057461c"
  integrity sha1-oUEMLt2PB3sItOJTyOrPyvBXRhw=

simple-concat@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/simple-concat/download/simple-concat-1.0.1.tgz#f46976082ba35c2263f1c8ab5edfe26c41c9552f"
  integrity sha1-9Gl2CCujXCJj8cirXt/ibEHJVS8=

simple-get@^2.7.0:
  version "2.8.2"
  resolved "http://registry.npm.qima-inc.com/simple-get/download/simple-get-2.8.2.tgz#5708fb0919d440657326cd5fe7d2599d07705019"
  integrity sha1-Vwj7CRnUQGVzJs1f59JZnQdwUBk=
  dependencies:
    decompress-response "^3.3.0"
    once "^1.3.1"
    simple-concat "^1.0.0"

simple-git@^1.85.0:
  version "1.132.0"
  resolved "http://registry.npm.qima-inc.com/simple-git/download/simple-git-1.132.0.tgz#53ac4c5ec9e74e37c2fd461e23309f22fcdf09b1"
  integrity sha1-U6xMXsnnTjfC/UYeIzCfIvzfCbE=
  dependencies:
    debug "^4.0.1"

slash@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/slash/download/slash-2.0.0.tgz#de552851a1759df3a8f206535442f5ec4ddeab44"
  integrity sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=

slice-ansi@0.0.4:
  version "0.0.4"
  resolved "http://registry.npm.qima-inc.com/slice-ansi/download/slice-ansi-0.0.4.tgz#edbf8903f66f7ce2f8eafd6ceed65e264c831b35"
  integrity sha1-7b+JA/ZvfOL46v1s7tZeJkyDGzU=

slice-ansi@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/slice-ansi/download/slice-ansi-2.1.0.tgz#cacd7693461a637a5788d92a7dd4fba068e81636"
  integrity sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY=
  dependencies:
    ansi-styles "^3.2.0"
    astral-regex "^1.0.0"
    is-fullwidth-code-point "^2.0.0"

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz#6c175f86ff14bdb0724563e8f3c1b021a286853b"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz#f956479486f2acd79700693f6f7b805e45ab56e2"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "http://registry.npm.qima-inc.com/snapdragon/download/snapdragon-0.8.2.tgz#64922e7c565b0e14204ba1aa7d6964278d25182d"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

snappy@^6.0.1, snappy@^6.3.5:
  version "6.3.5"
  resolved "http://registry.npm.qima-inc.com/snappy/download/snappy-6.3.5.tgz#c14b8dea8e9bc2687875b5e491d15dd900e6023c"
  integrity sha1-wUuN6o6bwmh4dbXkkdFd2QDmAjw=
  dependencies:
    bindings "^1.3.1"
    nan "^2.14.1"
    prebuild-install "5.3.0"

snappystream@^1.4.0:
  version "1.5.0"
  resolved "http://registry.npm.qima-inc.com/snappystream/download/snappystream-1.5.0.tgz#e19ac455968ee1f7d801e1e050a0dacdae2ed2ca"
  integrity sha1-4ZrEVZaO4ffYAeHgUKDaza4u0so=
  dependencies:
    async "^3.2.0"
    fast-crc32c "^2.0.0"
    int24 "0.0.1"
    snappy "^6.3.5"

snappystream@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/snappystream/download/snappystream-2.0.0.tgz#ab6a60a185281ee60aad2328e381a28e937f43cd"
  integrity sha1-q2pgoYUoHuYKrSMo44GijpN/Q80=
  dependencies:
    "@napi-rs/snappy" "^1.0.1"
    "@node-rs/crc32" "^1.1.2"
    int24 "0.0.1"

sorted-array-functions@^1.0.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/sorted-array-functions/download/sorted-array-functions-1.2.0.tgz#43265b21d6e985b7df31621b1c11cc68d8efc7c3"
  integrity sha1-QyZbIdbphbffMWIbHBHMaNjvx8M=

source-map-resolve@^0.5.0:
  version "0.5.3"
  resolved "http://registry.npm.qima-inc.com/source-map-resolve/download/source-map-resolve-0.5.3.tgz#190866bece7553e1f8f267a2ee82c606b5509a1a"
  integrity sha1-GQhmvs51U+H48mei7oLGBrVQmho=
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@^0.5.6:
  version "0.5.16"
  resolved "http://registry.npm.qima-inc.com/source-map-support/download/source-map-support-0.5.16.tgz#0ae069e7fe3ba7538c64c98515e35339eac5a042"
  integrity sha1-CuBp5/47p1OMZMmFFeNTOerFoEI=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.0"
  resolved "http://registry.npm.qima-inc.com/source-map-url/download/source-map-url-0.4.0.tgz#3e935d7ddd73631b97659956d55128e87b5084a3"
  integrity sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=

source-map@^0.5.6:
  version "0.5.7"
  resolved "http://registry.npm.qima-inc.com/source-map/download/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.0:
  version "0.6.1"
  resolved "http://registry.npm.qima-inc.com/source-map/download/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

sparkline@^0.1.1:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/sparkline/download/sparkline-0.1.2.tgz#c3bde46252b1354e710c4b200d54816bd9f07a32"
  integrity sha1-w73kYlKxNU5xDEsgDVSBa9nwejI=
  dependencies:
    here "0.0.2"
    nopt "~2.1.2"

spdx-correct@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/spdx-correct/download/spdx-correct-3.1.0.tgz#fb83e504445268f154b074e218c87c003cd31df4"
  integrity sha1-+4PlBERSaPFUsHTiGMh8ADzTHfQ=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/spdx-exceptions/download/spdx-exceptions-2.2.0.tgz#2ea450aee74f2a89bfb94519c07fcd6f41322977"
  integrity sha1-LqRQrudPKom/uUUZwH/Nb0EyKXc=

spdx-expression-parse@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/spdx-expression-parse/download/spdx-expression-parse-3.0.0.tgz#99e119b7a5da00e05491c9fa338b7904823b41d0"
  integrity sha1-meEZt6XaAOBUkcn6M4t5BII7QdA=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.5"
  resolved "http://registry.npm.qima-inc.com/spdx-license-ids/download/spdx-license-ids-3.0.5.tgz#3694b5804567a458d3c8045842a6358632f62654"
  integrity sha1-NpS1gEVnpFjTyARYQqY1hjL2JlQ=

split-on-first@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/split-on-first/download/split-on-first-1.1.0.tgz#f610afeee3b12bce1d0c30425e76398b78249a5f"
  integrity sha1-9hCv7uOxK84dDDBCXnY5i3gkml8=

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/split-string/download/split-string-3.1.0.tgz#7cb09dda3a86585705c64b39a6466038682e8fe2"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/sprintf-js/download/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

sqlstring@^2.3.1:
  version "2.3.2"
  resolved "http://registry.npm.qima-inc.com/sqlstring/download/sqlstring-2.3.2.tgz#cdae7169389a1375b18e885f2e60b3e460809514"
  integrity sha1-za5xaTiaE3WxjohfLmCz5GCAlRQ=

sse4_crc32@^6.0.1:
  version "6.0.1"
  resolved "http://registry.npm.qima-inc.com/sse4_crc32/download/sse4_crc32-6.0.1.tgz#3511c747ce48a224e0554ebb23d5835ba08a9637"
  integrity sha1-NRHHR85IoiTgVU67I9WDW6CKljc=
  dependencies:
    bindings "^1.3.0"
    node-addon-api "^1.3.0"

staged-git-files@1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/staged-git-files/download/staged-git-files-1.1.2.tgz#4326d33886dc9ecfa29a6193bf511ba90a46454b"
  integrity sha1-QybTOIbcns+immGTv1EbqQpGRUs=

standard-as-callback@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/standard-as-callback/download/standard-as-callback-1.0.2.tgz#d0813289db00f8bd5e0f29e74744cb63706707c8"
  integrity sha1-0IEyidsA+L1eDynnR0TLY3BnB8g=

static-extend@^0.1.1:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/static-extend/download/static-extend-0.1.2.tgz#60809c39cbff55337226fd5e0b520f341f1fb5c6"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", statuses@^1.2.0, statuses@^1.3.1:
  version "1.5.0"
  resolved "http://registry.npm.qima-inc.com/statuses/download/statuses-1.5.0.tgz#161c7dac177659fd9811f43771fa99381478628c"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

strict-uri-encode@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz#279b225df1d582b1f54e65addd4352e18faa0713"
  integrity sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM=

strict-uri-encode@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/strict-uri-encode/download/strict-uri-encode-2.0.0.tgz#b9c7330c7042862f6b142dc274bbcc5866ce3546"
  integrity sha1-ucczDHBChi9rFC3CdLvMWGbONUY=

string-argv@^0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/string-argv/download/string-argv-0.0.2.tgz#dac30408690c21f3c3630a3ff3a05877bdcbd736"
  integrity sha1-2sMECGkMIfPDYwo/86BYd73L1zY=

string-width@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/string-width/download/string-width-1.0.2.tgz#118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3"
  integrity sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

"string-width@^1.0.2 || 2 || 3 || 4":
  version "4.2.3"
  resolved "http://registry.npm.qima-inc.com/string-width/download/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^2.0.0, string-width@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/string-width/download/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^3.0.0, string-width@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/string-width/download/string-width-3.1.0.tgz#22767be21b62af1081574306f69ac51b62203961"
  integrity sha1-InZ74htirxCBV0MG9prFG2IgOWE=
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string-width@^4.1.0, string-width@^4.2.0:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/string-width/download/string-width-4.2.0.tgz#952182c46cc7b2c313d1596e623992bd163b72b5"
  integrity sha1-lSGCxGzHssMT0VluYjmSvRY7crU=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.0"

string.prototype.codepointat@^0.2.0:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/string.prototype.codepointat/download/string.prototype.codepointat-0.2.1.tgz#004ad44c8afc727527b108cd462b4d971cd469bc"
  integrity sha1-AErUTIr8cnUnsQjNRitNlxzUabw=

string_decoder@~0.10.x:
  version "0.10.31"
  resolved "http://registry.npm.qima-inc.com/string_decoder/download/string_decoder-0.10.31.tgz#62e203bc41766c6c28c9fc84301dab1c5310fa94"
  integrity sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/string_decoder/download/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

stringify-object@^3.2.2:
  version "3.3.0"
  resolved "http://registry.npm.qima-inc.com/stringify-object/download/stringify-object-3.3.0.tgz#703065aefca19300d3ce88af4f5b3956d7556629"
  integrity sha1-cDBlrvyhkwDTzoivT1s5VtdVZik=
  dependencies:
    get-own-enumerable-property-symbols "^3.0.0"
    is-obj "^1.0.1"
    is-regexp "^1.0.0"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/strip-ansi/download/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-ansi/download/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^5.0.0, strip-ansi@^5.1.0, strip-ansi@^5.2.0:
  version "5.2.0"
  resolved "http://registry.npm.qima-inc.com/strip-ansi/download/strip-ansi-5.2.0.tgz#8c9a536feb6afc962bdfa5b104a5091c1ad9c0ae"
  integrity sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-ansi/download/strip-ansi-6.0.0.tgz#0b1571dd7669ccd4f3e06e14ef1eed26225ae532"
  integrity sha1-CxVx3XZpzNTz4G4U7x7tJiJa5TI=
  dependencies:
    ansi-regex "^5.0.0"

strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://registry.npm.qima-inc.com/strip-ansi/download/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-bom/download/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-eof/download/strip-eof-1.0.0.tgz#bb43ff5598a6eb05d89b59fcd129c983313606bf"
  integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=

strip-json-comments@^3.0.1:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/strip-json-comments/download/strip-json-comments-3.1.0.tgz#7638d31422129ecf4457440009fba03f9f9ac180"
  integrity sha1-djjTFCISns9EV0QACfugP5+awYA=

strip-json-comments@~2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz#3c531942e908c2697c0ec344858c286c7ca0a60a"
  integrity sha1-PFMZQukIwml8DsNEhYwobHygpgo=

supports-color@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/supports-color/download/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"
  integrity sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=

supports-color@^5.3.0, supports-color@^5.5.0:
  version "5.5.0"
  resolved "http://registry.npm.qima-inc.com/supports-color/download/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.0.0, supports-color@^7.1.0:
  version "7.1.0"
  resolved "http://registry.npm.qima-inc.com/supports-color/download/supports-color-7.1.0.tgz#68e32591df73e25ad1c4b49108a2ec507962bfd1"
  integrity sha1-aOMlkd9z4lrRxLSRCKLsUHliv9E=
  dependencies:
    has-flag "^4.0.0"

supports-hyperlinks@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/supports-hyperlinks/download/supports-hyperlinks-2.1.0.tgz#f663df252af5f37c5d49bbd7eeefa9e0b9e59e47"
  integrity sha1-9mPfJSr183xdSbvX7u+p4Lnlnkc=
  dependencies:
    has-flag "^4.0.0"
    supports-color "^7.0.0"

symbol-observable@^1.1.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/symbol-observable/download/symbol-observable-1.2.0.tgz#c22688aed4eab3cdc2dfeacbb561660560a00804"
  integrity sha1-wiaIrtTqs83C3+rLtWFmBWCgCAQ=

sync-rpc@^1.3.6:
  version "1.3.6"
  resolved "http://registry.npm.qima-inc.com/sync-rpc/download/sync-rpc-1.3.6.tgz#b2e8b2550a12ccbc71df8644810529deb68665a7"
  integrity sha1-suiyVQoSzLxx34ZEgQUp3raGZac=
  dependencies:
    get-port "^3.1.0"

synchronous-promise@^2.0.6:
  version "2.0.10"
  resolved "http://registry.npm.qima-inc.com/synchronous-promise/download/synchronous-promise-2.0.10.tgz#e64c6fd3afd25f423963353043f4a68ebd397fd8"
  integrity sha1-5kxv06/SX0I5YzUwQ/Smjr05f9g=

table@^5.2.3:
  version "5.4.6"
  resolved "http://registry.npm.qima-inc.com/table/download/table-5.4.6.tgz#1292d19500ce3f86053b05f0e8e7e4a3bb21079e"
  integrity sha1-EpLRlQDOP4YFOwXw6Ofko7shB54=
  dependencies:
    ajv "^6.10.2"
    lodash "^4.17.14"
    slice-ansi "^2.1.0"
    string-width "^3.0.0"

tar-fs@^1.13.0:
  version "1.16.3"
  resolved "http://registry.npm.qima-inc.com/tar-fs/download/tar-fs-1.16.3.tgz#966a628841da2c4010406a82167cbd5e0c72d509"
  integrity sha1-lmpiiEHaLEAQQGqCFny9Xgxy1Qk=
  dependencies:
    chownr "^1.0.1"
    mkdirp "^0.5.1"
    pump "^1.0.0"
    tar-stream "^1.1.2"

tar-stream@^1.1.2:
  version "1.6.2"
  resolved "http://registry.npm.qima-inc.com/tar-stream/download/tar-stream-1.6.2.tgz#8ea55dab37972253d9a9af90fdcd559ae435c555"
  integrity sha1-jqVdqzeXIlPZqa+Q/c1VmuQ1xVU=
  dependencies:
    bl "^1.0.0"
    buffer-alloc "^1.2.0"
    end-of-stream "^1.0.0"
    fs-constants "^1.0.0"
    readable-stream "^2.3.0"
    to-buffer "^1.1.1"
    xtend "^4.0.0"

term-canvas@0.0.5:
  version "0.0.5"
  resolved "http://registry.npm.qima-inc.com/term-canvas/download/term-canvas-0.0.5.tgz#597afac2fa6369a6f17860bce9c5f66d6ea0ca96"
  integrity sha1-WXr6wvpjaabxeGC86cX2bW6gypY=

term-size@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/term-size/download/term-size-1.2.0.tgz#458b83887f288fc56d6fffbfad262e26638efa69"
  integrity sha1-RYuDiH8oj8Vtb/+/rSYuJmOO+mk=
  dependencies:
    execa "^0.7.0"

text-table@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.qima-inc.com/text-table/download/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "http://registry.npm.qima-inc.com/thenify-all/download/thenify-all-1.6.0.tgz#1a1918d402d8fc3f98fbf234db0bcc8cc10e9726"
  integrity sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.0"
  resolved "http://registry.npm.qima-inc.com/thenify/download/thenify-3.3.0.tgz#e69e38a1babe969b0108207978b9f62b88604839"
  integrity sha1-5p44obq+lpsBCCB5eLn2K4hgSDk=
  dependencies:
    any-promise "^1.0.0"

through@^2.3.6, through@~2.3:
  version "2.3.8"
  resolved "http://registry.npm.qima-inc.com/through/download/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

timed-out@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/timed-out/download/timed-out-4.0.1.tgz#f32eacac5a175bea25d7fab565ab3ed8741ef56f"
  integrity sha1-8y6srFoXW+ol1/q1Zas+2HQe9W8=

tmp@^0.0.33:
  version "0.0.33"
  resolved "http://registry.npm.qima-inc.com/tmp/download/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

to-buffer@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/to-buffer/download/to-buffer-1.1.1.tgz#493bd48f62d7c43fcded313a03dcadb2e1213a80"
  integrity sha1-STvUj2LXxD/N7TE6A9ytsuEhOoA=

to-fast-properties@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/to-fast-properties/download/to-fast-properties-1.0.3.tgz#b83571fa4d8c25b82e231b06e3a3055de4ca1a47"
  integrity sha1-uDVx+k2MJbguIxsG46MFXeTKGkc=

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/to-object-path/download/to-object-path-0.3.0.tgz#297588b7b0e7e0ac08e04e672f85c1f4999e17af"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/to-regex-range/download/to-regex-range-2.1.1.tgz#7c80c17b9dfebe599e27367e0d4dd5590141db38"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/to-regex-range/download/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/to-regex/download/to-regex-3.0.2.tgz#13cfdd9b336552f30b51f33a8ae1b42a7a7599ce"
  integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toidentifier@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/toidentifier/download/toidentifier-1.0.0.tgz#7e1be3470f1e77948bc43d94a3c8f4d7752ba553"
  integrity sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM=

toposort-class@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/toposort-class/download/toposort-class-1.0.1.tgz#7ffd1f78c8be28c3ba45cd4e1a3f5ee193bd9988"
  integrity sha1-f/0feMi+KMO6Rc1OGj9e4ZO9mYg=

toposort@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/toposort/download/toposort-2.0.2.tgz#ae21768175d1559d48bef35420b2f4962f09c330"
  integrity sha1-riF2gXXRVZ1IvvNUILL0li8JwzA=

touch@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/touch/download/touch-3.1.0.tgz#fe365f5f75ec9ed4e56825e0bb76d24ab74af83b"
  integrity sha1-/jZfX3XsntTlaCXgu3bSSrdK+Ds=
  dependencies:
    nopt "~1.0.10"

tr46@~0.0.3:
  version "0.0.3"
  resolved "http://registry.npm.qima-inc.com/tr46/download/tr46-0.0.3.tgz#8184fd347dac9cdc185992f3a6622e14b9d9ab6a"
  integrity sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=

"traverse@>=0.3.0 <0.4":
  version "0.3.9"
  resolved "http://registry.npm.qima-inc.com/traverse/download/traverse-0.3.9.tgz#717b8f220cc0bb7b44e40514c22b2e8bbc70d8b9"
  integrity sha1-cXuPIgzAu3tE5AUUwisui7xw2Lk=

ts-node@^7.0.0:
  version "7.0.1"
  resolved "http://registry.npm.qima-inc.com/ts-node/download/ts-node-7.0.1.tgz#9562dc2d1e6d248d24bc55f773e3f614337d9baf"
  integrity sha1-lWLcLR5tJI0kvFX3c+P2FDN9m68=
  dependencies:
    arrify "^1.0.0"
    buffer-from "^1.1.0"
    diff "^3.1.0"
    make-error "^1.1.1"
    minimist "^1.2.0"
    mkdirp "^0.5.1"
    source-map-support "^0.5.6"
    yn "^2.0.0"

tsconfig-paths@^3.4.2:
  version "3.9.0"
  resolved "http://registry.npm.qima-inc.com/tsconfig-paths/download/tsconfig-paths-3.9.0.tgz#098547a6c4448807e8fcb8eae081064ee9a3c90b"
  integrity sha1-CYVHpsREiAfo/Ljq4IEGTumjyQs=
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.1"
    minimist "^1.2.0"
    strip-bom "^3.0.0"

tslib@1.10.0:
  version "1.10.0"
  resolved "http://registry.npm.qima-inc.com/tslib/download/tslib-1.10.0.tgz#c3c19f95973fb0a62973fb09d90d961ee43e5c8a"
  integrity sha1-w8GflZc/sKYpc/sJ2Q2WHuQ+XIo=

tslib@1.9.3:
  version "1.9.3"
  resolved "http://registry.npm.qima-inc.com/tslib/download/tslib-1.9.3.tgz#d7e4dd79245d85428c4d7e4822a79917954ca286"
  integrity sha1-1+TdeSRdhUKMTX5IIqeZF5VMooY=

tslib@^1.10.0, tslib@^1.8.1, tslib@^1.9.0, tslib@^1.9.3:
  version "1.11.1"
  resolved "http://registry.npm.qima-inc.com/tslib/download/tslib-1.11.1.tgz#eb15d128827fbee2841549e171f45ed338ac7e35"
  integrity sha1-6xXRKIJ/vuKEFUnhcfRe0zisfjU=

tslib@^2.0.3, tslib@^2.4.0, tslib@^2.5.0:
  version "2.5.0"
  resolved "http://registry.npm.qima-inc.com/tslib/download/tslib-2.5.0.tgz#42bfed86f5787aeb41d031866c8f402429e0fddf"
  integrity sha1-Qr/thvV4eutB0DGGbI9AJCng/d8=

tsutils@^3.17.1:
  version "3.17.1"
  resolved "http://registry.npm.qima-inc.com/tsutils/download/tsutils-3.17.1.tgz#ed719917f11ca0dee586272b2ac49e015a2dd759"
  integrity sha1-7XGZF/EcoN7lhicrKsSeAVot11k=
  dependencies:
    tslib "^1.8.1"

tunnel-agent@0.6.0, tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "http://registry.npm.qima-inc.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

type-check@~0.3.2:
  version "0.3.2"
  resolved "http://registry.npm.qima-inc.com/type-check/download/type-check-0.3.2.tgz#5884cab512cf1d355e3fb784f30804b2b520db72"
  integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
  dependencies:
    prelude-ls "~1.1.2"

type-fest@^0.11.0:
  version "0.11.0"
  resolved "http://registry.npm.qima-inc.com/type-fest/download/type-fest-0.11.0.tgz#97abf0872310fed88a5c466b25681576145e33f1"
  integrity sha1-l6vwhyMQ/tiKXEZrJWgVdhReM/E=

type-fest@^0.8.1:
  version "0.8.1"
  resolved "http://registry.npm.qima-inc.com/type-fest/download/type-fest-0.8.1.tgz#09e249ebde851d3b1e48d27c105444667f17b83d"
  integrity sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=

type-is@^1.5.5, type-is@^1.6.14, type-is@^1.6.16:
  version "1.6.18"
  resolved "http://registry.npm.qima-inc.com/type-is/download/type-is-1.6.18.tgz#4e552cd05df09467dcbc4ef739de89f2cf37c131"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typeorm@^0.2.25:
  version "0.2.25"
  resolved "http://registry.npm.qima-inc.com/typeorm/download/typeorm-0.2.25.tgz#1a33513b375b78cc7740d2405202208b918d7dde"
  integrity sha1-GjNROzdbeMx3QNJAUgIgi5GNfd4=
  dependencies:
    app-root-path "^3.0.0"
    buffer "^5.1.0"
    chalk "^2.4.2"
    cli-highlight "^2.0.0"
    debug "^4.1.1"
    dotenv "^6.2.0"
    glob "^7.1.2"
    js-yaml "^3.13.1"
    mkdirp "^1.0.3"
    reflect-metadata "^0.1.13"
    sha.js "^2.4.11"
    tslib "^1.9.0"
    xml2js "^0.4.17"
    yargonaut "^1.1.2"
    yargs "^13.2.1"

typescript@^2.9.2:
  version "2.9.2"
  resolved "http://registry.npm.qima-inc.com/typescript/download/typescript-2.9.2.tgz#1cbf61d05d6b96269244eb6a3bce4bd914e0f00c"
  integrity sha1-HL9h0F1rliaSROtqO85L2RTg8Aw=

typescript@^3.3.3333:
  version "3.8.3"
  resolved "http://registry.npm.qima-inc.com/typescript/download/typescript-3.8.3.tgz#409eb8544ea0335711205869ec458ab109ee1061"
  integrity sha1-QJ64VE6gM1cRIFhp7EWKsQnuEGE=

undefsafe@^2.0.2:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/undefsafe/download/undefsafe-2.0.3.tgz#6b166e7094ad46313b2202da7ecc2cd7cc6e7aae"
  integrity sha1-axZucJStRjE7IgLafsws18xueq4=
  dependencies:
    debug "^2.2.0"

union-value@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/union-value/download/union-value-1.0.1.tgz#0b6fe7b835aecda61c6ea4d4f02c14221e109847"
  integrity sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

unique-string@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/unique-string/download/unique-string-1.0.0.tgz#9e1057cca851abb93398f8b33ae187b99caec11a"
  integrity sha1-nhBXzKhRq7kzmPizOuGHuZyuwRo=
  dependencies:
    crypto-random-string "^1.0.0"

universalify@^0.1.0:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/universalify/download/universalify-0.1.2.tgz#b646f69be3942dabcecc9d6639c80dc105efaa66"
  integrity sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=

unpipe@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/unpipe/download/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/unset-value/download/unset-value-1.0.0.tgz#8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

unzip-response@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/unzip-response/download/unzip-response-2.0.1.tgz#d2f0f737d16b0615e72a6935ed04214572d56f97"
  integrity sha1-0vD3N9FrBhXnKmk17QQhRXLVb5c=

upath@^1.1.1:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/upath/download/upath-1.2.0.tgz#8f66dbcd55a883acdae4408af8b035a5044c1894"
  integrity sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=

update-notifier@^2.5.0:
  version "2.5.0"
  resolved "http://registry.npm.qima-inc.com/update-notifier/download/update-notifier-2.5.0.tgz#d0744593e13f161e406acb1d9408b72cad08aff6"
  integrity sha1-0HRFk+E/Fh5AassdlAi3LK0Ir/Y=
  dependencies:
    boxen "^1.2.1"
    chalk "^2.0.1"
    configstore "^3.0.0"
    import-lazy "^2.1.0"
    is-ci "^1.0.10"
    is-installed-globally "^0.1.0"
    is-npm "^1.0.0"
    latest-version "^3.0.0"
    semver-diff "^2.0.0"
    xdg-basedir "^3.0.0"

uri-js@^4.2.2:
  version "4.2.2"
  resolved "http://registry.npm.qima-inc.com/uri-js/download/uri-js-4.2.2.tgz#94c540e1ff772956e2299507c010aea6c8838eb0"
  integrity sha1-lMVA4f93KVbiKZUHwBCupsiDjrA=
  dependencies:
    punycode "^2.1.0"

urijs@^1.19.0:
  version "1.19.2"
  resolved "http://registry.npm.qima-inc.com/urijs/download/urijs-1.19.2.tgz#f9be09f00c4c5134b7cb3cf475c1dd394526265a"
  integrity sha1-+b4J8AxMUTS3yzz0dcHdOUUmJlo=

urix@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/urix/download/urix-0.1.0.tgz#da937f7a62e21fec1fd18d49b35c2935067a6c72"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

url-parse-lax@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/url-parse-lax/download/url-parse-lax-1.0.0.tgz#7af8f303645e9bd79a272e7a14ac68bc0609da73"
  integrity sha1-evjzA2Rem9eaJy56FKxovAYJ2nM=
  dependencies:
    prepend-http "^1.0.1"

url-parse@^1.4.4:
  version "1.4.7"
  resolved "http://registry.npm.qima-inc.com/url-parse/download/url-parse-1.4.7.tgz#a8a83535e8c00a316e403a5db4ac1b9b853ae278"
  integrity sha1-qKg1NejACjFuQDpdtKwbm4U64ng=
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

url-parse@^1.4.7:
  version "1.5.10"
  resolved "http://registry.npm.qima-inc.com/url-parse/download/url-parse-1.5.10.tgz#9d3c2f736c1d75dd3bd2be507dcc111f1e2ea9c1"
  integrity sha1-nTwvc2wddd070r5QfcwRHx4uqcE=
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

urllib@2.22.0:
  version "2.22.0"
  resolved "http://registry.npm.qima-inc.com/urllib/download/urllib-2.22.0.tgz#2965dc4ae127a6fb695b7db27d3184f17d82cb42"
  integrity sha1-KWXcSuEnpvtpW32yfTGE8X2Cy0I=
  dependencies:
    any-promise "^1.3.0"
    content-type "^1.0.2"
    debug "^2.6.0"
    default-user-agent "^1.0.0"
    digest-header "^0.0.1"
    ee-first "~1.1.1"
    humanize-ms "^1.2.0"
    iconv-lite "^0.4.15"
    qs "^6.4.0"
    statuses "^1.3.1"

use@^3.1.0:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/use/download/use-3.1.1.tgz#d50c8cac79a19fbc20f2911f56eb973f4e10070f"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

utility-types@^3.7.0:
  version "3.10.0"
  resolved "http://registry.npm.qima-inc.com/utility-types/download/utility-types-3.10.0.tgz#ea4148f9a741015f05ed74fd615e1d20e6bed82b"
  integrity sha1-6kFI+adBAV8F7XT9YV4dIOa+2Cs=

utility@0.1.11:
  version "0.1.11"
  resolved "http://registry.npm.qima-inc.com/utility/download/utility-0.1.11.tgz#fde60cf9b4e4751947a0cf5d104ce29367226715"
  integrity sha1-/eYM+bTkdRlHoM9dEEzik2ciZxU=
  dependencies:
    address ">=0.0.1"

uuid@^3.0.0, uuid@^3.3.3:
  version "3.4.0"
  resolved "http://registry.npm.qima-inc.com/uuid/download/uuid-3.4.0.tgz#b23e4358afa8a202fe7a100af1f5f883f02007ee"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

uuid@^9.0.0:
  version "9.0.0"
  resolved "http://registry.npm.qima-inc.com/uuid/download/uuid-9.0.0.tgz#592f550650024a38ceb0c562f2f6aa435761efb5"
  integrity sha1-WS9VBlACSjjOsMVi8vaqQ1dh77U=

v8-compile-cache@^2.0.3:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/v8-compile-cache/download/v8-compile-cache-2.1.0.tgz#e14de37b31a6d194f5690d67efc4e7f6fc6ab30e"
  integrity sha1-4U3jezGm0ZT1aQ1n78Tn9vxqsw4=

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "http://registry.npm.qima-inc.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

validator@^10.11.0:
  version "10.11.0"
  resolved "http://registry.npm.qima-inc.com/validator/download/validator-10.11.0.tgz#003108ea6e9a9874d31ccc9e5006856ccd76b228"
  integrity sha1-ADEI6m6amHTTHMyeUAaFbM12sig=

vary@^1.0.0:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/vary/download/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/wcwidth/download/wcwidth-1.0.1.tgz#f0b0dcf915bc5ff1528afadb2c0e17b532da2fe8"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/webidl-conversions/download/webidl-conversions-3.0.1.tgz#24534275e2a7bc6be7bc86611cc16ae0a5654871"
  integrity sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/whatwg-url/download/whatwg-url-5.0.0.tgz#966454e8765462e37644d3626f6742ce8b70965d"
  integrity sha1-lmRU6HZUYuN2RNNib2dCzotwll0=
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which-module@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/which-module/download/which-module-2.0.0.tgz#d9ef07dce77b9902b8a3a8fa4b31c3e3f7e6e87a"
  integrity sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=

which-pm-runs@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/which-pm-runs/download/which-pm-runs-1.0.0.tgz#670b3afbc552e0b55df6b7780ca74615f23ad1cb"
  integrity sha1-Zws6+8VS4LVd9rd4DKdGFfI60cs=

which@^1.2.10, which@^1.2.9:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/which/download/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.0:
  version "1.1.5"
  resolved "http://registry.npm.qima-inc.com/wide-align/download/wide-align-1.1.5.tgz#df1d4c206854369ecf3c9a4898f1b23fbd9d15d3"
  integrity sha1-3x1MIGhUNp7PPJpImPGyP72dFdM=
  dependencies:
    string-width "^1.0.2 || 2 || 3 || 4"

widest-line@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/widest-line/download/widest-line-2.0.1.tgz#7438764730ec7ef4381ce4df82fb98a53142a3fc"
  integrity sha1-dDh2RzDsfvQ4HOTfgvuYpTFCo/w=
  dependencies:
    string-width "^2.1.1"

win-release@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/win-release/download/win-release-1.1.1.tgz#5fa55e02be7ca934edfc12665632e849b72e5209"
  integrity sha1-X6VeAr58qTTt/BJmVjLoSbcuUgk=
  dependencies:
    semver "^5.0.1"

window-size@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/window-size/download/window-size-0.1.4.tgz#f8e1aa1ee5a53ec5bf151ffa09742a6ad7697876"
  integrity sha1-+OGqHuWlPsW/FR/6CXQqatdpeHY=

wkx@^0.4.8:
  version "0.4.8"
  resolved "http://registry.npm.qima-inc.com/wkx/download/wkx-0.4.8.tgz#a092cf088d112683fdc7182fd31493b2c5820003"
  integrity sha1-oJLPCI0RJoP9xxgv0xSTssWCAAM=
  dependencies:
    "@types/node" "*"

word-wrap@~1.2.3:
  version "1.2.3"
  resolved "http://registry.npm.qima-inc.com/word-wrap/download/word-wrap-1.2.3.tgz#610636f6b1f703891bd34771ccb17fb93b47079c"
  integrity sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=

"wordwrap@>=0.0.1 <0.1.0", wordwrap@~0.0.2:
  version "0.0.3"
  resolved "http://registry.npm.qima-inc.com/wordwrap/download/wordwrap-0.0.3.tgz#a3d5da6cd5c0bc0008d37234bbaf1bed63059107"
  integrity sha1-o9XabNXAvAAI03I0u68b7WMFkQc=

wrap-ansi@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/wrap-ansi/download/wrap-ansi-2.1.0.tgz#d8fc3d284dd05794fe84973caecdd1cf824fdd85"
  integrity sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU=
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"

wrap-ansi@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/wrap-ansi/download/wrap-ansi-3.0.1.tgz#288a04d87eda5c286e060dfe8f135ce8d007f8ba"
  integrity sha1-KIoE2H7aXChuBg3+jxNc6NAH+Lo=
  dependencies:
    string-width "^2.1.1"
    strip-ansi "^4.0.0"

wrap-ansi@^5.1.0:
  version "5.1.0"
  resolved "http://registry.npm.qima-inc.com/wrap-ansi/download/wrap-ansi-5.1.0.tgz#1fd1f67235d5b6d0fee781056001bfb694c03b09"
  integrity sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=
  dependencies:
    ansi-styles "^3.2.0"
    string-width "^3.0.0"
    strip-ansi "^5.0.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "http://registry.npm.qima-inc.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz#e9393ba07102e6c91a3b221478f0257cd2856e53"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^2.0.0:
  version "2.4.3"
  resolved "http://registry.npm.qima-inc.com/write-file-atomic/download/write-file-atomic-2.4.3.tgz#1fd2e9ae1df3e75b8d8c367443c692d4ca81f481"
  integrity sha1-H9Lprh3z51uNjDZ0Q8aS1MqB9IE=
  dependencies:
    graceful-fs "^4.1.11"
    imurmurhash "^0.1.4"
    signal-exit "^3.0.2"

write@1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/write/download/write-1.0.3.tgz#0800e14523b923a387e415123c865616aae0f5c3"
  integrity sha1-CADhRSO5I6OH5BUSPIZWFqrg9cM=
  dependencies:
    mkdirp "^0.5.1"

x256@>=0.0.1, x256@~0.0.1:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/x256/download/x256-0.0.2.tgz#c9af18876f7a175801d564fe70ad9e8317784934"
  integrity sha1-ya8Yh296F1gB1WT+cK2egxd4STQ=

xdg-basedir@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/xdg-basedir/download/xdg-basedir-3.0.0.tgz#496b2cc109eca8dbacfe2dc72b603c17c5870ad4"
  integrity sha1-SWsswQnsqNus/i3HK2A8F8WHCtQ=

xml2js@^0.4.17, xml2js@^0.4.5:
  version "0.4.23"
  resolved "http://registry.npm.qima-inc.com/xml2js/download/xml2js-0.4.23.tgz#a0c69516752421eb2ac758ee4d4ccf58843eac66"
  integrity sha1-oMaVFnUkIesqx1juTUzPWIQ+rGY=
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xmlbuilder@~11.0.0:
  version "11.0.1"
  resolved "http://registry.npm.qima-inc.com/xmlbuilder/download/xmlbuilder-11.0.1.tgz#be9bae1c8a046e76b31127726347d0ad7002beb3"
  integrity sha1-vpuuHIoEbnazESdyY0fQrXACvrM=

xregexp@^4.2.4:
  version "4.3.0"
  resolved "http://registry.npm.qima-inc.com/xregexp/download/xregexp-4.3.0.tgz#7e92e73d9174a99a59743f67a4ce879a04b5ae50"
  integrity sha1-fpLnPZF0qZpZdD9npM6HmgS1rlA=
  dependencies:
    "@babel/runtime-corejs3" "^7.8.3"

xss@0.3.7:
  version "0.3.7"
  resolved "http://registry.npm.qima-inc.com/xss/download/xss-0.3.7.tgz#1df6dc85c0240b455b5e5f0428bdeccd739ab4ee"
  integrity sha1-HfbchcAkC0VbXl8EKL3szXOatO4=
  dependencies:
    commander "^2.9.0"
    cssfilter "0.0.10"

xtend@^4.0.0:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/xtend/download/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

y18n@^3.2.0:
  version "3.2.1"
  resolved "http://registry.npm.qima-inc.com/y18n/download/y18n-3.2.1.tgz#6d15fba884c08679c0d77e88e7759e811e07fa41"
  integrity sha1-bRX7qITAhnnA136I53WegR4H+kE=

y18n@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/y18n/download/y18n-4.0.0.tgz#95ef94f85ecc81d007c264e190a120f0a3c8566b"
  integrity sha1-le+U+F7MgdAHwmThkKEg8KPIVms=

yallist@^2.1.2:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/yallist/download/yallist-2.1.2.tgz#1c11f9218f076089a47dd512f93c6699a6a81d52"
  integrity sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=

yallist@^3.0.2:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/yallist/download/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yargonaut@^1.1.2:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/yargonaut/download/yargonaut-1.1.4.tgz#c64f56432c7465271221f53f5cc517890c3d6e0c"
  integrity sha1-xk9WQyx0ZScSIfU/XMUXiQw9bgw=
  dependencies:
    chalk "^1.1.1"
    figlet "^1.1.1"
    parent-require "^1.0.0"

yargs-parser@^13.1.2:
  version "13.1.2"
  resolved "http://registry.npm.qima-inc.com/yargs-parser/download/yargs-parser-13.1.2.tgz#130f09702ebaeef2650d54ce6e3e5706f7a4fb38"
  integrity sha1-Ew8JcC667vJlDVTObj5XBvek+zg=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^18.1.2:
  version "18.1.3"
  resolved "http://registry.npm.qima-inc.com/yargs-parser/download/yargs-parser-18.1.3.tgz#be68c4975c6b2abf469236b0c870362fab09a7b0"
  integrity sha1-vmjEl1xrKr9GkjawyHA2L6sJp7A=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs@^13.2.1:
  version "13.3.2"
  resolved "http://registry.npm.qima-inc.com/yargs/download/yargs-13.3.2.tgz#ad7ffefec1aa59565ac915f82dccb38a9c31a2dd"
  integrity sha1-rX/+/sGqWVZayRX4Lcyzipwxot0=
  dependencies:
    cliui "^5.0.0"
    find-up "^3.0.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^3.0.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^13.1.2"

yargs@^15.0.0:
  version "15.4.0"
  resolved "http://registry.npm.qima-inc.com/yargs/download/yargs-15.4.0.tgz#53949fb768309bac1843de9b17b80051e9805ec2"
  integrity sha1-U5Sft2gwm6wYQ96bF7gAUemAXsI=
  dependencies:
    cliui "^6.0.0"
    decamelize "^3.2.0"
    find-up "^4.1.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^4.2.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^18.1.2"

yargs@^3.32.0:
  version "3.32.0"
  resolved "http://registry.npm.qima-inc.com/yargs/download/yargs-3.32.0.tgz#03088e9ebf9e756b69751611d2a5ef591482c995"
  integrity sha1-AwiOnr+edWtpdRYR0qXvWRSCyZU=
  dependencies:
    camelcase "^2.0.1"
    cliui "^3.0.3"
    decamelize "^1.1.1"
    os-locale "^1.4.0"
    string-width "^1.0.1"
    window-size "^0.1.4"
    y18n "^3.2.0"

yarn@^1.22.22:
  version "1.22.22"
  resolved "http://registry.npm.qima-inc.com/yarn/download/yarn-1.22.22.tgz#ac34549e6aa8e7ead463a7407e1c7390f61a6610"
  integrity sha1-rDRUnmqo5+rUY6dAfhxzkPYaZhA=

yn@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/yn/download/yn-2.0.0.tgz#e5adabc8acf408f6385fc76495684c88e6af689a"
  integrity sha1-5a2ryKz0CPY4X8dklWhMiOavaJo=

yup@^0.27.0:
  version "0.27.0"
  resolved "http://registry.npm.qima-inc.com/yup/download/yup-0.27.0.tgz#f8cb198c8e7dd2124beddc2457571329096b06e7"
  integrity sha1-+MsZjI590hJL7dwkV1cTKQlrBuc=
  dependencies:
    "@babel/runtime" "^7.0.0"
    fn-name "~2.0.1"
    lodash "^4.17.11"
    property-expr "^1.5.0"
    synchronous-promise "^2.0.6"
    toposort "^2.0.2"

zan-ajax@2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/zan-ajax/download/zan-ajax-2.1.0.tgz#b4729f7c8f680be1bb42a35ea78866e6952c87e6"
  integrity sha1-tHKffI9oC+G7QqNep4hm5pUsh+Y=
  dependencies:
    axios "0.18.0"
    zan-json-parse "^1.0.0"

zan-jquery@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/zan-jquery/download/zan-jquery-1.0.2.tgz#d9a45088d0d152cfe41729aca70b6682e30ce5cf"
  integrity sha1-2aRQiNDRUs/kFymspwtmguMM5c8=

zan-json-parse@1.0.2, zan-json-parse@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/zan-json-parse/download/zan-json-parse-1.0.2.tgz#457d9017f33c0b4361fe73ebdd5e61ef81818f3c"
  integrity sha1-RX2QF/M8C0Nh/nPr3V5h74GBjzw=
