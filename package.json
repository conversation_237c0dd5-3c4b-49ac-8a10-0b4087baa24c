{"name": "ebiz-fe-platform", "version": "0.0.1", "description": "电商前端内部工具服务", "scripts": {"dev": "ENABLE_CDN=false ast dev --inspect --ts --tsconfig tsconfig.json --env=qa --port=8765", "precommit": "lint-staged", "postinstall": "npm run ts:build", "ts:build": "sh ./bin/ts-build.sh"}, "lint-staged": {"./app/**/*.ts": ["eslint", "git add"]}, "husky": {"hooks": {"pre-commit": "npm run precommit"}}, "repository": {"type": "git", "url": "***********************:ebiz-web/ebiz-fe-platform.git"}, "keywords": [], "author": "kobestyle", "license": "MIT", "dependencies": {"@types/koa": "2.0.48", "@types/koa-router": "7.0.40", "@types/lodash": "4.14.122", "@types/node": "11.9.5", "@youzan/marconi-lark-sdk": "^1.0.3", "@youzan/nsq-client": "1.0.0", "@youzan/skynet-log": "0.0.6", "@youzan/uniform-resource": "2.0.1", "@youzan/utils": "2.3.11", "@youzan/youzan-framework": "3.4.2", "axios": "^0.19.2", "complete-assign": "0.0.2", "date-fns": "^1.29.0", "fast-glob": "3.2.2", "glob": "7.1.6", "kafka-node": "5.0.0", "lodash": "^4.17.10", "mime-types": "^2.1.27", "moment": "^2.29.4", "mysql2": "^2.1.0", "node-schedule": "1.3.2", "nsqjs": "^0.13.0", "qs": "^6.11.0", "reflect-metadata": "^0.1.13", "sequelize": "5.21.5", "typeorm": "^0.2.25", "uuid": "^9.0.0"}, "devDependencies": {"@types/glob": "7.1.1", "@types/mime-types": "^2.1.0", "@types/node-schedule": "1.3.0", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^2.5.0", "@typescript-eslint/parser": "^2.5.0", "astroboy-cli": "0.1.1", "babel-eslint": "^7.2.1", "chalk": "^2.4.2", "eslint": "^6.5.1", "eslint-config-google": "^0.9.1", "eslint-config-prettier": "^2.10.0", "eslint-plugin-lean-imports": "^0.3.3", "eslint-plugin-prettier": "^2.6.2", "husky": "^1.3.1", "lint-staged": "^8.1.0", "prettier": "^1.18.2", "typescript": "^3.3.3333"}}